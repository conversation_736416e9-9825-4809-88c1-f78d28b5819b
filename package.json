{"name": "starter-kit-do<PERSON><PERSON>", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "dev": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "generate": "docusaurus gen-api-docs all", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc"}, "dependencies": {"@docusaurus/core": "3.8.1", "@docusaurus/preset-classic": "3.8.1", "@docusaurus/theme-mermaid": "^3.8.1", "@mdx-js/react": "^3.0.0", "@tailwindcss/cli": "^4.1.11", "clsx": "^2.0.0", "docusaurus-plugin-openapi-docs": "^4.5.1", "docusaurus-theme-openapi-docs": "^4.5.1", "prism-react-renderer": "^2.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-type-animation": "^3.2.0", "tailwindcss": "^4.1.11"}, "devDependencies": {"@docusaurus/module-type-aliases": "3.8.1", "@docusaurus/tsconfig": "3.8.1", "@docusaurus/types": "3.8.1", "typescript": "~5.6.2"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 5 safari version"]}, "engines": {"node": ">=18.0"}}