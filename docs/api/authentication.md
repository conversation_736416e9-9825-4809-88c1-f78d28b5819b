---
sidebar_position: 1
---

# API Authentication

## Authentication Flow

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Auth
    participant DB
    
    Client->>API: Login Request
    API->>Auth: Validate Credentials
    Auth->>DB: Check User
    DB-->>Auth: User Data
    Auth->>Auth: Generate JWT
    Auth-->>API: JWT Token
    API-->>Client: JWT Token
    
    Note right of Client: Store Token
    
    Client->>API: Request with JWT
    API->>Auth: Validate JWT
    Auth-->>API: Token Valid
    API->>DB: Fetch Data
    DB-->>API: Data
    API-->>Client: Response
```

## Authentication Methods

### JWT Authentication

```typescript
// Request
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123"
}

// Response
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 3600
}
```

### OAuth 2.0 Integration

```typescript
// Request
GET /api/auth/oauth/google

// Callback
GET /api/auth/oauth/google/callback?code=4/0AeaYSHDp...

// Response
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 3600
}
```

## Token Management

### Token Structure

```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "sub": "1234567890",
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "admin",
    "permissions": ["read:users", "write:users"],
    "iat": 1516239022,
    "exp": 1516242622
  },
  "signature": "..."
}
```

### Token Refresh

```typescript
// Request
POST /api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}

// Response
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 3600
}
```

## Security Best Practices

1. **Token Storage**
   - Store tokens in HttpOnly cookies
   - For SPAs, use memory storage or secure localStorage

2. **HTTPS Only**
   - All API communications must use HTTPS

3. **Token Expiration**
   - Short-lived access tokens (1 hour)
   - Longer-lived refresh tokens (2 weeks)

4. **CSRF Protection**
   - Include CSRF tokens for cookie-based auth

## Code Examples

### Frontend (React)

```typescript
// Auth service
class AuthService {
  private token: string | null = null;

  async login(email: string, password: string) {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });
    
    const data = await response.json();
    this.token = data.tokens.accessToken;
    localStorage.setItem('token', this.token);
    return data;
  }

  async apiCall(url: string, options: RequestInit = {}) {
    return fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${this.token}`
      }
    });
  }
}
```

### Backend (Node.js)

```javascript
// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};
```

## Next Steps

- [API Status Codes](./statusCode.md)
- [Rate Limiting](./rateLimiting.md)
