---
id: get-servicesetups
title: "Get service setups id"
description: "Get service setups id"
sidebar_label: "Get service setups id"
hide_title: true
hide_table_of_contents: true
api: eJztVj1v2zAQ/SvGTS3ARGlHbR6CJEOLIE4mw8NZPMtMKZIhT0ENgf+9OElWnDYtHKSjJxHk8d3Xo+514ANFZOPdjYYSauIFxWdTUSJuQwIFjHWCcgnj/kL2YaUgYMSGmKKcduCwISjBaFBgHJQQkLegINJTayJpKDm2pCBVW2oQyg54F+RG4mhcDQo2PjbIUELbGg05qwn06vLbYv5wf72H3hJqiu8Az3klxil4lyjJ+deLC/loSlU0QfKHEhZtVVGSpCvvmBz3SPSTi2BRPHd/evDrR6oYFIQopWQz4KcR6sVw7b0ldJAVNJQS1vRWEVxrLa4tDRllBRr5CHdGH1FRBdqkYHH3vS/rEc6f0bbHWSbb1kcZVpGQSc/5nwFrZDpj01BfLq/NxrznTlaAWhtpK9rbg0pt0CZSwIZtT6yJ7j2t70aGfBhg/71Gpy3FAS8Ea6r+qRWPyZ/YdGLTB9jU/5VONDrR6CM0ygL5egReEc/ScHk2SIBZP9Ib4q0fFYI0WoZ7CQUGU4zmZ4N50RmdQYHs7rVBG61MbeaQyqKoqUltqCNqOpd1ZX2rzzEEkClt3Mb3FZ2SaVLA3WxBzJYacjyb396AAkEfYn7+Ig0JPnGD/XvYy4a/5PIq46l3B3M+qyHkbsxzCRjMmNNLpqCgNFqk0NYnFquuW2Oih2hzlu2nluIOyuVKOBvNwLhlJ4SXtZ4a91tAk/iAT3ejwPk8EyX2VqDjJrodTE8DQMEP2g1yLK+y2ium/+598HKgz6YIRL4Np/OqosAHZ4cQqwNqXV3eQ86/ANe1q/0=
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get service setups id"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/service-setups/{id}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get service setups id

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"id","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceSetupResponse"}},"additionalProperties":false,"title":"GetServiceSetupResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceSetupResponse"}},"additionalProperties":false,"title":"GetServiceSetupResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceSetupResponse"}},"additionalProperties":false,"title":"GetServiceSetupResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      