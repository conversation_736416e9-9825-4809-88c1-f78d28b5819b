---
id: get-settlementmodes
title: "Get settlement modes"
description: "Get settlement modes"
sidebar_label: "Get settlement modes"
hide_title: true
hide_table_of_contents: true
api: eJztVU1v2zAM/SsBTxugNd2OvvVQtAVWoGjaU5ADY7GOOllSRTpYYOi/D3S+3I9hPexSoCcLJv34Hp/t10NMlFFcDFcWKmhIZiTiqaUgbbTEYECwYajmcKxcR0uwMJAwY0tCWes9BGwJKrg4v56d3d9dggEXoIIVoaUMBjI9dS6ThUpyRwa4XlGLUPUgm6RPsmQXGihloc2cYmBirf84PdWLJa6zS0oXKph1dU2sDOsYhIIMSPRbpsmjTu5fT4jLR6oFDKSsysVt8XkHdWxcxugJAxQDLTFjQ695Ggid97j0tFVUDFiU8TjMGTe6B6GW/01ju8B3jGHfNe9oLAbQWqf7Qn8zGvWAnsmAOPGDY2Pb1dzb3fJV/v/G/OlY9udLDNZThgE1Je/q4V2cPnL89O+D+Td8eZ/GfTTjigI//7FekEz4ADHZ50BLsoq7lNANoqyggikmNz12f9t3M+X1Phi67DUHRBJX02lDLXepyWjpRM+1j509wZRA//suPMRhwwc5LSfcTI6aJmc3V2BA0bd81991XSmytBiOdv5NxzOxBytHwVHMlnG/0zgHTG6Q9ELlwsAqsmhH3y+R6T77UvT2U0d5A9V8YWCN2W1tnC+K2aehrsU61oI9WPiC2SHW4MvtLjq/TjSQ32L8izbPs3eNvtM2KKbfVc/qmpKMamOIxcjhi/M7KOUPrCXbIw==
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settlement modes"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settlement-modes"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settlement modes

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"name":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true}},"additionalProperties":false,"title":"GetSettlementModeResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementModeResponseListResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"name":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true}},"additionalProperties":false,"title":"GetSettlementModeResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementModeResponseListResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"name":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true}},"additionalProperties":false,"title":"GetSettlementModeResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementModeResponseListResponseHandler"}}}}}}
>
  
</StatusCodes>


      