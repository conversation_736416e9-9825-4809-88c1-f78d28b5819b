---
id: delete-gemspaybankaccount
title: "Delete gemspay bank account id"
description: "Delete gemspay bank account id"
sidebar_label: "Delete gemspay bank account id"
hide_title: true
hide_table_of_contents: true
api: eJztVU1v2zAM/SvBO22A2nQ7+pahQVtgA4p+nIIcGItN1MqSKsnFAkP/fWDspO5WYB1Q7NSTZVF8JB+Jxw4+cKRsvLvQqKDZcuYzblKg7YrcA9W1b12GQqZ1QrXAYPxG7mE2GJcKgSI1nDnKmw6OGkYFo6FgHCoEyhsoRH5sTWSNKseWFVK94YZQdcjbIB4pR+PWULjzsaGMCm1rNEpRB9Cz+Y/r2e3N+R56w6Q5/gN4KUt5nIJ3iZPYv56cyEdzqqMJwgYqXLd1zSlBofYus8s7JP6Zp8GSRO7+jOBX91wLWyEKsdn0+GmAen648t4yORSFhlOiNb9GgmutpZXlvqKioCm/IZzRb2IUpLWRYslejvzvyCZWyCZLYJyOR2LU9auBwPdD2n/PyWnLsQcOwZp6N6DT++Q/WP8vrO+m/IPu/0N3EeyX0tODTNY9ykSEeDIo8WSnqQ3njX8WbCFHBLbClIKZDn5H4nc0+E07owsUEsenvUq30Yp+5hxSNd15tWEdSfOxnGvrW31MIUD00rg7vyN8qHOocHLNOVtu2OXJ7PICCoLeV/H0RToffMoN7SZpEPC/VveCjEOPR9JbVJ97N5S9AAUDhdcKh0JltOyojU9Z3nbdihLfRluKXD+2HLeoFkuFJ4qmH8VFB22SnPWhxb+lddgK+HQ1bJ7PE1mUr6U7XJLbCkVkW/mDwgNv+z1ZlkXtV9m7R++jjBbnIQPZq711Vtcc8sg2hliORu50/n1+M0cpvwBlL+Dm
sidebar_class_name: "delete api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete gemspay bank account id"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/gemspay-bank-account/{id}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete gemspay bank account id

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"id","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteGemspayBankAccountResponse"}},"additionalProperties":false,"title":"DeleteGemspayBankAccountResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteGemspayBankAccountResponse"}},"additionalProperties":false,"title":"DeleteGemspayBankAccountResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteGemspayBankAccountResponse"}},"additionalProperties":false,"title":"DeleteGemspayBankAccountResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      