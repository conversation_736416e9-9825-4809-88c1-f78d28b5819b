---
id: get-settlements-all
title: "Get settlements all"
description: "Get settlements all"
sidebar_label: "Get settlements all"
hide_title: true
hide_table_of_contents: true
api: eJztWktv2zgQ/isBT7uAtu52b745STc10HSFOOkl8GFMjm22EskMqaBew/99QT0s2VFiygj2xFMSeb7RvMnPmS3TBgmc1Goq2Jit0M3QuQxzVM5OsowlzMHKsvEjaz9g84QZIMjRIfnPtkxBjmzMUljhtyJfILGEScXG7KlA2rCEWb7GHNh4y9zGeFGpHK5KuaWmHFz16K9PbLdLDvTN5L/4Ptqu1qAUZieVWUdSrQ6gk1wXyp1Eqsb3vRVCF4sMD3TdEygL3Af9DpdIqPhpB3tsSpGkFucg/yadh+K6voDDP5zMj9zR76bqGjbvpusWia9BuelZEZraqUpJrwitPYlfaJ0hqAMFN59vZ5OH+y8NeI0gysogfCokoWBjRwW+Zc3cC1ujlUXrP//08aP/IdByksaXDxuzWcF5ZSPXyvnm9JrwlxuZDPybty/foBc/kPtiNuTb38lKv61V9TiWsBythRX2JUIVWQa+yEuPdolPSPd1QFTl1WFuA8zYz5mveuWn0hupLwopvHWubanhiLYJQ5zrAK+7mRgGvVoDHUTz9cnRg7sCcy70O2TFGe+9Btcbnr4mPH5rOXND4lNlXqRIL1GdUrRIz5JjSvpZCqRvZcMFqT/AXWkRhoNy9qcgRWDcKsC9rk7MQJDVBXG8BPUz2LAWEh6DEjLh3Ft4JqpyIgSXtzM4pCcb8QEZbUbFbXDM9pCU9FJmODOZdMEhl/YSHF/3FyYUTnduSQEyg8YAoaPNVXkPCbn4JGzhTR023ZoUPCj5VODlcAXWh3MgZB+OzpnbGzxOCA7FxIWPolwLuZTDMK1BdckPObRqSGDJL4b0+2JIp3vhoX3exeggBA9vnJRkDrR57XoxaFL874VQD4shhVBDBs6+y8JKhdYG56wB/qMWGkhI9cqd6eWxdnJaeSEhpD/GIUs7bi4hs5gwJ8vjjd2ga27bs2P37+ob7CBts+NuOkvLS5taNT3xMC13DRuvpiGnYeJLSdalr9yh23og2ZesDM7HOu0g82AbaGkJuEOuSYRCFP462z5D+Cx1Yc/Dv0tBfJVVeEXz4AsokSFV9WZMJnn5Jcnoh9WRU0VOFTlV5FSRU0VOFTlV5FSRU0VOFTlV5FSRUw3gVOV/qCKZimQqkqlIpiKZimQqkqlIpiKZimQqkqlIpiKZGkKmdv4Nh0uBN+gu2j63F1Au8ubo1rre8vVjAdyajdkIjBx1hEeVsL+LNsu9BWV+g9E5Y8ej0QpzW5gVgcAP/nee6UJ8AGOY31iUalkN5r1XuTWwuWhdupikU5Ywr72y9vnPsli1dTmUPKVZoez1QvRyms7G4y6pDN7WHj4yMJJ1Z6XflvTK5glba+u8yHa7AIsPlO12/nG16ul9F9L6XIp9un7i5njR+bmiLIz5/c83APUmc4h4u6ocIr3fTg4RfmX9OMiJZuM4RLheMg4ySQcKVgvCIZIH678hgKON3xYy93+QrDr6cb5Lml3e3vI4Ks/9Ui777a5e/P39wi/V95VtZUhnc/jA7jrVnKPpprqrYt5p8pvP92y3+w9Jobux
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settlements all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settlements/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settlements all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"Channel","in":"query","schema":{"type":"string"}},{"name":"Amount","in":"query","schema":{"type":"number","format":"double"}},{"name":"TransactionReference","in":"query","schema":{"type":"string"}},{"name":"Period","in":"query","schema":{"type":"string"}},{"name":"From","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"To","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"Day","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"MerchantId","in":"query","schema":{"type":"string"}},{"name":"IsInProgress","in":"query","schema":{"type":"boolean"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"settlementLogId":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"transactionReference":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionCharge":{"type":"number","format":"double"},"transactionChargeCap":{"type":"number","format":"double"},"transactionChargeValue":{"type":"number","format":"double"},"transactionDate":{"type":"string","format":"date-time"},"transactionChannel":{"type":"string","nullable":true},"settledPerChannel":{"type":"boolean"},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"amountPaid":{"type":"number","format":"double"},"amountToSettle":{"type":"number","format":"double"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"merchantId":{"type":"string","format":"uuid"},"merchantName":{"type":"string","nullable":true},"settlementMode":{"type":"string","nullable":true},"settlementProfileSplitCode":{"type":"string","nullable":true},"isBatch":{"type":"boolean"},"autoSettlement":{"type":"boolean"},"autoSettlementCharge":{"type":"number","format":"double"},"retryCount":{"type":"integer","format":"int32"},"batchReference":{"type":"string","nullable":true},"merchantUniqueBatchReference":{"type":"string","nullable":true},"splitReference":{"type":"string","nullable":true},"settlementInProgress":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementAccount":{"type":"object","properties":{"settlementAccountId":{"type":"string","format":"uuid"},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"code":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"merchantId":{"type":"string","format":"uuid"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementProfile":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetSettlementAccountResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetMerchantSettlementResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"settlementLogId":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"transactionReference":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionCharge":{"type":"number","format":"double"},"transactionChargeCap":{"type":"number","format":"double"},"transactionChargeValue":{"type":"number","format":"double"},"transactionDate":{"type":"string","format":"date-time"},"transactionChannel":{"type":"string","nullable":true},"settledPerChannel":{"type":"boolean"},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"amountPaid":{"type":"number","format":"double"},"amountToSettle":{"type":"number","format":"double"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"merchantId":{"type":"string","format":"uuid"},"merchantName":{"type":"string","nullable":true},"settlementMode":{"type":"string","nullable":true},"settlementProfileSplitCode":{"type":"string","nullable":true},"isBatch":{"type":"boolean"},"autoSettlement":{"type":"boolean"},"autoSettlementCharge":{"type":"number","format":"double"},"retryCount":{"type":"integer","format":"int32"},"batchReference":{"type":"string","nullable":true},"merchantUniqueBatchReference":{"type":"string","nullable":true},"splitReference":{"type":"string","nullable":true},"settlementInProgress":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementAccount":{"type":"object","properties":{"settlementAccountId":{"type":"string","format":"uuid"},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"code":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"merchantId":{"type":"string","format":"uuid"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementProfile":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetSettlementAccountResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetMerchantSettlementResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"settlementLogId":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"transactionReference":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionCharge":{"type":"number","format":"double"},"transactionChargeCap":{"type":"number","format":"double"},"transactionChargeValue":{"type":"number","format":"double"},"transactionDate":{"type":"string","format":"date-time"},"transactionChannel":{"type":"string","nullable":true},"settledPerChannel":{"type":"boolean"},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"amountPaid":{"type":"number","format":"double"},"amountToSettle":{"type":"number","format":"double"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"merchantId":{"type":"string","format":"uuid"},"merchantName":{"type":"string","nullable":true},"settlementMode":{"type":"string","nullable":true},"settlementProfileSplitCode":{"type":"string","nullable":true},"isBatch":{"type":"boolean"},"autoSettlement":{"type":"boolean"},"autoSettlementCharge":{"type":"number","format":"double"},"retryCount":{"type":"integer","format":"int32"},"batchReference":{"type":"string","nullable":true},"merchantUniqueBatchReference":{"type":"string","nullable":true},"splitReference":{"type":"string","nullable":true},"settlementInProgress":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementAccount":{"type":"object","properties":{"settlementAccountId":{"type":"string","format":"uuid"},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"code":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"merchantId":{"type":"string","format":"uuid"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementProfile":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetSettlementAccountResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetMerchantSettlementResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      