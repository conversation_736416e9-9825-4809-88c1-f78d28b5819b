---
id: create-connector-balanceinquiry
title: "Create connector connector balance inquiry"
description: "Create connector connector balance inquiry"
sidebar_label: "Create connector connector balance inquiry"
hide_title: true
hide_table_of_contents: true
api: eJztV8tu2zAQ/BVjT30wcdujbkkQJD60NeL0ZBjFmtrYTCiSISm3hqB/L1ZUZKXOQe4DyMG+mKKXw+XsiN6pwDryGJU1kxwykJ4w0oU1hmS0/hw1GknKPJbKb0FAxFWAbA5dBCwEOPRYUCTPP1VgsCCG6kIEKAMZOIxrEOCJwSiHLPqSBAS5pgIhqyBuHS8M0SuzgroWHdbV5efZ2bfb6yeoNWFO/iCwRQqmEM9tvuUIaU0kE3mIzmklGxrG98EantuDsst7khEEOM+kRUWhWSulLU38UhZL8vs7CzCl1rjUlHKsRbeiOduA+OXGDItD83Bh8yGgnEWeKz4w6mnvPHeoAwmIKnIstAKYJAHcJAK5NBDpZzxyNYyrvrzevT+SNoA0BvAUnDUhsfDpwwf+yilIrxzjQgazUkoKAUT/ZW6U6TSqg1gOLdQucGmtJjRQM/qgQwsoKARcDYvNMQ7Ji0Job+cBmNGjCSjj8BWHauUP1LhB1cy0tf6P6kl64YoF8hslaertRuUDE42qoFnEwr0UfWd9gREyLhudcCj8ZZpfJtPvT+NrNLkmv3dbHHpXHFV8VPHrUPEf9QdH+R7l+xrk27Qfz1uNi8YZjTpX0xstE/JoZ5MKimvLfsrZ0KiJrU8GY3Rq3K0bV92wHrcYJzsMJu/JUpVes+uJ0YVsPF5REUq38pjTKY+ltmV+is4Buxx+5252TufyJxZO0wuN5I7WZ/LbTTdtYe+x6/46V8V27M42lWpZvqIiONyOZhSjpoJMHJ1NJyCAz5KY3HzkhcxMgc3t0Jq8gxh+VpxOKL3WrxaJtaolfw7oVGoUO1ua9R9+r8BCwJqrl82hqpYY6JvXdc3TjyX5LWTzhYANepXkO68gV4HHeSe935LselR4c9P61rcjNtUvJd9OouHTblCX/AQCHmj7zFzXi1o8+eF/nkTarOe+u0TYnKdfLxLgyS0D7CL2upjdijMpycVebH/TRe/tmX6d3XJlWsNeJPF5/MHOH38kQmxztOaabuYq0GhWZfMHAGln/vwCVDb1fA==
sidebar_class_name: "post api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create connector connector balance inquiry"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/connector/{connector}/balance-inquiry"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create connector connector balance inquiry

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"connector","in":"path","required":true,"schema":{"type":"string"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"accountName":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"BalanceInquiryRequest"}},"text/json":{"schema":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"accountName":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"BalanceInquiryRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"accountName":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"BalanceInquiryRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"availableBalance":{"type":"string","nullable":true}},"additionalProperties":false,"title":"BalanceInquiryResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"BalanceInquiryResponseNIP_ResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"availableBalance":{"type":"string","nullable":true}},"additionalProperties":false,"title":"BalanceInquiryResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"BalanceInquiryResponseNIP_ResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"availableBalance":{"type":"string","nullable":true}},"additionalProperties":false,"title":"BalanceInquiryResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"BalanceInquiryResponseNIP_ResponseHandler"}}}}}}
>
  
</StatusCodes>


      