---
id: get-healthcheck
title: "Get health check"
description: "Get health check"
sidebar_label: "Get health check"
hide_title: true
hide_table_of_contents: true
api: eJxtkk9r3DAQxb+KeacW1GzSo26hhE0OgZJNTosPE3tii9iSIo2XLkbfvYx3HdimJ4n5x++9mRkhciJxwT+0sOhY7pkG6Zuem3cYCHUZdo9T9NcSrQ0iJRpZOGlyhqeRYbG9e9zdvjzfw8B5WPRMLScYJP6YXOIWVtLEBrnpeSTYGXKM2pklOd+hlFqLcww+c9b8z+trfVrOTXJROWGxm5qGc0Yp5p/MlqXqF9RqVTCy9OGsDUouPSw2p6ofa1XmdFjVTGlQeJGY7WbT8Zin2CVq+Ur/zRCm9opihMI6/xYWHU6GxQIec6RjtWORgUf2Ut3+foCBTj8xHm5QDGLIMpLX3tW9r+wX4j7NEv4jmziQ8zpooZ3Puva40FUb9CGLxuf5lTK/pKEUDX9MnI6w+9rgQMnRq9Lv62LWpakRrcuaaGHfaMj8hacJXtgLLL49nTf8vdKj+R/nOx8vT+RAw6Rli4+fW9rePaOUv9Ur390=
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get health check"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/health-check"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get health check

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      