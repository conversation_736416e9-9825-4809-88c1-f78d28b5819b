---
id: delete-batchperiod-remove
title: "Delete BatchPeriod Remove batchPeriodId"
description: "Delete BatchPeriod Remove batchPeriodId"
sidebar_label: "Delete BatchPeriod Remove batchPeriodId"
hide_title: true
hide_table_of_contents: true
api: eJztVctu2zAQ/BVjTi1ARGmPuqWokQRogSCPk+HDWtzYTCmRIVdGDYH/XlCSHTnJIQXSnnISxSVnd4eLmQ7OcyAxrrnUKKHZsvA3kmrjORinr7l2W4aC0DqiXKCPXfUxLBU8BapZOORgh4ZqRonV06FLDQXToIQn2UAh8GNrAmuUElpWiNWGa0LZQXY+X44STLOGwr0LNQlKtK3RSEkd8M/nP2/O7m4v9tAbJs3hL8BTWubD0bsmcszxr6en+aM5VsH4TAhK3LRVxTFCoXKNcCM9Ev+WwlvKmbuXGdzqgSuBgg+ZWzEDfhyhng6unLNMDZJCzTHSml8joWmtpZXloaOkoEnekO74Bd5CLkhrk/smezWBuicbWUGM5BowzMNkCK5HEt8BYv+9oEZbDgOi99ZU/YAWD9F9UP7vKe/n+4Pr/8B1yqDHivO9F+DZ5PpsAJw919SaZeOeNDtTlAW2REHeFBOAYgAouiOEBIXIYbtX7jbYLKQiPpZFseY6tn4dSPNJXlfWtfqEvEcWTtPcu570seVzrqOn3eyGRSzX3Mjs7OoSChl96Gv7JQ+Cd1Fq6gdrVPK393vE0+HBJ2Kc1NBENxKxAHkDdeRYCgdDK48TLBU2Lkq+1nUrinwXbEp5+7HlsEO5WCpsKZhhWhcdtIl5rQ8T8KzCg2Xg0/VoS59n2Ulfq3zcpGaXaSPb5j8o/OLdCz9Ny6T2lvfuhQwJJwZ7KCb77xA9qyr2MolNIZaT0fw+/zG/nSOlP3I+7+4=
sidebar_class_name: "delete api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete BatchPeriod Remove batchPeriodId"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/BatchPeriod/Remove/{batchPeriodId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete BatchPeriod Remove batchPeriodId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"batchPeriodId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"RemoveBatchPeriodResponse"}},"additionalProperties":false,"title":"RemoveBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"RemoveBatchPeriodResponse"}},"additionalProperties":false,"title":"RemoveBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"RemoveBatchPeriodResponse"}},"additionalProperties":false,"title":"RemoveBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      