---
id: create-chargerevenues-logsettledrevenue
title: "Create charge revenues log settled revenue"
description: "Create charge revenues log settled revenue"
sidebar_label: "Create charge revenues log settled revenue"
hide_title: true
hide_table_of_contents: true
api: eJztWE2P0zAQ/SvVnEDyUj5uuZUVYleCZdWWU9XDNJ6mBsc29riiRPnvyEn6uQXa5ZpTInvmeebN+KWdCqwjj6ysuZeQQe4JmW5X6AvytCYTKXyyRSBmTbJbAQGMRYBsBq3luFufC3DosSQmn7YrMFgSZPDxw+fJ6Ov0DgQoAxmsCCV5EODpR1SeJGTsIwkI+YpKhKwC3rjkGdgrU0Bdz5NxcNYECmn/7evX6SEp5F65lABkMIl5TiGAgNwaJsMNEv3kodOYTq6enmAX3yhnEOB84oJVix86qL3hwlpNaKAWUFIIWNDTOAWYqDUuNLUZ1QIk8uFx6D1uEg9MZfh3GEqeO2RpfYkMGcSoZAoISxu7bFtTE8tFw/DOVNqYwqoFtNUsyfDERp/TmJbkyeSX5XPqPfVoAuapArcnQSjDVBxHoQy/e3scxLQxroBMLFNP3T9MpqOHKQh4P5re3qWuOg2KFaeIYHIEEva48v4y3py3qcokz9e521amGBP7zTX5tTdJjvivcUhkumFVNnUprVRLdY1PqryUKnGP+vGgcZaoA+15+kh8dFPH3U1Kpz4psMOCHtr2uSzV5DBRv+hC86XygR//cH32FfLqXPdpfL4vW0adnMOFkTYOY8qtl5e6GPr57Picp7WyMTzP/3974ZNqmZXbhTs0UpNvu8w5rfLmOzH8FmyvpL2S9kraK2mvpFcrafNrtJfQXkJ7Ce0ltJfQ6yW0TuDHf/tvm/Ye5A3SYDu3GGhbDLobONiPLkrilU2zDmdDI3HIK8hgiE4NW4SbLcJQ2+KmQ7jZIwTy6+2AI3qd5hnMLmTDYUFliK7wKOlVes+1jfIVOgdpfqHM0jZU7pIvg8PNYC8cg9HjPQhI6G1m6zdNT9vAJTZfjG6gclXGR2TtKnkwFqlFm0fVkTEDdKmoJ3SAgHOEzAWsEpXZDKpqgYG+el3XaflHJL+BbDYXsEav2v6YzWuxHQAlBqUKaUPueuMk3N0kB16Mu2nRywGI82l8p83xuGmNOiYzqEXV7Y7ynBwf7B1CzA8a5PHLZAp1/Rs336GN
sidebar_class_name: "post api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create charge revenues log settled revenue"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/charge-revenues/log-settled-revenue"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create charge revenues log settled revenue

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"amount":{"type":"number","format":"double"},"settlementSourceReference":{"type":"string","nullable":true},"settlementSourceTransactionCount":{"type":"integer","format":"int32"},"settlementType":{"enum":["INSTANT","BATCH"],"type":"string","title":"SettlementTypes"},"settledId":{"type":"string","format":"uuid"},"processed":{"type":"boolean"},"processingRetryCount":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChargeRevenueResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetChargeRevenueResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"amount":{"type":"number","format":"double"},"settlementSourceReference":{"type":"string","nullable":true},"settlementSourceTransactionCount":{"type":"integer","format":"int32"},"settlementType":{"enum":["INSTANT","BATCH"],"type":"string","title":"SettlementTypes"},"settledId":{"type":"string","format":"uuid"},"processed":{"type":"boolean"},"processingRetryCount":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChargeRevenueResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetChargeRevenueResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"amount":{"type":"number","format":"double"},"settlementSourceReference":{"type":"string","nullable":true},"settlementSourceTransactionCount":{"type":"integer","format":"int32"},"settlementType":{"enum":["INSTANT","BATCH"],"type":"string","title":"SettlementTypes"},"settledId":{"type":"string","format":"uuid"},"processed":{"type":"boolean"},"processingRetryCount":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChargeRevenueResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetChargeRevenueResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      