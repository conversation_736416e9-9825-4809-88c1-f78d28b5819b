---
id: get-gemspaybankaccount-all
title: "Get gemspay bank account all"
description: "Get gemspay bank account all"
sidebar_label: "Get gemspay bank account all"
hide_title: true
hide_table_of_contents: true
api: eJztW1Fv2zgM/isBn3aAu+y2t7xlRdEV6HpFsz0VeWAsxtEiS5pEB8sC//dBtpPGbdpTezugwfTU1uZHURRJUx/KDRhLDlkafSFgBAXxOZXe4nqGeol5birNY6UgA8bCw+gWuvcfUS/H7XuYZmDRYUlMLshsQGNJMIJrLOiqKmfkIAOpYQTfK3JryMDnCyoRRhvgtQ2iUjMVjdzcuBK5ffThPdR11tM3kT/p92g7P/s8GX/98mmrbUEoGoyj75V0JGDErqID6j07qQuo62kQ9tZoTz68f//uXfghyOdO2uBWGMGkynPyHjLIjWbS3GiiHzy0CsPKm4crmNk3yhkysC6cEMtWv+9U3QnOjFGEGuoMSvIeC3poZwa6Ugpnitod1RkI5P3l0DkMjpRMpf93M6Q4tMjO1VUlRTCoi5+rxt0RRm3l24iJQYQoPTUiTn0QjrZluc4vaUUqTvNKR8mVqAUy3dCcHOmcnrHTViBC0FbOGh8n66hEt4xTi+uSNJ8uUGtS0T7vw6K934dNVFVEwhxrcs9YppF/xm4a+YuD4X8gnFmu6HCuKvT82Qg5lyQ+rqPU9SFnJcq44MwdIZMY85M5G+LyhGVJTSXplnkOJl+gLujSFP5/ryvW0UXZr3SpUKVCdUSF6k8qDWG7QsjQDKG63kvPOSpPGbBk1bRju95zr7f8R6v1TddhNalvPKfcT7mfcv/Py32pJUtkE9/f7RDxbqlsMP/KcGw1aC95x+fw+94+3fZv+y6/t4n/dJxP6A25VDwsNI/f4ltAQwXEic+l83z9yO347pvg5GMp9lIsG0YVwD7S0gZwQ7lxIhai6ceL7bOOVtJU/mX43xIQl7J1r9g++IRaKHJtNFurZN4wVMNv3iS2JDUiqRFJbMlxt0WJLUmFKhWqdGNKbEnK/ZT7KfcTW/LaHZ7YksSWHAFb0vxXSaJJUgeSOpBEkxx9P5RoklSoUqFKV6VEk6TcT7mfcj/RJK/d4YkmSTTJq6dJ6rBCf0TnnHhQtLoG4WM56D7IA2xmn0rihenmo0JLgLyAEQzRymGHOgmokw41bFGe3Go7EFU5FSaMmK0fDRtQZQuHgt6G33NlKvEWrYUwUST13DRe66fVYELMisLXYTC+voAMgvbW/tXf2z6mxKZwbEecnt5Xzwm7Y9obTQq1yzU1rt3zLaANJ3Zo15BB0DrNYGE8B9nNZoaevjpV1+FxO60VvCGkD+ctdke6pPX9cbEVqipYA2Fi6wlANw92Jz4NfzjZBtTttM62g10HV77ngt2EFry56abA/hpAdtg1rRF7Y2Q9m9u34zwny3vv9lVM90Lr/OwL1PUvk59taQ==
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get gemspay bank account all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/gemspay-bank-account/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get gemspay bank account all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"partnerName":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"partnerId":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"changeLogs":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"preImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"postImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true},"updateNote":{"type":"string","nullable":true},"action":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GemspayBankAccountChangeLogResponse"},"nullable":true}},"additionalProperties":false,"title":"GetGemspayBankAccountResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetGemspayBankAccountResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"partnerName":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"partnerId":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"changeLogs":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"preImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"postImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true},"updateNote":{"type":"string","nullable":true},"action":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GemspayBankAccountChangeLogResponse"},"nullable":true}},"additionalProperties":false,"title":"GetGemspayBankAccountResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetGemspayBankAccountResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"partnerName":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"partnerId":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"changeLogs":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"preImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"postImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true},"updateNote":{"type":"string","nullable":true},"action":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GemspayBankAccountChangeLogResponse"},"nullable":true}},"additionalProperties":false,"title":"GetGemspayBankAccountResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetGemspayBankAccountResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      