---
id: get-settled
title: "Get settled settledId"
description: "Get settled settledId"
sidebar_label: "Get settled settledId"
hide_title: true
hide_table_of_contents: true
api: eJytUstu2zAQ/BVjTi1ARGmPvOUQuD4UCOLkZOiwkdYyUYliyJVRQ+C/FytLatLHoUBPonaXw5nZGdEHjiSu97saFg3LnkVarmEg1CTYA5ZKaRAoUsfCURsjPHUMi3Qd2Okl52ERSE4wiPw6uMg1rMSBDVJ14o5gR8glTBclOt/A4NjHjgQWw+Bq5GxW7O391/3d89OXBfrEVHP8B/CcSx1OofeJk/Y/397qp+ZURRdUOyz2Q1VxSvr2L50ty2ZWuHmrtGM59bNpMFfNFgUFV8xjxbjOZxgkjufFuSG2KkYkJFsUDXdpCE2kmm/0XLX9UN9QCFDyzh/7SZeTdrKEuxTosrnupWMvm7uHHQwU/cr5/AnZIPRJOvJ6d3HzL1reKV4dFP4uRWjJeUWbKI+zzgMouEnTkhb7E680OPVJdGocXyjxc2xz1vLrwPECeygNzhQdvaigw4jaJT3XsEdqE/9GqOq9sNeAfHic9/5xoxH9E9G5SP6inlA76B8MvvHlXVhzmc2Sp/9O4vrYm/SuRHSla3a290/I+QcoATDo
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settled settledId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settled/{settledId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settled settledId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settledId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      