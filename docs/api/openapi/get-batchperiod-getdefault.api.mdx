---
id: get-batchperiod-getdefault
title: "Get Batch<PERSON>eriod GetDefault"
description: "Get Batch<PERSON>eriod GetDefault"
sidebar_label: "Get Batch<PERSON>eriod GetDefault"
hide_title: true
hide_table_of_contents: true
api: eJztV8lu2zAQ/RVjTi3Axml6081pAyeHtkacnAwfxuJYZsot5MioIejfC8qbvCB106tOEjQL3yx80KvAeQrIytkHCRkUxLfI+cJTUE4OiSXNsdQMAhiLCNkEGvuoscNUgMeAhphCMlZg0RBkMLz7Ph48P92DAGUhgwWhpAACAr2WKpCEjENJAmK+IIOQVcArnyIjB2ULqOtpco7e2Ugx2W+ur9NDUsyD8gkxZDAu85xiBAG5s0yWm0z0m/teYzq5Oj3BzV4oTxX5kIpntc4fN6n2jjPnNKGFWoChGLGgU5wCbKk1zjStK6oFSOQLjpvt25g6f5p37oJBhgzKUsmEoRXx1ckLsWzGd7aoeXDmSZnLMrG72HW9PPeuDC13ZZmKZgV2hSnLX24SkDwQMskBv9kHiUyfOIFIA3FSzdX7Ym5XF5Wxd78zqPRFMZGYNRmyPApurjS19wlDwFW6EUwm/n1DDIV8gZZvy6gsxfgDL+z/NvCnnTkMUtni/IadBGLJbryr4NzSJCcpVbp9qEctuHPUkQSwYt3cf+LxcSseN7c5jeLo6H9I26KffcL/i98+79FKTWGdznut8oYY+y/RdUzSMUnHJB2TvINJmr+RjkI6CukopKOQ91BIXTd3py17hsS9VmxvSPxtJ9QM8cJtxFyaI/ICMuijV/1WTP8gJlJYbiVcGXRSbMw+Zv1+QSaWvggo6Sq959qV8gq9h6TQlJ27ZjS7mkz0uOrt+90bjB5AQMq+xr78nHruXWSDDSduJeNbNR2Uv9uEltCrxRp3tal3AugViAO1KqCVcypg4SInz6qaYaTnoOs6fX4tKawgm0wFLDGo9WZMprXYqtjUJKliMsjdVI8Q7uQofHjcSN6PvaSizyH/RatDzbxEXSY3qEW1sQ7ynDy3bO0U09bUh3dPUNd/AFNgkRc=
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get BatchPeriod GetDefault"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/BatchPeriod/GetDefault"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get BatchPeriod GetDefault

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      