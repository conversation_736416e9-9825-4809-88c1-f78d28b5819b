---
id: update-tunnel-updatechannel
title: "Update tunnel settlementTunnelId update channel channelSettlementId"
description: "Update tunnel settlementTunnelId update channel channelSettlementId"
sidebar_label: "Update tunnel settlementTunnelId update channel channelSettlementId"
hide_title: true
hide_table_of_contents: true
api: eJztV0uP2zgM/isBT+2upunu0bd0WrRzWGAwj9MgB8ZiEnVlSZXoaQPD/72g5STOxCiSYtpLJxfbCv3x9dEkG/CBIrLx7kpDAXXQyHRXO0f2vrsv1ygPoIBxlaB4gFtitlSR4ywHcwUBI1bEFEWiAYcVQQHpieSVBgXGQQEBeQ0KIn2pTSQNBceaFKRyTRVC0QBvQofA0bgVKFj6WCGLhbXR0LZqp6Q3cG/VL9Hy8cN/t7P7u09b6DWhpngGeNvOszAlfuf1RiRK75gcyy2GYE3ZJWL6OXknZ0dQfvGZSgYFIUra2FDqYPoUjTjkamtxYSlb13aJYkfx0ms6Sd6kWcnmcSi88N4SOgkPoNZGbEZ7PTBpiTaRAjYsSJCJdPk0T5kTNzkkHRrTN/5zvR9S4K+//9AwCF6kFLxL2a9/376Vi6ZURhNEDRRwW5clpQRqWEIde4JFc1bcUg814pqCilLC1WlB0sgnqDP6hO+O+sXZfE9LrC2P+/yjZCsoIyGTnvEP3ZBUX7CpqAui12ZpznnnVE59JD4i1E1PnudB2V4/odOW4lGdnlulL2x7Ydt5bPupnvhCsxeanUOzru0ettjcryfc9efJ8SQ/yavCpE/qZHwMr4jXXudRvJRZvBvJC5hiMNOMPW2OwdtpRr/oUafNCHwLChLFx+3SUUcrgzlzSMV0uqIq1WEVUdMbuS+tr/UbDAFkEJeCutkP4x++YRUsHcxR+2wecHF/vOfUloNu6Ts27HJQpYCbyd7oyez6ChSIyTnKj/8ImYJPXGFX4f268TzRP8jojqWDOalVOWpNn5cHwGBk1av7na8YXeEOkyNiY/rnCtY+saA2zQIT3UfbtnL8paa4geJhruARo8mF/NCANknu9Y7NTxzYDXvw6qZfu15PxNwxx/pDdBuJONpankDB/7QZX01l2/uNFoyFrJ23artaPntAstrBIrszSTzP/15mwIs7AdhLHI08+zdmZUmBB7JDpfPBF+B6dncpWhf98lvlcor4VbZo/Jpj4zvfuq7SnTVg0a3qrl9BVi2/7wqh4Rw=
sidebar_class_name: "patch api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update tunnel settlementTunnelId update channel channelSettlementId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/tunnel/{settlementTunnelId}/update-channel/{channelSettlementId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update tunnel settlementTunnelId update channel channelSettlementId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementTunnelId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"channelSettlementId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isActive":{"type":"boolean"}},"additionalProperties":false,"title":"UpdateChannelSettlementTunnelRequest"}},"text/json":{"schema":{"type":"object","properties":{"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isActive":{"type":"boolean"}},"additionalProperties":false,"title":"UpdateChannelSettlementTunnelRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isActive":{"type":"boolean"}},"additionalProperties":false,"title":"UpdateChannelSettlementTunnelRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"}},"additionalProperties":false,"title":"GetChannelSettlementResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"}},"additionalProperties":false,"title":"GetChannelSettlementResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"}},"additionalProperties":false,"title":"GetChannelSettlementResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      