---
id: delete-settlementsplitconfigaccount
title: "Delete SettlementSplitConfigAccount settlementSplitAccountId"
description: "Delete SettlementSplitConfigAccount settlementSplitAccountId"
sidebar_label: "Delete SettlementSplitConfigAccount settlementSplitAccountId"
hide_title: true
hide_table_of_contents: true
api: eJytk0FvEzEQhf9K9E4gWd3C0beIRhAJJETaU5TDdD1JLHZt1x5HRCv/d+TNNm2BRkJwWq/9/Gbmm/EAHziSWO+WBhqGOxZesUjHPTtJobPSere1O2pbn51AQWiXoNd4kq2q7MMom0+yjUKgSD0Lx6oe4KhnaKSXtyb90kDBOmgEkj0UIj9kG9lAS8yskNo99wQ9QI5h9JFo3Q4KWx97EmjkbA1KUedQHxdfVvO720+P1nsmw/EvzEvZVHEK3iVO9fz99XX9GE5ttKFyg8Yqty2nVGP/cnIz8pxdIjW7AKRn2funvkCd6Gg0FGxzybUZXrMtUEgcD49tybGraERC0k2z4z7lsItk+Kqu285nc0UhoKKwbutHSla6ETD3KdDxWX2z+dclFKr7icDhHYpC8El6cvXu1Jt/JPMC87ltwj+kCR1ZV4OOlQ0TsjUoWKjLQ6ugX425Udj7JNVpGO4p8V3sSqnbD5njEXq9UThQtHRf2awHGJvq2kBvqUv8W9Ktd8KuTu6bb9NAvp3V9/WnYqZNcseKl7pc/6DwnY+XHlXZFPU49/89p1PsZ6/snFcdlvPw3iw+L24XKOUn06GOVw==
sidebar_class_name: "delete api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete SettlementSplitConfigAccount settlementSplitAccountId"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/SettlementSplitConfigAccount/{settlementSplitAccountId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete SettlementSplitConfigAccount settlementSplitAccountId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementSplitAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      