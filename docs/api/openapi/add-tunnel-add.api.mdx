---
id: add-tunnel-add
title: "Add tunnel add"
description: "Add tunnel add"
sidebar_label: "Add tunnel add"
hide_title: true
hide_table_of_contents: true
api: eJztWMGO2zYQ/RVjTm3KxEmPujnbYJNDkMXu5mT4MBbHNlOKZMiRE8PQvxcjai157bbepEULRL7IFofD4ePjo/n24ANFZOPdOw0FoNb3tXNkZ1qDAsZ1gmIOd8RsqSLHuRUWCgJGrIgpSsQeHFYEBVy/eX83+3j/FhQYBwVsCDVFUBDpc20iaSg41qQglRuqEIo98C5Iz8TRuDU0zSIHU+LXXu8kovSOybF8xRCsKduCp5+Sd/LuJJVffqKSQUGIMj02lKQ1l/h4QAWuthaXlnJpjQJNqYwmyCAXxSdbry8KxJLNdljD0ntL6KBRUG5QoE2DVowRd4IkU5X+fn5dhosqCRjZUbzy+hJIpHStjQCC9mYw6AptIgVsWGJhpvVVLuIxY27zispEvz33nyZtFDB95ZERIyN6Rgyl4tkvIzVGanTUkCyRUvAuZSx+fflSHkcrCXd1WVJKoIbHT6sywaJ5EpdSl+rsUlaUEq4v5BryBcMZfS7ZyscKGQqoa6NbaP/fDI+ETHrGfzkXjUzP2VTUIum1WZmn9fn+jXQh2v/eflNg0m+0wtryeShNmv3XQF+6b6+JTzThttuq3yEH18SncvCQ9R9I8vB8i05biifHz1MPn1EwRsEYBeMHE4xvusGMSjEqxagUP5RStPeX47vKTOsJt90m2Bp3FfHGi6EXfGp5hbyBAqYYzDQHTnNgorh9sPDqaMWxYw6pmE7XVKU6rCNqeiHfS+tr/QJDAHHoRKdue5fuzVesgqX+4tyv2XGlh9d5J/e/HzZsdgf7vTMfXFX76CMaH6zDhTDSrXxLnQO0VQq4m/T4TmY370CBTDvXtH0l6y9IVegGUzhB9bxaDe6Ejcog7jvA54DBiI+aXdOWDeKdbmRVijns90tM9DHappHXn2uKOyjmCwVbjCazcS6z6mxUQUObJA36QKJHVR1uq/DTbee5/jyRGs5V+zvtjk3bLdpawqBR+671Kid8fi8J+oiT/7d9j1lZUuBB7HDQxYCdNx/u7kHBsvN5q7ycEb+IYYxfpA4Fvp1aK4jtuz1YdOu6PdggjyyfPwANgv0H
sidebar_class_name: "post api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Add tunnel add"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/tunnel/add"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Add tunnel add

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"channels":{"type":"array","items":{"type":"object","properties":{"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddChannelSettlementTunnelRequest"},"nullable":true}},"additionalProperties":false,"title":"AddSettlementTunnelRequest"}},"text/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"channels":{"type":"array","items":{"type":"object","properties":{"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddChannelSettlementTunnelRequest"},"nullable":true}},"additionalProperties":false,"title":"AddSettlementTunnelRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"channels":{"type":"array","items":{"type":"object","properties":{"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddChannelSettlementTunnelRequest"},"nullable":true}},"additionalProperties":false,"title":"AddSettlementTunnelRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      