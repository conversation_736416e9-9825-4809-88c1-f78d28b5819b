---
id: update-settlementprofile-merchant-profile
title: "Update SettlementProfile merchant merchantId profile profileId"
description: "Update SettlementProfile merchant merchantId profile profileId"
sidebar_label: "Update SettlementProfile merchant merchantId profile profileId"
hide_title: true
hide_table_of_contents: true
api: eJztWEuT2zYM/isenPqgo2Q367S6OTuZZA9pPfs4eXyARchmIpEMSW3i0ei/dyjq5Udar5t2MhP7YokCP4LABxJACUqTQSeUvOEQQ6E5Oroj5zLKSTptVCoyek8mWaN0s/AKDByuLMRz6EXbbwsGGg3m5Mh4kRIk5gQx5A3IDQcGQkIMGt0aGBj6VAhDHGJnCmJgkzXlCHEJbqP9TOuMkCtgkCqTo/N6FoJDVbEOvFH0P8F+++b93fTh/l0LvSbkZJ4AXlWLIEzWvVZ84yUSJR1J5x9R60wktROiD1ZJP7YHpZYfKHHA/E41GSfI+q+tUV8XVkiy9o9a5f3dySLLcJlRULVigIVTvfMGU5ZKZYQSKgbCTnku5G3Q/LCM7TCmSaKKLaiv6LxE+fFa8eP09MJHb8oLN2qcNEcdNUPYmRE5ms0/WWTWkfIYvgFyLjwJMJsN7JViZomBE86rAFPO73ZN3jroKSAPdaS3gb0XxkNIR1/cmZhnYn53xBwenL/8embomaHfF0M9qCGrlbTBfRfPn/s/TjYxQvu1IIa7IknIWmDDnKA+c3WG4kmUtg3UQePmZC2ujvMsR3fMcie5i50eee3EP+VSoeFCrg6veELIHuv/t+T+xvPB198Wrf1/h5JnZPZOvqeee2eS/CAkOSlvO7Pjh2BHfTdt30PhWhvtgYxak436Cn7U1NujYd2dk1srHmrvxBffdQ0eQ4RaRHuwUYsWlT1uFTWAUdkhV+ATA/PYthMKk/ki3Dlt4yhaUW4LvTLI6Zl/TjJV8GeoNfii27P/ti+833zBXGf09eyv9/+uu0OFv5vWNXX/oUSuT9V60D4j2x7bSrwOf1LDD4M0aleDQRjBZYq/XaWTl+OrVy9ejV9eTS7Gy8s0GV8kv08u08kEU5zURBMyDflbx6ncatwMuDCazm6AgXdCYMvjCx+GWlmXY33ANM2Sf82iLVJ2QTbIhyoWKFA2/JoDagHsQCuqj3ZgEG/1n3QnEveLLxislXfrHMpyiZYeTFZVfvhTQd7Y8wWDRzQiHAzzEriw/pl3UbmjfZfRwU+3TbPo55Fvnx3aVTOIcuMtjVnh34DBR9pst898b+p/XLk3ULWoWNv++ubbD4sNmm2dIn6/4et1ABzfe4BeYi8d6mdMk4S0G8gOF10MDq3Z9P7ar7psGnR5iF2Dn32nDz8Hi6h6b/XVVY+VkKFcFfWlCGFp//sLwNKUpQ==
sidebar_class_name: "patch api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update SettlementProfile merchant merchantId profile profileId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/SettlementProfile/merchant/{merchantId}/profile/{profileId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update SettlementProfile merchant merchantId profile profileId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"profileId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"isAdminRequest":{"type":"boolean"},"settlementAccount":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"settlementProfileId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementAccountRequest"}},"additionalProperties":false,"title":"AddUpdateMerchantSettlementProfileRequest"}},"text/json":{"schema":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"isAdminRequest":{"type":"boolean"},"settlementAccount":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"settlementProfileId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementAccountRequest"}},"additionalProperties":false,"title":"AddUpdateMerchantSettlementProfileRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"isAdminRequest":{"type":"boolean"},"settlementAccount":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"settlementProfileId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementAccountRequest"}},"additionalProperties":false,"title":"AddUpdateMerchantSettlementProfileRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      