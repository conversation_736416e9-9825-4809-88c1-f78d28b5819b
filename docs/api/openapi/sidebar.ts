import type { SidebarsConfig } from "@docusaurus/plugin-content-docs";

const sidebar: SidebarsConfig = {
  apisidebar: [
    {
      type: "doc",
      id: "api/openapi/gemspay-settlement-api",
    },
    {
      type: "category",
      label: "AutoSettlementChargeSetup",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method delete",
        },
      ],
    },
    {
      type: "category",
      label: "BackgroundJob",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "BankReconciliation",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "BatchPeriod",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method delete",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "ChargeRevenue",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "Connector",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "FundReversal",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "FundTransferTrace",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "GemspayBankAccount",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method delete",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method patch",
        },
      ],
    },
    {
      type: "category",
      label: "HealthCheck",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "ServiceProviderNipConnectorMap",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method patch",
        },
      ],
    },
    {
      type: "category",
      label: "ServiceSetup",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "Settled",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "Settlement",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "SettlementAccount",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method delete",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method delete",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "SettlementMapping",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method delete",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "SettlementMode",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "SettlementProfile",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method patch",
        },
      ],
    },
    {
      type: "category",
      label: "SettlementSplitCode",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method delete",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method delete",
        },
      ],
    },
    {
      type: "category",
      label: "SettlementSplitConfigAccount",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method delete",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "SettlementTunnel",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method delete",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method delete",
        },
      ],
    },
    {
      type: "category",
      label: "WemaNIPService",
      items: [
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/openapi/",
          label: "Missing summary",
          className: "api-method get",
        },
      ],
    },
  ],
};

export default sidebar.apisidebar;
