---
id: update-settlementaccount-merchant
title: "Update SettlementAccount settlementAccountId merchant merchantId"
description: "Update SettlementAccount settlementAccountId merchant merchantId"
sidebar_label: "Update SettlementAccount settlementAccountId merchant merchantId"
hide_title: true
hide_table_of_contents: true
api: eJztVktv2zAM/isBT3todbejb1lRdD10KPo4BTkwFpuolSVVj7aB4f8+0HISp/GAFeh2Wi5RJOoj+ZH6mAasI49RWXMuoYTkJEa6phg11WQiVpVNJl6Qr1ZoIgiIuAxQzmBnM802MBfg0GNNkTybNGCwJighvDY9lyBAGSjBYVyBAE+PSXmSUEafSECoVlQjlA3EtesgoldmCQLurK8xcqRJSWhbsfVS9zH+FfCz04vr6e3Njw30ilCSfwN4286zMYX43co1W1TWRDKRl+icVlVXh+I+WMN7B1B2cU8V18B5rlpUFPh0gebhxEoay8gkrXGhKYfXis64r8LPLrU33rF/cKMVgFIqTgb15SDWO9SBBEQV2RZuXzVb7+Mqs8T8Q6SX+J+QASHDRvn0+T8zG2YYyVNw1oSc6rfjY/6SFCqvHDuAEq5TVVEIHZP7J9nB5MDDZES8JhupmexpTk1xZWXWnYqFp9OfEgp0qjgALpoR5LbYIBbNDrsFAYH800ZWk9csQTG6UBbFkuqQ3NKjpCNeV9omeYTOAUsOd8jVTnZOX7B2mvZ7YVedg5KPH9mBrrEg3uVS97U6ozo4XA+4nEwvz0EAJ5DZfvrKF50NscaugXuZfYcq7JV123+djjiNyrDjjsCmr88M0CkQIwNNQDk+uurdOCwHzucCVjZEhmyaBQa69bptefsxkV9DOZsLeEKv8gOYNSBV4LXc9vur6LcTAj5c9YPm44SH8FhW/SaaNXONOvEvEPBA698MYR5w/zCEAVXtvBWbIfruRGRvg5G9jYQTzqcnGfDLDQPsLA7mML+h7cO+nN6cMN6iH+B1fj4en/mfAD7nZG0XdSdC3V4DGs0y4ZJtMyh/fgH4g1Ly
sidebar_class_name: "patch api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update SettlementAccount settlementAccountId merchant merchantId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/SettlementAccount/{settlementAccountId}/merchant/{merchantId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update SettlementAccount settlementAccountId merchant merchantId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateSettlementAccountRequest"}},"text/json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateSettlementAccountRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateSettlementAccountRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      