---
id: update-batchperiod-update-merchantprofile
title: "Update BatchPeriod Update MerchantProfile settlementProfileId"
description: "Update BatchPeriod Update MerchantProfile settlementProfileId"
sidebar_label: "Update BatchPeriod Update MerchantProfile settlementProfileId"
hide_title: true
hide_table_of_contents: true
api: eJztWUtz2zYQ/iuaPTUtFCV2rKS82R5P7ENajR/Tg0aHFbGUkJIAAiydaDT8750l9aAeTpUm7TQZ+SIZXHz7xAfp0xycp4BsnL3RkEDpNTJdIKdTT8E4/VAvvKOQTtGyDy4zOYECxkmEZAi16aA2hZECjwELYgrycA4WC4IEIjHnVJDlQQNwo0GBsZCAR56CgkAfShNIQ8KhJAUxnVKBkMyBZ76G4GDsBBRkLhTIEmppNFSVWnl5e/Xu7vzh/noJPSXUFL4AvKpGjTFFvnB6Jhaps0yW5S16n5u0rlXvfXRW1nagMAScSQRMRWytu/F7ShkU+CAVZ0P10/G6fFL/A9IF1NpIDJgPWlAZ5pEUsOFc9m+2rdWk2yY9qASK6RP/IKm0m/PzLz9CTgIWKHpnYxPMyYsX8qIppsF48QEJ3JVpSjGCak9q3Vefo9lfhCeSjQuoteHYuZzQQqWgoBhxQvtqYMs8x7HkJcerUqCRD3G3hxT+vsISyaJqZTSWYvytPv0HhLXc+LsdOwza2Ml+jzsbsWR3t4p2f31akxL/u3nb8Hvp9IH9oQzL/IlEsuCKe3NgSdkdbNpcJ9euDC1zY5kmNUevEjOWT08kkDQQMulz/mwd5Bx1WYKQDjttMvPP9lzMDhuilflVgSY/aM/OpH/FhPwfp/9Q1ntLfLdditsFw0krtlx/AewGg34LwJ04/zA83evlXwBdvl6j1TkF2L7fnrzdjsR+JPYjsR+J/Ujs3w+xf/472JHRj4x+ZPQjox8Z/Xth9Fq32dRoGrWn09reWSwtFaCFk85+obQgnjrdiKWpqKW1aJpAD73ptVB7DWpvC7U33wNbgQx7eFwqtWXIRTJl9jHp9SZUxNJPAmp6Lu/T3JX6OXoPIpHKbXW7lkmvPmHhpXDDHVaE0wzfnGX9V92z1y9fd1+d9U+649Ms7Z6kv/ZPs34fM+zXmMZmrh7YVQ+K6HHWWTeicz64AQUScVPWx5cyON5FLrC+PRc68NeWe6N5qyPU0tQq1ZRrvmjEENAbUBtauFqEAQq2vIKCZJ/jkYKpiyxw8/kYIz2EvKpk+UNJYQbJcKTgEYNpjslwDtpEea9XA7wV+UoRhJ9uFxL4s45o9/syWnKcFYZ7xLyU/0DBnzR7QsGvRpVaiuzfPJzGbUvSX4Ukin/z9LIB7N4LwNpi56vyesd5mpLnlm3b6ah10gbn95fidbz4GaCoL2wI+FF+T8CPTW1cnVtN9vXaHHK0k7L+8AWNa/n7Cwt5+7A=
sidebar_class_name: "patch api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update BatchPeriod Update MerchantProfile settlementProfileId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/BatchPeriod/Update/MerchantProfile/{settlementProfileId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update BatchPeriod Update MerchantProfile settlementProfileId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementProfileId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"UpdateMerchantBatchPeriodRequest"}}},"text/json":{"schema":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"UpdateMerchantBatchPeriodRequest"}}},"application/*+json":{"schema":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"UpdateMerchantBatchPeriodRequest"}}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"batchPeriods":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponse"}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"batchPeriods":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponse"}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"batchPeriods":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponse"}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      