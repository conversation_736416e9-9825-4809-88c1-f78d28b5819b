---
id: add-auto-settlement-charge-setup
title: "Add new auto settlement charge setup"
description: "Add new auto settlement charge setup"
sidebar_label: "Add new auto settlement charge setup"
hide_title: true
hide_table_of_contents: true
api: eJztWE1z0zwQ/iuZPfEhk9LS8OJbYBjgAHSacsrksLHWiUCWhCS3ZDL+7++snA+nSaFlmOFAcrFHfnalffZZRaslWEceo7Lmg4QcUMphHe2IYtRUkYlv5uhnNKJYOxAQcRYgH8PdmIkAhx4riuQZugSDFUEO795+HA2/XL0HAcpADnNCSR4EePpeK08S8uhrEhCKOVUI+RLiwrFliF6ZGTTNpAVTiK+tXDCisCaSifyKzmlVpEj6X4M1PLbnyk6/UhFBgPMcd1QUEm4TyoW3pdLEXNyeX0BpfYURcqhrJaERUKTAO1BTV9MU1AYqbT3VtAIbQ/qQY1NrjQxLFDSN4DwoDgX1RWelJepAAqKKjIXh4WRdthwB+4n0Ix7p2NDRFcmTp0deWl7Yk6fgrAltoKcnJ/yQFAqvHE8AOYzqoqAQQHSrLunLaVQPI3LlagucWqsJDRNQUQi4Q9ddBAiQGO8xnfqraRL7uvn1kivyPEN8XQdlKIRPaRO9x1xrw89matFLZWaHRbpniDsSOZSa++rtHcXR7YgvV/JK1HnCSHIYf5oViZGyqKpkUlmpSvUQmwcs9nBxrNf7hxytn+/RSE1+bzd68F50LKFjCR1L6PfON8faOdbOv1476dS3e8IbStkzdNPjXPS2qum1yuSR1ARWFOeWm0VnQ5IOxjnk0Een+myabU2z1jRLpqGPUgLr0V+vm8Paa+4FY3Qh7/dnVIXazTxKesbvhba1fIbOAfd+XOaX2/7v7Q+sXCvmgwdzOCvxv/Ny8CI7f/n8ZfbifHCaTc/KIjstXg3OysEASxzAtu5OOlW16Tm5WS1tyvqG6Co4XPS2TPeGFx9AAMfU8nj9nA2ZnQrTxrRqge/J705SNnLrnLQb0fK2XFE/BnQK2hq6k3xIQuL+fM5py8ewXE4x0Bevm4aHv9fkF5CPJwKu0au2OseTRqxbdc6XVIE/yI3+bi120xrAo8tVX/+4xxcHh4L4Rovdi4Fr1DXDoBHL1dc3rcPsih1sEXtHp63FsCjIxQ62O+mkI9+Lz6MrEDBd3SVUVjLa4w1fSuANr0OATaGlTTGNLUGjmdXpHwLamfn3P5SLF/o=
sidebar_class_name: "post api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Add new auto settlement charge setup"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/auto-settlement-charge-setups/add"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Add new auto settlement charge setup

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddAutoSettlementChargeRequest"}},"text/json":{"schema":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddAutoSettlementChargeRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddAutoSettlementChargeRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      