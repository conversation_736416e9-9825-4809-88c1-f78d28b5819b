---
id: get-bank-statement-by-id
title: "Get bank statement by ID"
description: "Get bank statement by ID"
sidebar_label: "Get bank statement by ID"
hide_title: true
hide_table_of_contents: true
api: eJztWE2P2zYQ/SvGnFqAibdpLtFtN5tuFmiAYO2cFj6MxbHNLEUy5MiIIei/FyPZsqy6hd3mkINOkkXO43w8PRmvAh8oIhvvHjVksCa+Q/cyY2QqyPHd7lGDAsZ1guwZZO2Jcu9yY00TBQsFASMWxBRlTwUOC4IM0gGjQTAOMgjIG1AQ6VtpImnIOJakIOUbKhCyCngX2tBo3BoUrHwskCGDsjQa6lp16A8fPs1uv8w/HqA3hJriFeB1vZDNKXiXKMn6m5sbuWhKeTShKS6DWZnnlBIoyL1jctwg0XeeBotycvX3E/zyK+UMCkKU7rJp8dMe6rhx6b0ldFArKCglXNO5JrjSWlxaaiuqFWjkC44z+oKOSgtWFMnllx297JPjjz1WBeTKQvjhvCNQUPC7tzfCjCEeGxYwuDsDUyvhojNufYcWTxNyZbFsptulr30pedUKcuvT1UGJMfI98tmqj/uR6RWbogkhp68L4IguYS5Eeu/LPXPaSOOY1qe5Gce/vxmE9bmCMeJO2M5UpB82/e4l/dM4uidGY9NFROhl+UQFxpeLorAYNOLfZtQ7Yt7sPvIsj6SNFK1pafhips0HiA39WzUjff69PK7f8uWjN2lGcWtyer/BePJW96DzSMjX4RZem5W5JkaarrWRktF+7lFkhTbRsU8PA+Gf9+fbiqQkMJjof0U/Qv5fhMP1IzptKbaAIViTN5+n6dfkR5EeRXoU6VGkR5H+mUS6+Q89qvOozqM6j+o8qvNPpc61YJ76IA/EE1GuSUf3yXI3ebwXqSLe+L15JC+RmDwZTDGYqUS8iieGUfusQ0nTqmcU1aAgUdwerKQyWvF2mEPKptM1FakM64iaXst9bn2pX2MIIF6OcSvfjLQrt0gBd5MZMds249vPj6BA0Nuitr/JQIJPXGDzHTqYS/9c7ElXOv70DKFatVlX+0Y8AwYDrewPWnF4emwGKMj6vtlCwcYnFpSqWmKiL9HWtTz+VlLcQfa8ULDFaFpCPVegTZJ73U1/kHDnYsEvT3un7NcJqPOFHHTTiWpu0ZbyCxS80G5g8NWLWh08uB+eRntcz/HrUhFDsF29zXMK3FvrQyx6JH34MIe6/gvirU9B
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get bank statement by ID"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/bank-reconciliation/bank-statements/{statementId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get bank statement by ID

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"statementId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"reference":{"type":"string","nullable":true},"bankStatementFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"openingBalance":{"type":"number","format":"double"},"closingBalance":{"type":"number","format":"double"},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"},"transactionCount":{"type":"integer","format":"int32"},"transactions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponse"}},"additionalProperties":false,"title":"GetBankStatementResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"reference":{"type":"string","nullable":true},"bankStatementFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"openingBalance":{"type":"number","format":"double"},"closingBalance":{"type":"number","format":"double"},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"},"transactionCount":{"type":"integer","format":"int32"},"transactions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponse"}},"additionalProperties":false,"title":"GetBankStatementResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"reference":{"type":"string","nullable":true},"bankStatementFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"openingBalance":{"type":"number","format":"double"},"closingBalance":{"type":"number","format":"double"},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"},"transactionCount":{"type":"integer","format":"int32"},"transactions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponse"}},"additionalProperties":false,"title":"GetBankStatementResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      