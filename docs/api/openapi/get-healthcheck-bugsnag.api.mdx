---
id: get-healthcheck-bugsnag
title: "Get health check bugsnag"
description: "Get health check bugsnag"
sidebar_label: "Get health check bugsnag"
hide_title: true
hide_table_of_contents: true
api: eJx1kktP5DAQhP9KVCdW8hLYo28sQgMHJMTAaZRDT9IkFolj7M5oR5H/+6ozj132cYqVKrfqK/eMMXAkcaN/aGDRstwz9dLVHdfv36c2eWphINQm2A0O4q2KqAwCRRpYOKo4w9PAsFjdPa5vXl/uYeA8LDqmhiMMIn9MLnIDK3Fig1R3PBDsDNkHvZkkOt8i50rNKYw+cVL929WVfhpOdXRB48JiPdU1p4SczR/KiqXolqjFAlJszyQDSzceUaEE0sGiPLi/Lu7ylztx3J3optgrjEhItixbHtIU2kgNX+q57sepuaQQoOGdfxsXLif9UgkPKdC+WLNIzwN7KW6eHmCg0w+Zd9fIBmFMMpDXu6c2/8/yCfpcovAPKUNPzuvAJfV85Nzgd04YnGZVBt2YRB3zvKXEr7HPWX9/TBz3sJvKYEfR0VZ5NlU2p2fVahqXVGhg36hP/FeyevTCXmBx8XzcgS+FrtW/Er/z/vMS7aif1LY0e36/1d0Lcv4Jn8/vHw==
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get health check bugsnag"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/health-check/bugsnag"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get health check bugsnag

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      