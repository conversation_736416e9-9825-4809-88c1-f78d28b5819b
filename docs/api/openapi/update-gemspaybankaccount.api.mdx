---
id: update-gemspaybankaccount
title: "Update gemspay bank account id"
description: "Update gemspay bank account id"
sidebar_label: "Update gemspay bank account id"
hide_title: true
hide_table_of_contents: true
api: eJztW9tu2zgQ/RVjnvYiN23SuLt+c4IgDdBLkMtTkIexOLbZUCRLUm4NQ/++ICVb8iWtHASLtGZebIszQ4o855CjaOagNBl0XMkLBn3INUNH55RZjbMhygdMU5VLBwk4HFvo30HVeILyYVA13ieg0WBGjoy3mYPEjKAPnEECXEIfNLoJJGDoa84NMeg7k1MCNp1QhtCfg5tp72Gd4XIMCYyUydD5IeWcQVEky6DnZx+vB7c37xehJ4SMzA7Bi+K+NCbrThSbeYtUSUfS+a+oteBpmJODL1ZJf20jlBp+odRPizZ+Bh0nG3zLGfkURrp5UzIXAoeCyhEWydI+z4ZkWnn4RTlVrF14b9x6LA+z9ANNSbSLPJWt7DKUHlFXNCJDMqUd7rQ0mAPJPPPAS5UQlPplgQQsOScoowDNJYY9EtfjOu5EgM0Gbm9mmiwUCejcaGXbzZKhDM1DK1ONMz/A0wlKSaL1oq26tV6+VbdrkY+f4OZFoJWTcZLMdus15nqWcsfRKdP6XpYeZxnyNoD0VGKMewyguGxQcoTCUg2CAWO3TYlroOGqFAQvNeDou4vcj9yP3N8/7je3/7/+jiIQRSCKwJ6JQBGW2Wolbcnqw9ev/QcjmxquA9D6cJ2nKVkLSTN5CCcHLZDvpBq2ClUbDpUShNLPXkbW4rjdvDF0Lbrj7ZYtatnza9nPmbXXUhRUZYdugv0Od/MD1doCZ8entJ2TAq37qBgfcWIns1bhVl3a6loCqSF0xAbuh5z1uOw6nlFQjKqbXXzSCcoxfVDjpgqhMTjzz1kcZfbZdEUbushWFS0KVRSqX0io9kka2p6szsltnqk+SzG7qk5SgfrKusj9yP3I/f3j/v+QBSbVP7A+KddWDcpk7teb8PXZPl2c35pT/sQkeety1nGfJczi8z1KJshsPHrb9cFbTKHj7hR3p5hCxxQ6ClUUqpcvVPskDTGFjtyP3I/cjyn0i5rw3zGFftKbazF3jttS3JZi7hxz5yhUUahevlDtkzTE3DlyP3I/cj/mzi9qwn+/3Dm89b36hnf5pnhnXEbpeHHtVALeCaWtGbmJYmV5a+rrW0OZax8OUPODyq3r3bqV28GcsyJUL5jpolY2N8JXsTqnbf8geOV6bJDRK/89FSpnr1Br8FWrPrO/qitXz75jpkXA2spGVK/y2n5TN9Tbyuq19QD1JtGw83tB/fMxya8tKuytFHAsRbw2W2h1fWWbJD/Wuj7ybQL7WGsofm60LRM1OBrhP8ej3tvu8bs377pvj3uH3eHRKO0epv/2jka9Ho6wBxvqUsdaF5FFS9CXkQrMXGVV53pZ2tIZXF5AAh4qJSKnbxbHmAyDblQ10T9F6gqwl2LQqFbw4mWCyJUYvgPUHBLYhmJIoM+Zr7WZKOu87Xw+REu3RhSFv/w1JzOD/t19AlM0vNSAuzkwbv13tuTq2rCWhRTwx1VVzP1np8bP6nAXSaL0KeIURe5/ecTSrCw9L+6LZFEd/uy9l700atGXI/Cl6mXraRmw6+uMGhYbr7nUHoM0Je0ats1O7xuCczm4OfW9Dqv69aykhsFvnkf4rZwKFe4tnGfDtTkIlOM8HICh7Nr//QdBK31i
sidebar_class_name: "patch api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update gemspay bank account id"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/gemspay-bank-account/{id}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update gemspay bank account id

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"id","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"enum":["collection","settlement","operation"],"type":"string","title":"GemspayBankAccountTypes"},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"paymentChannelId":{"type":"string","nullable":true},"partnerId":{"type":"string","format":"uuid"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddUpdateGemspayBankAccountRequest"}},"text/json":{"schema":{"type":"object","properties":{"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"enum":["collection","settlement","operation"],"type":"string","title":"GemspayBankAccountTypes"},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"paymentChannelId":{"type":"string","nullable":true},"partnerId":{"type":"string","format":"uuid"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddUpdateGemspayBankAccountRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"enum":["collection","settlement","operation"],"type":"string","title":"GemspayBankAccountTypes"},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"paymentChannelId":{"type":"string","nullable":true},"partnerId":{"type":"string","format":"uuid"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddUpdateGemspayBankAccountRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"partnerName":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"partnerId":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"changeLogs":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"preImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"postImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true},"updateNote":{"type":"string","nullable":true},"action":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GemspayBankAccountChangeLogResponse"},"nullable":true}},"additionalProperties":false,"title":"GetGemspayBankAccountResponse"}},"additionalProperties":false,"title":"GetGemspayBankAccountResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"partnerName":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"partnerId":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"changeLogs":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"preImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"postImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true},"updateNote":{"type":"string","nullable":true},"action":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GemspayBankAccountChangeLogResponse"},"nullable":true}},"additionalProperties":false,"title":"GetGemspayBankAccountResponse"}},"additionalProperties":false,"title":"GetGemspayBankAccountResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"partnerName":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"partnerId":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"changeLogs":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"preImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"postImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true},"updateNote":{"type":"string","nullable":true},"action":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GemspayBankAccountChangeLogResponse"},"nullable":true}},"additionalProperties":false,"title":"GetGemspayBankAccountResponse"}},"additionalProperties":false,"title":"GetGemspayBankAccountResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      