---
id: trigger-background-settlement
title: "Trigger background settlement job"
description: "Trigger background settlement job"
sidebar_label: "Trigger background settlement job"
hide_title: true
hide_table_of_contents: true
api: eJyNkkFr3DAQhf+KeacWRJz2qFsKJUmhNHQ3J+PD2J7YamxJkeQli9F/L+PdbNgkh5wk9DTD+97MAuc5UDLO3nbQSMH0PYcf1D72wc2223BKI09sExQS9RG6wqv8yzWoFTwFmjhxEHmBpYmhcf3z9+bqfnsDBWOhMTB1HKAQ+Gk2gTvoFGZWiO3AE0EvSHsvlTEFY3vkXMvn6J2NHEX/fnkpR8exDcaLbWhs5rblGJGzeqNsDzhFczJcxBNQ8c81UJg4DU7YvYsC6SkN0CjJm/IMtDyUQiFy2L2wzmEUtJR81GXZ8xRn3wfq+ELu7ejm7oK8h6AY++BWSiN9NK55ip72xWvIxdXdLRSk+4Fg9w1ZrdYmslJ7zPYzZGdZnLJN/JxKP5Kx0nm1vxyhK5A3UG/mK7wreK0wSEa6wrI0FPk+jDnL89PMYQ9d1Qo7CoYawavqrF5mLkl1JorQQT/QGPmdv9bZJHum8eXvcUG+FrJ1H/l+5P35hu1onOXbGvRppnd/Nlvk/B8mF/79
sidebar_class_name: "post api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Trigger background settlement job"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/BackgroundJob/settle"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Trigger background settlement job

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      