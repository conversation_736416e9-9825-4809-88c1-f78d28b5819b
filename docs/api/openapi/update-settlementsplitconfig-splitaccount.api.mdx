---
id: update-settlementsplitconfig-splitaccount
title: "Update settlement split config settlementSplitConfigId SplitAccount splitAccountId"
description: "Update settlement split config settlementSplitConfigId SplitAccount splitAccountId"
sidebar_label: "Update settlement split config settlementSplitConfigId SplitAccount splitAccountId"
hide_title: true
hide_table_of_contents: true
api: eJzVVU1v2zAM/SsGT/tQ6m5H37Ki6HoYUDTtLkUOjMU46mRJleS2geH/PlBKEydtBwxYBzQXCzLz+MjnR/ZgHXmMyppzCRV0TmKkGcWoqSUTg9Mq1tYsVTPjI9a17UwEARGbANUN7GJTwImVBHMBDj22FMlzUA8GW4IKwmEwA59LEKAMVOAwrkCAp7tOeZJQRd+RgFCvqEWoeohrl2CiV6YBAUvrW4zMu1MShkHsMjH+NLN9kwRnpz9m0+ur70/QK0JJ/i/Ah2GegynEb1auOaK2JpKJfETntKqTMuVtsIbvnkHZxS3VrIbzrGNUFFIcF/8TdUejWNO1i0RwW5O03UITVwUopeJUqC9GSEvUgQREFTUjXB98HLNRjy9zIQks0mN8b5zH7f70+V2RZzhPwVkTMpuvx8f8kBRqrxxngQpmXV1TCCn5/pucpdh5s0jlFNn2xSueLcZkimd2aymurMyWq9lzyXoVlOhUuYOcpD9OcqqyfyXXUI6Tlf1+tgEEBPL3T7Om85r9GKMLVVk21IbONR4lHfG51raTR+gcsP9Y6MudB08fsXWaDiU9HtjkS5vE3WhzRm1wuC526hTTi3MQwDxyY++/wCDA2RBbTJ/TZnS8ScP3NN1+g8mKTqMyTCV1pt9IcQPoVGrdi2KAgOr1cT3mkiL36cwFrGyInKTvFxjo2uth4Ou7jvwaqpu5gHv0CtkPLJpUgc9y64GDeraTET5cbgbsx4LX0Et1bi7RrFmPrCGAgF+0/sMS4uH+P2nst2yYD+JpifzzhuSMo5W1ZcNF57cnGXByxQC7iGd7iG2zdffF9OqE8RabBdby+q/A4wNvQnzIBdvEOs2mdNeDRtN02HBsBuXfb383A+g=
sidebar_class_name: "patch api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update settlement split config settlementSplitConfigId SplitAccount splitAccountId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/settlement-split-config/{settlementSplitConfigId}/SplitAccount/{splitAccountId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update settlement split config settlementSplitConfigId SplitAccount splitAccountId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementSplitConfigId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"splitAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"}},"additionalProperties":false,"title":"UpdateSettlementSplitAccountRequest"}},"text/json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"}},"additionalProperties":false,"title":"UpdateSettlementSplitAccountRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"}},"additionalProperties":false,"title":"UpdateSettlementSplitAccountRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      