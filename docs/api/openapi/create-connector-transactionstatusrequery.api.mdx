---
id: create-connector-transactionstatusrequery
title: "Create connector connector transaction status requery"
description: "Create connector connector transaction status requery"
sidebar_label: "Create connector connector transaction status requery"
hide_title: true
hide_table_of_contents: true
api: eJztWE1v2zAM/SsBT/tQmm5H37qsaHPYFjTtKQgGxmYTdbakSXTawPB/H2gljvuBISmGAQWSS2SJfCKfRFnPFVhHHllbM8oggdQTMg2tMZSy9dceTcBUhgMjl8HT75L8GhQwLgIkU2htYabAoceCmLwMVWCwIAFtTRRoAwk45CUoEDDtKYOEfUkKQrqkAiGpgNdOHAN7bRZQ16rFujj/Njm7ub7cQi0JM/IHgc2iMQX+YrO1WKTWMBmWJjqX67QhZHAXrJG+Z1B2fkcpgwLnhT7WFJrRHVtC5tOZFZgyz3GeU4yxrhVglmmxx3zcgbrFPJAC1iy20FmFSbMKV3EVrmIWwg8wPfCbCrhL9IePbydygfIUnDUhhvL59FT+Mgqp1048IYFJmaYUAqju3mrWyOWoD0o1bKB2hnNrc0IDtaBntEf6CgoKARf72WbIe8S1JWG4bwhdh690q42OZO3hGiiEfZdaHbw5FARb+pRGJrDmUvz2TipDprENTAeHdt3Y/oedG1mHhka/0imNvV1pOTX3ilgXNGEs3EvWt9YXyJA0NPTFFP5ZwN9H45/b9iWaLCf/7OA49Ng41tKxlo61tKulV90ajkV0LKJjEW2LqLkQPr78DRsR1WtlT6fVobsXFVVvJ6kK4qUVFeZsaLa3yKQEBuj0oIUYVG2zHnTg+hGuv4MTcrdKrPS5iCVmF5LBYEFFKN3CY0Yn0k5zW2Yn6ByIOJLz4GonkM4fsHA5vXDrbiWVaLFb21hsuLygIjhc9ybEnFNBhntn4xEokIgiS6tP4iipFtgU3UbhvZa9R2vQ7ozOnbtWkYZqQ+wU0Ol4Q2/ladJ9+Au7MwVLWaRkClU1x0A3Pq9r6Y7jyXSmYIVex607rSDTQdpZu+2exNvqBHh3tZGy73sSxEt5bDrRSOIrzEt5AgW/aP1Ib9ezWm0l8j8PIk7WEeRtIKLX4+gwAvabs2Vn8ewOt/M4S1Ny3LHtTjrrFMn4x+QaFMw3Gr5oDkjweC8fA/A+EmKb1OLrQfoqyNEsyubFA3Fm+f0B4sITcw==
sidebar_class_name: "post api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create connector connector transaction status requery"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/connector/{connector}/transaction-status-requery"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create connector connector transaction status requery

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"connector","in":"path","required":true,"schema":{"type":"string"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"transactionId":{"type":"string","nullable":true}},"additionalProperties":false,"title":"TransactionStatusRequeryRequest"}},"text/json":{"schema":{"type":"object","properties":{"transactionId":{"type":"string","nullable":true}},"additionalProperties":false,"title":"TransactionStatusRequeryRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"transactionId":{"type":"string","nullable":true}},"additionalProperties":false,"title":"TransactionStatusRequeryRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"responseCode":{"type":"string","nullable":true},"responseCodeDefinition":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"sourceInstitutionCode":{"type":"string","nullable":true},"datePosted":{"type":"string","nullable":true},"transactionType":{"type":"string","nullable":true}},"additionalProperties":false,"title":"TransactionStatusRequeryResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"TransactionStatusRequeryResponseNIP_ResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"responseCode":{"type":"string","nullable":true},"responseCodeDefinition":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"sourceInstitutionCode":{"type":"string","nullable":true},"datePosted":{"type":"string","nullable":true},"transactionType":{"type":"string","nullable":true}},"additionalProperties":false,"title":"TransactionStatusRequeryResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"TransactionStatusRequeryResponseNIP_ResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"responseCode":{"type":"string","nullable":true},"responseCodeDefinition":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"sourceInstitutionCode":{"type":"string","nullable":true},"datePosted":{"type":"string","nullable":true},"transactionType":{"type":"string","nullable":true}},"additionalProperties":false,"title":"TransactionStatusRequeryResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"TransactionStatusRequeryResponseNIP_ResponseHandler"}}}}}}
>
  
</StatusCodes>


      