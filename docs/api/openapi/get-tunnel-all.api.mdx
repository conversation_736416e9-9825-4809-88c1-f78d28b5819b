---
id: get-tunnel-all
title: "Get tunnel all"
description: "Get tunnel all"
sidebar_label: "Get tunnel all"
hide_title: true
hide_table_of_contents: true
api: eJztWMFu2zAM/ZWApw3wmq67+RZ0RVtgG4KmPRU5MBbjqJMlVaKDdoH/fZDlJG7qdW7RYRfn0tQin8hHmkTeBowlhyyNvhSQQk58XWpNaqIUJMCYe0hvYUbMigrSzSnME7DosCAmFyw2oLEgSGGKOf0oiwU5SEBqSOG+JPcICfhsRQVCugF+tMFUaqa8tlsaVyDHR19OoKqSJ3gz+YveB+387PtscnN9sUVbEYrax9F9KR0JSNmV1AHv2UmdQ1XNg7G3Rnvy4fzk+Dj8EeQzJ20gElKYlVlG3kMCmdFMmmskeuCxVRhu3jy/wSzuKGNIwLpQE5YR3zdQe8OFMYpQQ5VAQd5jTs/jTECXSuFCUcyoSkAgt69D5zAQKZkK//cwpOi6ZEd1WUoRAoo894mmzVcPe6/KvJchZizX1E1X5giZxIRfzEUg0yeWBdUMGyGX8nU+2QrDS+L/OdvNRb14sehYkzs1ol+BpP9KSywVd1Mp/eR/Ex2KLYQMLYRq2uJvicpTAixZ1S898Wkkaj/GrppXuO7Zp6m/AvZwLL6AGgqwm4x9plZ0qEdfP/OldJ6nf5gG+9ZxsqvaCt/uy4ZRBWffM9La4Yoy40RfF00Pb47POlpLU/q3+b9DO3yTkVyxfXCBWihysYWtVTKrN/D4zpthNwy7YdgNw24YdsOwG+IvhmEpDEthWArDUhiWwrAUqvA5mIUBaMS1/whr1a4gXplGzQuTAnkFKYzRynG0G0c7T269le9Kp4Iexmx9Oh7nVPjS5g4FHYXvmTKlOEJrIehfUi9Nzcsuk8JbfBzt0xlNppeQQECPMa4/1w1qPBdYz++tIHcYe/eYb0lnVRJj3TR53QLaUIWIAgkEnHkCK+M5nG42C/R041RVhcdRPwwZC+lD1cSuMD/p8VDAXKMqw/0QNMQXHBqFcm8+D/84Gdvidl4lW6mx8+aDpHeaIXy4anTJj6OQYhcZMYiWsPkk5ng6yTKy3DprQ8xbDXN+dg1V9Rtf6J9K
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get tunnel all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/tunnel/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get tunnel all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      