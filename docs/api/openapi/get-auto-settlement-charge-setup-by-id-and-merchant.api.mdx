---
id: get-auto-settlement-charge-setup-by-id-and-merchant
title: "Get auto settlement charge setup by ID and merchant"
description: "Get auto settlement charge setup by ID and merchant"
sidebar_label: "Get auto settlement charge setup by ID and merchant"
hide_title: true
hide_table_of_contents: true
api: eJztV8Fu2zAM/RWDpw1Qmm5H39KtaHPoVjTtKciBsRhHnSypEl0sMPzvg2wncZt0S4Zup5zsWOQj+UgqZAXWkUdW1owlpJATj0q2E2LWVJDhL0v0OU2IS3exGsuRkTfksyUaBgGMeYB0Cm+qwEyAQ48FMfkoWoHBgiCFEI/HEgQoAyk45CUI8PRUKk8SUvYlCQjZkgqEtAJeuUaNvTI5CFhYXyBDCmWpJNS16CN3jtx6u1Ca/omVq8ubyejh/noNvSSU5I8Ar+tZFA7OmkAhnn8+P48<PERSON>SHzysWUQAqTMssoBBCQWcNkuEGinzx0GqPlateCnT9SFvPjfMwuqxY/dFBbwbm1mtBALaCgEDCnfSSYUmuca2ojqgVI5APMKXkAowKyplp6oqYs5g2TG1Fpy2i+FTaG9EFe7tTBn10uutK+KIMyFMK3JtcH2Forfjdzi14qk4/3Rr+jiC86Z19qopCUKpYD6tueuwvUgQSwYt0UJPHkdcR3XXk11HlCJjni32ZFItOAVdGoFFaqhTpG5whn990ZW3/fCWj9vEYjNfkW1zmtsubKGz4Ge2qhUwudWui4Fmr+f069c+qdU+8c2Tt1hH454V0RJzEPybZikrYqk2ZITuarZPw1QSOTYjt6F8RL203ssZDiZJvCEJ0aRrDBFmzQgg0asDCsusm7Hq7Rmk+vR+YaYgn75/XYXnodp1xmF9LhMKcilC73KOksvmfalvIMnYM41SqzsE3KNiwVweEq2dKUjG7HICCityQ8f4oZdzZwgc2tsh6z/4qcFwRvSqc3NdeiDajqiJsCOgVtP7xJHQhIt2tLz1y6b+WYCVjawBG7quYY6MHruo6fn0ryK0inMwHP6FXbkNMKpArxXW6q7FUYmwUAPtx1S8bHJG5h+8LrPqJZRZ5Rl/EXCPhBq972FRea/2t2h6d6Vov19vTuLLRme7vaxqUYeXs6yjJy3DvrQ8x6nXZ1eQ91/QszdVpB
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get auto settlement charge setup by ID and merchant"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/auto-settlement-charge-setups/{setupId}/merchant/{settlementProfileId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get auto settlement charge setup by ID and merchant

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"setupId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"settlementProfileId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      