---
id: create-settlementmodes-code-merchant
title: "Create settlement modes settlementMode code settlementModeCode merchant merchantId"
description: "Create settlement modes settlementMode code settlementModeCode merchant merchantId"
sidebar_label: "Create settlement modes settlementMode code settlementModeCode merchant merchantId"
hide_title: true
hide_table_of_contents: true
api: eJztVk1v2zAM/SsGTxug1d2OuhXB0PZQrGjaXYIcGItN1MmWKsnFAkP/faDtJHaSYd3QoSjQk2xRfvx4lB8bsI48Rm2rSwUSCk8YaUoxGiqpiqVVFCZW0RX5YoVVBAERlwHkDHanrqwimAtw6LGkSJ7tDVRYEkgI43MCdAUSHMYVCPD0WGtPCmT0NQkIxYpKBNlAXLv26+h1tYSUxG8AJy8DWvYJXqp/BBNwb32JESTUtVYj8POvV9Ozu9uLDfSKUJH/q0jnfDg4WwUKbP9yesqLolB47ZhAkDCti4JCAAGFrSJVsUWinzF3Btlzc+jBLh6oYF6d516IusMPPdTu4MJaQ1hBElBSCLikY0WoamNwYajLKAlQGJ/hrmAWnwOnw3c0Wh2Pa6/V/oyXBKBSmsuH5noQ0T2aQAKijnwWWp+jm7FpvZueFHhBrM16gZUy5Dto54wu2puaPwT7zuUb5rK9ke8kvmUSE6OPf76TVjuzXcpZq57ZuAYZFzY7lLBsI0DZSIlKiivLyuxsaEliUZKQo9P5DuRT6ylvxrApZ1/7u5PWsnGSNzt3CZgw/7TR79ob1qoYXZB5vqQy1G7pUdEJPxfG1uoEnQPWJl3d25bevqbnVAaH62xX0+zs+hIEMHpXr6fP3CGcV4ntTejF8r/UcUTVtgsH0phEl2/Tl3gG6DQMO7grMgiQBxNN0S3y6GRS7kYnOYhpLmDFnMoZNM0CA915kxJvP9bk1yBncwFP6HV3O2YNKB34WW0beC+prebDh5t+rviY8cB2LNl+E6s1k4Km5jcQ8IPWhzMbjzKv5n3yChEMeErzJDYD24uz0HkbjIfbSDjhznpWFOTiwDaEmA9+EdffpreQ0i/KPCpm
sidebar_class_name: "post api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create settlement modes settlementMode code settlementModeCode merchant merchantId"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/settlement-modes/{settlementMode}/code/{settlementModeCode}/merchant/{merchantId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create settlement modes settlementMode code settlementModeCode merchant merchantId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementMode","in":"path","required":true,"schema":{"type":"string"}},{"name":"settlementModeCode","in":"path","required":true,"schema":{"type":"string"}},{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"code":{"type":"string","nullable":true},"isValid":{"type":"boolean"},"settlementMode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"ValidateSettlementModeCodeResponse"}},"additionalProperties":false,"title":"ValidateSettlementModeCodeResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"code":{"type":"string","nullable":true},"isValid":{"type":"boolean"},"settlementMode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"ValidateSettlementModeCodeResponse"}},"additionalProperties":false,"title":"ValidateSettlementModeCodeResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"code":{"type":"string","nullable":true},"isValid":{"type":"boolean"},"settlementMode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"ValidateSettlementModeCodeResponse"}},"additionalProperties":false,"title":"ValidateSettlementModeCodeResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      