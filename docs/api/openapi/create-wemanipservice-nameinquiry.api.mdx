---
id: create-wemanipservice-nameinquiry
title: "Create WemaNIPService NameInquiry"
description: "Create WemaNIPService NameInquiry"
sidebar_label: "Create WemaNIPService NameInquiry"
hide_title: true
hide_table_of_contents: true
api: eJzdVk1T4zAM/SsdnfbDS9k95sYyDPSwbIfC7KHTg2KL1uDYxnYKnUz++46S0CaUQ+EGvTRj68nSk6znCpyngEk7O1GQgQyEif5RgVb7SGGtJV1iQdo+lDpsQEDCZYRsDmxzOZnOWhtYCPAYsKBEgfcrsFgQZHB+9md2cnN9AQK0hQxWhIoCCAjELklBlkJJAqJcUYGQVZA2npExBW2XUNeL1phi+u3Uhi2ks4ls4k/03mjZZDC+i87y2p4rl9+RTCDAB843aYq8m6O9P3WK9g8VYEtjMDfUhlcLQCldadNlWeQUDkAwRCnNgaGZ9s69RRNJQNKJbYHpnXT0MijRU/ocmfRL8+37J0iJYYGidza2Mf46PuY/RVEG7dkbZDArpaQYQfTbtKmqN6jfxEHsXO0Mc+cMoYVaQEEx4vIwfhSmV67Wu2mZNfirjooLtMpQ2Kv5Wyv+8bJ91139aGk2bT9s8dNGJkZDDRj1LwtnklaONcW72JCAaQUZjNHr8RA4HgJZdp5FpAyGNSMlH7PxeElFLP0yoKIj/pbGleoIvQfWCK7D1U4nzp6w8IaGo2RH0ouJsVUblqlb19Da8XJORfS4Gc0oJUMF2TQ6mU5AAEfZ8rH+yUBOtMCmFzrxO4SnAbPbYvbmRS1aGqqOwjmg1yBeKrAYDKuFgBXzns2hqnKMdBNMXfPyQ0lhA9l8IWCNQbeNMl/U4lmamXelI2+obZO8iHI72eDLVafjX0f8Ongt+nvaDB8CazQlm0Etqm73tHX445od7Cz2xskOcSIl+dSz7R+66PXf9O/sGgTk3duhaBsh4CM/QvCR4xDgmtSaS9msVWDQLsvmKkJ7Mv/+AxVpN3I=
sidebar_class_name: "post api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create WemaNIPService NameInquiry"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/WemaNIPService/NameInquiry"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create WemaNIPService NameInquiry

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NameInquiry"}},"text/json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NameInquiry"}},"application/*+json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NameInquiry"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}}}}}}
>
  
</StatusCodes>


      