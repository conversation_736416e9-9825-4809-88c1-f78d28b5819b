---
id: get-settled-all
title: "Get settled all"
description: "Get settled all"
sidebar_label: "Get settled all"
hide_title: true
hide_table_of_contents: true
api: eJytVU1v2kAQ/StoTq3kxml68w21KeWQCoXkFHEY7MFedb+yO45Kkf97NbYhMaXBinIy3n1v9N545rED5ykgK2fnBWRQEi+JWVMx1RoSYCwjZA/QH8IqAY8BDTEFudiBRUOQwQJL+lmbNQVIQFnI4LGmsIUEYl6RQch2wFsvUGWZyha3ccEgd0dfrqBpkkG9pfpD71Pta4XWkj5bLHJQthxQp8bVls8y7d77QUXh6rWmQa27gDZiLt2+pQ0Fsvl5gyc0LSgoV7yF+T04M5b30gsyfWJljuy4dyv1DbfvVuuGQl6h5fmbOjSPc7sIrgwU41n+2jlNaAcFZtc3y+n93Y89uSIs2skI9FirQAVkHGp6Tc1KwNE7GynK/dXlpTwKinlQXsYHMljWeS4amyY5upkRT2K3sBNs19gQV67fb5AV5goySNGrtAemHTBSeNpvdh20yGf2MUvTkkysfRmwoAv5nWtXFxfoPYhcZTeudaJYt00gEz1uJ11uGLI8mS7mkIBU71Q+fYYmAe8iG7TC3ffvH/UDd4duMf3m1GtUVuq0Yne9swdAr1o3XWolIIVWCVQuslzvdmuMdB9008hx933Fc6EiroWSbVBHSuAXbY/T7Ql1LQJAPvorhD6+xsCf82kM+hBJY8D/yZxRJvYxMwbcJ8soSW4ksEuFMcjBzo8hHK35M2UlL0EJB7KHVZPsF/jkeByNZu4sk5Vo+nDbb/vHifyNnhrZTsiLuBiIOKzs7PoOmuYvFiiJAQ==
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settled all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settled/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settled all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"Channel","in":"query","schema":{"type":"string"}},{"name":"Amount","in":"query","schema":{"type":"number","format":"double"}},{"name":"TransactionReference","in":"query","schema":{"type":"string"}},{"name":"Period","in":"query","schema":{"type":"string"}},{"name":"From","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"To","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"Day","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"MerchantId","in":"query","schema":{"type":"string"}},{"name":"IsInProgress","in":"query","schema":{"type":"boolean"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      