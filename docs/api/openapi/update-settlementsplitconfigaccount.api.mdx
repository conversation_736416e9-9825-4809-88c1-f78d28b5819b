---
id: update-settlementsplitconfigaccount
title: "Update SettlementSplitConfigAccount settlementSplitAccountId"
description: "Update SettlementSplitConfigAccount settlementSplitAccountId"
sidebar_label: "Update SettlementSplitConfigAccount settlementSplitAccountId"
hide_title: true
hide_table_of_contents: true
api: eJzVVU1PGzEQ/SvRnPrhEtrj3gJClEMlRKAXlMNkPUlMvbaxx0C02v9ejXdZApRIVXshl1jet29m3sybbcEHisjGuzMNFeSgkWlOzJYacpyCNVx7tzJrrGufHYMCxnWC6hqeYHOBHRfYbIAtFASM2BBTFHQLDhuCCtLztwb8mQYFxkEFAXkDCiLdZhNJQ8Uxk4JUb6hBqFrgbSg8HI1bg4KVjw2yJJ+Nhq5TY6jTkx/z2dXl90fqDaGm+BfkXbfowZT4yOutIGrvmBzLEUOwpi7qTW+Sd3L3isovb6gW2UIUrdlQKjgp/ifaTDtYl5tlSXCsSfu8tCRVAWptJBTa8x2mFdpECtiwFYarFw3c1fiiL6SQMT3we8t5V+5Pn99V8kIXKQXvUp/Nt8ND+dOU6miCRIEK5rmuKaUS/PmTPspkn+Mme4zVEG+87s1Vi7uKySqYYjDTfaTT9i3WDhQkineP7s7RisOYQ6qm0zU1KYd1RE0Hcq6tz/oAQwBxlLTu4slVJw/YBEsvm3TYiW1XvrRrUPuUmhRwuyPEZHZ+Bgokj16qu6/QKQg+cYNlQIZl8I8SPuvHOD/FRsGicRK0aNAO4l4DBgNq/5ZUUL0Zc6Fg4xMLU9suMdFVtF0n17eZ4haq64WCO4wGZWClB9okOetxSF8kPa4u+HAxbMCPE1nofypmuES3FXn7lgAo+EXbfVu8W3TqcdH+95z62DtrfcxLtn7/9Lgn/HIpBE+IV7taBnH0xfns8lj4lsOSb7yWlyLey9cC7/vSfcm6+LfctWDRrTOuBduTyu83yS2Y2w==
sidebar_class_name: "patch api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update SettlementSplitConfigAccount settlementSplitAccountId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/SettlementSplitConfigAccount/{settlementSplitAccountId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update SettlementSplitConfigAccount settlementSplitAccountId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementSplitAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"}},"additionalProperties":false,"title":"UpdateSettlementSplitAccountRequest"}},"text/json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"}},"additionalProperties":false,"title":"UpdateSettlementSplitAccountRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"}},"additionalProperties":false,"title":"UpdateSettlementSplitAccountRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      