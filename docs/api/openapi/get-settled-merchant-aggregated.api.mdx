---
id: get-settled-merchant-aggregated
title: "Get settled merchant merchantId aggregated"
description: "Get settled merchant merchantId aggregated"
sidebar_label: "Get settled merchant merchantId aggregated"
hide_title: true
hide_table_of_contents: true
api: eJytkzFv2zAQhf+KcFMLEFHakZuHwPUQoKidKfBwFs8SUYlkyJNbQ+B/L86SHCvwEqCTBJL37r7HxwF8oIhsvdsY0FATb4m5JfNMsWrQ8aquI9XIZEABY51Av8J0BvYKAkbsiCnKxgAOOwINfyw3u4guYSXaCRRYBxreeopnUJCqhjoEPQCfgxQcvG8JHeSsriLdNMLGzOUBuQEFkd56G8mA5tjTHbXE0boaFBx97JBBQ99bsxBfPz1vVy+7H7N0Q2gofkI8570cTsG7REn2vz8+ysdQqqINwg0atn1VUUrS+8POmrhIo5HFjFq8Mxd463xH3PjphkCNRmgoMdhy0ijn0nJ4F8nlQiVRPM031cdWsJlD0mVZU5f6UEc09CD/Vet784AhgGBad/QXByy3F/OoSwHPxZiDjhwXq58bUCDqI93pG2QFwSfu0Ent7PtnqBeGXS+A6S+XoUXrpMWFY5gceQUM9gI6BlRdQwQK9CJQN432ChqfWMqH4YCJXmKbsyyPeRW7jE14EEl9xDaRgt90vh/0E7a9jHlx7oTRSt19jQ98lXdMTuL65deUwq+FPLt73NMiuvNtz3muG9S8z2qO93+fYux285gW9NfUrp92kPM/bZODww==
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settled merchant merchantId aggregated"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settled/merchant/{merchantId}/aggregated"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settled merchant merchantId aggregated

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"withTransactions","in":"query","schema":{"type":"boolean"}},{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      