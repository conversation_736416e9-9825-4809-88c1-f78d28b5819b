---
id: delete-settlementaccount
title: "Delete SettlementAccount settlementAccountId"
description: "Delete SettlementAccount settlementAccountId"
sidebar_label: "Delete SettlementAccount settlementAccountId"
hide_title: true
hide_table_of_contents: true
api: eJytk09rGzEQxb+KeacWRDbtUTdDTGtIoNTJyexhshrboruSIo1MzaLvXuR/SWP3EOhptZrRm3k/jUb4wJHEejc30DDcs/CCRXoe2Al1nc9OoCC0TtBLvMamx1irECjSwMKxpoxwNDA00vvUuYGCddAIJBsoRH7JNrKBlphZIXUbHgh6hOzCXkKidWsorHwcSKCRszUoRZ2rfJs9LKZPj99P0hsmw/ED4qW0NTkF7xKnGv96e1s/hlMXbahwoLHIXccp1drvInd7aJMLMpPrAAaWjX+FDXWgodFQsM2FSjNekSlQSBy3J+I59tW6SEi6adY8pBzWkQzf1HXX+2xuKARUq9at/J6ClX4PkIcUaPem/8n0xxwKVf3gcPsFRSH4JAO5evbI/oPO/8J2vgbh39KEnqyrRfZOxiOSJShYqCtDp6Cv1WgVNj5JPTmOz5T4Kfal1O2XzHEHvWwVthQtPVfvyxHGpro20CvqE1802Xkn7Orkffp5HKjPk/oerjV/3CS3q/ioz/UPCr9494/3UNqiTiP739s5lH3zQM4t1Tk4z+Hd7H72OEMpfwDhtmeD
sidebar_class_name: "delete api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete SettlementAccount settlementAccountId"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/SettlementAccount/{settlementAccountId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete SettlementAccount settlementAccountId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      