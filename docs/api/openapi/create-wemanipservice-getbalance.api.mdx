---
id: create-wemanipservice-getbalance
title: "Create WemaNIPService GetBalance"
description: "Create WemaNIPService GetBalance"
sidebar_label: "Create WemaNIPService GetBalance"
hide_title: true
hide_table_of_contents: true
api: eJztWMFuGjEQ/RU0p1ZyQ5re9kabiCAlNApEVRUhNHgn4MTrdexZ1Ajtv1ezC2QhtAWpUi6cWNnznmfG9hPPC8g9BWSTu14KCehAyPSDMnTGRwpzo6lLPEGLThMoYJxGSO5BQvq9m0EdAiMFHgNmxBRkfgEOM4IEUOu8cNwvsgkFUGAcJPBcUHgBBVHPKENIFsAvXqIjB+OmUJZqTdC9uB507oaXK+yMMK2YAj0XJlAKCYeC/kY2kuDocxcpyvzZ6an8pBR1MF5qhwQGhdYUIyjQuWNyXDHRL257i7Ly4u0K+eSRNIMCH6SNbGr+uKR6DZzkuSV0UCrIKEac0ts8FbjCWpxYqisqFaTIzeUwBJS2GaYs/juNZevrPu6xWtDhYjzsXV/sin7IQ4YMieREn9hkJMVYPeseCJk/nI/PO8MDED7yoRByHH4eiOGA/QMhc7TFxeHLOJPutSEeAxtdWAxxr3ihDpRheNovPg0S9mZY7x5eacArs1td6tdK80Igde7D8fC20x8Pbq/G/bvrBs44pukm0Dj+ciY44yKHzA23IH8uYnp10AaUCjBNjVx6tDeN+/KANpICNizklb51iTv1HRowMmXk+HapI5LqVir/ifnKxPX3JbrUUqiz9t4aXSl1+zHmR0E6CtJRkI6C9H6CVP03OirRUYmOSnRUondUolLoN73ct8rFtjY9aqtL/HXtYzPiWS6O1+ex0gfkGSTQRm/am7j2Bk488criFsGKIWX2MWm3p5TFwk8DpnQi39rmRXqC3oMYUOMe8mojlqV2KYseX1oDYrZVla3OTQ8UCHtdxfxzdVryyBlWIrt0xHtUt9GO9e433Gyp6uwXy8LvAb0BtW3rFTRoRwpm0qzkHhaLCUa6C7YsZbj289KS1ETZ93S9tU/0suMVQNRBMqpaM8dg6sNyPyrVyuDvZNuqa+3U4cPt8jXgY0seKXbVW2fSeE54TUItlrMdrclzY65JMWqcmpvvgyGU5W+zCe3Y
sidebar_class_name: "post api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create WemaNIPService GetBalance"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/WemaNIPService/GetBalance"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create WemaNIPService GetBalance

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"accountNumber","in":"query","schema":{"type":"string"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"accountname":{"type":"string","nullable":true},"rcrE_TIME":{"type":"string","format":"date-time"},"lchG_TIME":{"type":"string","format":"date-time"},"vfD_DATE":{"type":"string","format":"date-time"},"pstD_DATE":{"type":"string","format":"date-time"},"entrY_DATE":{"type":"string","format":"date-time"},"traN_DATE":{"type":"string","format":"date-time"},"valuE_DATE":{"type":"string","format":"date-time"},"tranid":{"type":"string","nullable":true},"particulars":{"type":"string","nullable":true},"tranremarks":{"type":"string","nullable":true},"dr":{"nullable":true},"cr":{"nullable":true},"balance":{"type":"number","format":"double"},"parT_TRAN_SRL_NUM":{"type":"integer","format":"int32"},"instrmnT_NUM":{"type":"string","nullable":true},"gL_DATE":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementResponse"},"nullable":true}},"additionalProperties":false,"title":"WemaGetAccountStatementResponseListResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"accountname":{"type":"string","nullable":true},"rcrE_TIME":{"type":"string","format":"date-time"},"lchG_TIME":{"type":"string","format":"date-time"},"vfD_DATE":{"type":"string","format":"date-time"},"pstD_DATE":{"type":"string","format":"date-time"},"entrY_DATE":{"type":"string","format":"date-time"},"traN_DATE":{"type":"string","format":"date-time"},"valuE_DATE":{"type":"string","format":"date-time"},"tranid":{"type":"string","nullable":true},"particulars":{"type":"string","nullable":true},"tranremarks":{"type":"string","nullable":true},"dr":{"nullable":true},"cr":{"nullable":true},"balance":{"type":"number","format":"double"},"parT_TRAN_SRL_NUM":{"type":"integer","format":"int32"},"instrmnT_NUM":{"type":"string","nullable":true},"gL_DATE":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementResponse"},"nullable":true}},"additionalProperties":false,"title":"WemaGetAccountStatementResponseListResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"accountname":{"type":"string","nullable":true},"rcrE_TIME":{"type":"string","format":"date-time"},"lchG_TIME":{"type":"string","format":"date-time"},"vfD_DATE":{"type":"string","format":"date-time"},"pstD_DATE":{"type":"string","format":"date-time"},"entrY_DATE":{"type":"string","format":"date-time"},"traN_DATE":{"type":"string","format":"date-time"},"valuE_DATE":{"type":"string","format":"date-time"},"tranid":{"type":"string","nullable":true},"particulars":{"type":"string","nullable":true},"tranremarks":{"type":"string","nullable":true},"dr":{"nullable":true},"cr":{"nullable":true},"balance":{"type":"number","format":"double"},"parT_TRAN_SRL_NUM":{"type":"integer","format":"int32"},"instrmnT_NUM":{"type":"string","nullable":true},"gL_DATE":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementResponse"},"nullable":true}},"additionalProperties":false,"title":"WemaGetAccountStatementResponseListResponseHandler"}}}}}}
>
  
</StatusCodes>


      