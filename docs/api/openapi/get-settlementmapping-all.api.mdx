---
id: get-settlementmapping-all
title: "Get SettlementMapping all"
description: "Get SettlementMapping all"
sidebar_label: "Get SettlementMapping all"
hide_title: true
hide_table_of_contents: true
api: eJytUz1v2zAQ/SvGTQ1AREm6cfMQuBlSBHUyGR7O0lkiyq+QJ6OuwP9enGQnsWNk6iSJ9+7x3runAUKkhGyCf2hAQ0u8JGZLjjw7jNH4dm4tKGBsM+gVvJcfpzKsFURM6IgpCWQAj45AwxO29LN3G0qgwHjQ8NpT2oOCXHfkEPQAvI8CNZ6pHXHbkBzydPT9DkpRJ3xL85f+D9vi/nE5f3n+cWTrCJuxJ9FrbxI1oDn1dIE+cxLdpawFnGPwmbLU725u5NFQrpOJYipoWPZ1TTnL3WeVBfHsk50zHO12xF04bATEYO5AQ4XRVJ9aqqklU9odN9AnK5KYY9ZV1ZLLfWwTNnQt77UNfXONMYJIMH4bRnWG7WgMuRxx/2G02fzpARQI+zT57haKghgyO/TSe/T0C0Un2t+8ZPrDVbRovDCOYw8HtSvAaEBdSJwCoVwr6EJmAQ7DBjO9JFuKHE/BEB8ak3FjZZdbtJkU/Kb9eTJ3aHsZBSQcXzQcovcOX8tHMoIHvVoXdczQxZvP9NfBM3mJ5rdfh8BdzeQ/u+TLNMSHxJ4M8ZaVxf0zlPIPU4BLKQ==
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementMapping all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementMapping/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementMapping all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      