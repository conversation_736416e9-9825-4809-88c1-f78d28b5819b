---
id: delete-settlementsplitconfig-splitaccount
title: "Delete settlement split config settlementSplitConfigId SplitAccount splitAccountId"
description: "Delete settlement split config settlementSplitConfigId SplitAccount splitAccountId"
sidebar_label: "Delete settlement split config settlementSplitConfigId SplitAccount splitAccountId"
hide_title: true
hide_table_of_contents: true
api: eJy9lMFu2zAMhl/F+E8boNbdjroFa7AG2IBhaU9BDqrFJEJtSZXkYIGhdx9oJ3HSNgMGrDtZlij+5EdSHZynoJJxdqYhoammRHNKqaaGbIq+NqlydmXWc16qqnKtTRBIah0hFxhte4MvThOWAl4F1VCiwEYdrGoIEvGlMTueaQgYCwmv0gYCgZ5bE0hDptCSQKw21CjIDmnnezcpGLuGwMqFRiVItK3RyFmMSux/MkT7LgJfp9/nk4f7u4PrDSlN4S+c57xk4+idjRT5/PPNDX80xSoYzzWBxLytKoqRtV+c3Pa1KkamRZ90MZSruMC6mJ+QKV5haiht3NgJEAMziVJ5U44+r/qbV4NW2V0Qy+WpWtmdy2UIRArbQ5O0oWaQKfkoy3JNTWz9OihN17yuatfqa+U9GJyxK9czNanuy0FN9GpXjO1YTH7MIMDeB17bT8gC3sXUKMt395V8F45npTqWPtGvVPpaGcuh9Pl2e8ALKG96IG8ihoC8PD2nsfSW5+EsBTYuJhbpukcV6SHUOfP2c0thB7lYCmxVMOqRYS46aBN5rSFXqo70Kp/K2USWB+PDz32/fyz4VXgrz/2msjuuh6pb/oPAE+3+8CbwrP3PMM6R5WUWh5n+50AGxZMX5BgNt/ZxBG+n36b3U+T8GxXU+VU=
sidebar_class_name: "delete api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete settlement split config settlementSplitConfigId SplitAccount splitAccountId"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/settlement-split-config/{settlementSplitConfigId}/SplitAccount/{splitAccountId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete settlement split config settlementSplitConfigId SplitAccount splitAccountId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementSplitConfigId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"splitAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      