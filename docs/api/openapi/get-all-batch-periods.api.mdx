---
id: get-all-batch-periods
title: "Get all batch periods"
description: "Get all batch periods"
sidebar_label: "Get all batch periods"
hide_title: true
hide_table_of_contents: true
api: eJztV8lu2zAQ/RVjTi3A1ml6080pAidAFyNOToYPY3EsM6VIhhwZNQT9ezHypsRu6wbIzScJ4ixvFj7o1eADRWTj3a2GDArigbVXyPliRNF4nUABY5Egm0DnM0wVBIxYElOUwxoclgQZDK+/jQcP9zegwDjIYEGoKYKCSE+ViaQh41iRgpQvqETIauBVEM/E0bgCmmYqxil4lyjJ+eXFhTw0pTyaIFghg3GV55QEXu4dk+M2Ev3ifrAomevDDH72SDmDghClbDbr+GkTam84894SOmgUlJQSFnSIU4GrrMWZpXVFjQKN3E2HMeJK+sBUpn/DmO3bK7M4zDf3sUSGDKrKaMHW8fji9YkYaY6V5ePFzqMv7015WiT2J5uGFuONr2LH3Dimol2NXWHG8edLAZJHQiY94L/2QSPTBxYQMiivzdy8zudqdVIZe/PrEo09yScRs6WSHI+inxtL6fUbUlLMF+j4qkrGUUrf8cT+bx1/uJnHqI0rjm/YgSNW7Me7Co4tjRhpbeRWoh114M7RJlLAhm3LC8Tjl62429xyGcWL1P8RtkNLbxbwq0m8fb9Bpy3FdekhWJO3/Nl/TP5MO2faOdPOmXbemnba/5wz35z55sw3Z755c75pmvZCddXXkLiH1vbaq9kLO6lYEi/8RkjKXJEXkEEfg+l30vWHrcwEWZu43ArIKlrRi8whZf1+QWWqQhFR00d5z62v9EcMAUQfGjf37Zh25ZQp4Kq3731vMLoFBRJ9DXn5SdoVfOISW97cCtY/lPKs4N1CdBRmo9aQ602ZE8BgQD2TyQo2pU4VLHxisarrGSZ6iLZp5PNTRXEF2WSqYInRrGc5mTZqK52lN9okOdC7Ob5At9PA8O5uo7Pf90S6H0P9k1bPhfoSbSVm0Kh6czrIcwrcOeuGmHYGPby+h6b5DQgFtqg=
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get all batch periods"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/BatchPeriod/GetAll"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get all batch periods

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponseListResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponseListResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponseListResponseHandler"}}}}}}
>
  
</StatusCodes>


      