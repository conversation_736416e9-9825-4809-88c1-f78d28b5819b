---
id: get-settlementsplitconfig
title: "Get settlement split config settlementSplitConfigId"
description: "Get settlement split config settlementSplitConfigId"
sidebar_label: "Get settlement split config settlementSplitConfigId"
hide_title: true
hide_table_of_contents: true
api: eJytk01v2zAMhv+K8Z42QK27HXUrhiLLYcCwtKcgB9ZmEmG2pEp0sMDQfx/ofG1dW2DDTpZF6iX5kBwRIicSF/y8hcWGZcEiHffsJcfOSRP82m1gILTJsEtc7Au1fwotY2UQKVHPwkmdRnjqGRb5ubOKzVsYOA+LSLKFQeKnwSVuYSUNbJCbLfcEO0L2cZKR5LwmsQ6pJ4HFMLgWpZhzpNndl8Xtw/3nk/SWqeX0F+KlrNQ5x+AzZ7V/vLnRT8u5SS4qI1gshqbhnDX2M8uMpbrUW030qgO+6nUOPcs2HNHDHIhY1BRdfXl0NYldHcTq8RW1AoPMaXfqwZA6BSESs63rDfd5iJtELV/ruenC0F5TjNDCnV+HiYmTbsLJfY60ry7drm6/zmGg6od6dx9QDGLI0pPXt6dO/BOH31ieeyP8Q+rYkfMaaypoPDJagqKbKn6REgzsa9FWBtuQRTXG8ZEyP6SuFL1+GjjtYZcrgx0lR48KYzmidVnPLeyausx/pNsEL+x1MN99O87b+0qX5qUyjpfk98qTukH/YPCd92+sTFkVc5rq/57SIfQvO3ROS4fjPKOzu3uU8hPCa3bZ
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settlement split config settlementSplitConfigId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settlement-split-config/{settlementSplitConfigId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settlement split config settlementSplitConfigId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementSplitConfigId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      