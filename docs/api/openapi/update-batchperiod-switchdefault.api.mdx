---
id: update-batchperiod-switchdefault
title: "Update BatchPeriod SwitchDefault"
description: "Update BatchPeriod SwitchDefault"
sidebar_label: "Update BatchPeriod SwitchDefault"
hide_title: true
hide_table_of_contents: true
api: eJztWE1v2zgQ/SvGnLa7dN0mjburm5MGTQ+7NRLnFPgwFkc2uxTJkqOkhqH/vhjJsRXH6Dpd7KGAdZEgzufj8EkzK/CBIrLx7pOGDKqgkekcOV8EisbrmwfD+UJTgZVlUMA4T5DdQSMybkRgqiBgxJKYoiyuwGFJkMHHyz9vRreTK1BgHGSwINQUQUGkr5WJpCHjWJGClC+oRMhWwMsgmomjcXOo62krTInPvV6KRO4dk2N5xBCsyZvoB1+Sd/LumSk/+0K5hB6i5MqGkqzOtglI5rueFRQ+lsiCSWU01LUC1NqIL7TjjqkCbSIFbNiKfgtYB56J/9Cid93m0Zhi+sY/WchdsH/97WeKXYxFSsG71AZz8uaN3DSlPJogPsRSleeUEqhuhTX7FCyaFyWb1qa2gjPvLaGDWkFJKeGc9mHgKmtxJnnJsagVaOT/AVvV1bjw+sBY1hywN6ki+nJiysMssT9YtCWhK1/FjrhxTPOGRzaJGcenJxJIHgmZ9Ii/i4OQXJ8lCNkQr01hfkznfHlQGlvxyxKNPUgnEbOlkhyPoy+MpW49YYy4FFplKtO/V0hJMV+g4/MqGUcp/YUH4v+o+NnNPEZt3Hx/hT1TxIr9zSaDfUVz6An/SHyzC8X1+jTLVuy4foHZDmtsDf43/cf7FTptKT4jzpfS5pFJjkxyZJIjk8Qf/2s8UsiRQo4UcqSQ2HRCzdnptj23Tcff66j32p7qw6bnL4kXXqYDQYRkP5EXkMEAgxl0FAe7ioni/eNIoIpWJgDMIWWDwZzKVIV5RE2v5Tm3vtKvMQSQjl8o7nrb9V9+wzJY2sMucFrg72fF8F3/7P3b9/13Z8OT/uy0yPsn+R/D02I4xAKHsi/GFb7Z9w1gZQq47G03szcafwIFEm8LzP1bUQw+cYkN4a6HGgcA9gThTbF1eslatXis1lDeAQYD6slURcFTs1MFC59YhFerGSa6jbau5fXXiuISsrupgnuMpq2/u2mtHgcugr82SRb0pnZ2gtw0vfDL9Xo686onA599wf9Ny6fjnXu0lYhBrVbr1YvWYH8iBrYSz/6GtxqjPKfAHdmu02mnDsejyYV4na1HQmXz2YCIDzJbwgcJRIFvcmsop3m3AotuXjWfO2hdy/UPnnavWQ==
sidebar_class_name: "patch api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update BatchPeriod SwitchDefault"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/BatchPeriod/SwitchDefault"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update BatchPeriod SwitchDefault

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"SwitchBatchPeriodToDefaultRequest"}},"text/json":{"schema":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"SwitchBatchPeriodToDefaultRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"SwitchBatchPeriodToDefaultRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      