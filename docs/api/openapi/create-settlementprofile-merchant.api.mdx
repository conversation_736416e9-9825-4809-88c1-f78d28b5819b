---
id: create-settlementprofile-merchant
title: "Create SettlementProfile merchant merchantId"
description: "Create SettlementProfile merchant merchantId"
sidebar_label: "Create SettlementProfile merchant merchantId"
hide_title: true
hide_table_of_contents: true
api: eJztWEuP2zgM/isBT/tQmnamk259SwdFO4dug8nMKciBsehErS2pkjxtYPi/L2g7sfPoNgl6KNDkYkemPlHkR4lkAcaSw6CMvpMQQewIA00ohJQy0sE6k6iUPpCLl6gDCAi48BBNoZUZ1zIwE2DRYUaBHIsUoDEjiCBrZt9JEKA0RGAxLEGAoy+5ciQhCi4nAT5eUoYQFRBWlmf64JRegIDEuAwDRJDnSkJZig34u7cfJqPHh/dr6CWhJHcCeFnOamHy4Y2RK5aIjQ6kA7+itamKKwsNPnmjeWwPysw/UczWsY7tGRR5/rre+JvcK03e/1upvL87nacpzlOqVS0FYB5Ma+DOlLkxKaGGUoDyI5kpfV9rfljGbzBGcWzyLajv6DxH/fnWyOP0ZOGjN8XCjRpnzTFHzVB+7FSGbvUjizS0ZdofwTdAKRWTANNxx14Jpp4EBBVYBRhJOdk1+dpBp4A8WolhE3V7odaFDPQtXIh5IeYvR8zuwfnX3xeGXhj6azGUQR15a7Sv3Xf1/Dk/JPnYKctrQQSTPI7JexDdnKA6c22K6iRK+wbqoHEz8h4Xx3lWYjhmubPcJc6PvPXEj3pu0EmlF4dXPCNkj/X/Owr/4/na1z8Xbf18j1qm5PZOvlPPvQtJfhOSnJW3XdjxW7Cjupu276HbqjDv7YH01ibrbVXZGYWl4YLeGl/5lQvuCAZo1WAPY7CeOihakBL4xndP61o+dylX1yFYHw0GC8p8bhcOJT3j9zg1uXyG1gJX00zr+7aifvsNM5vS99O61rG7fqxL9918rSnoD2VobQ7Wgrap1vbYVkZ1+JPpfujkR7sadOIDrhP85yYZvuzfvHrxqv/yZnjVn18ncf8qfj28ToZDTHBYMUjppE7MNmTJvMVVx8m90fgOBLATaho8veD4YqdmWJ0cTRfkRHpscWsTK520phS1w4uGOVNAq0Ac6Pq0QQsCos4qMwFLJl80haKYo6dHl5YlD3/JiW04nQl4QqfqQJ4WIJXnd7mJoh01NxkY/HHfNHf+7HFL6pD6zSDqFRsQ05z/gYDPtNpuSZWzUqy7Rj9di3q1To9qowm3sOqvtzVg/4EBWom9LKKdMYpjsqEj21101on+8cfJAzO6aWtldWA4/Mr9MfxaW8RUW6sO/GqsgBT1Iq+uEqhX5t9/fzotHw==
sidebar_class_name: "post api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create SettlementProfile merchant merchantId"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/SettlementProfile/merchant/{merchantId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create SettlementProfile merchant merchantId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"isAdminRequest":{"type":"boolean"},"settlementAccount":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"settlementProfileId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementAccountRequest"}},"additionalProperties":false,"title":"AddUpdateMerchantSettlementProfileRequest"}},"text/json":{"schema":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"isAdminRequest":{"type":"boolean"},"settlementAccount":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"settlementProfileId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementAccountRequest"}},"additionalProperties":false,"title":"AddUpdateMerchantSettlementProfileRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"isAdminRequest":{"type":"boolean"},"settlementAccount":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"settlementProfileId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementAccountRequest"}},"additionalProperties":false,"title":"AddUpdateMerchantSettlementProfileRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      