---
id: update-settlementsplitconfig
title: "Update settlement split config settlementSplitConfigId"
description: "Update settlement split config settlementSplitConfigId"
sidebar_label: "Update settlement split config settlementSplitConfigId"
hide_title: true
hide_table_of_contents: true
api: eJztVstu2zAQ/BVjT33IcZ5Oq5sbBGkOBYI8ejF8WIsrm6lEMiSVxBD078WStmQ7TZsWvSW+SCDp2d2ZEXdr0IYseqnVuYAUKiPQ0xV5X1BJyjtTSJ9plcsZJOBx5iAdQ7d/xfsnWhBMEjBosSRPlg/VoLAkSMFtH2awcwEJSAUpGPRzSMDSXSUtCUi9rSgBl82pREhr8AsTYLyVipPItS3Rc66VFNA0SRvp7PTb1ejm+usKek4oyP4FeNNM4mFy/osWCz6RaeVJeX5FYwqZBbIGt04rXnsCpae3lHlIwFim1ktyvBtTfFqNqooCpwXF1JoEAuPX4VQNpKqSCc8LDJBkM1IeZ4HubSgvPePAVYuwghtlma6Ud2sJoLW4YKI8le7P6QeY71hU60WoqpwGfltJhK64Fo7bqr4MzvZ6gZiAQkhmGIuLtQxyLBx1JY6E2PLgMsplFI8z2GL2pdA3W19A6/AWuknA06N/s8CrtsD6ZfDh45sXXq8XGMuSM1q5yM7+7i4/BLnMSsMhWIYqy8i5UNTmTgzR6yjqBXp7se32nu+fJfm5FrGHZtxEQy9NYYBGDrq/9QNcP8IN6mfwGmCV7P2qe1e24BbqvXHpYDCj0lVmZlHQDr9nha7EDhoD3DLZ/Zdd2zx9xNIU1Pm8E3jNzisXb1lyvOmu3We8Awc5fjrKh4f9o+O94/7h0XC/Pz3Is/5+9nl4kA+HmOMQmknDs0Cug9mWkp5R6Qwuep2ovdHFOSTAtUdJ7vfYMkY7X6Jaq+OfpdpQvPV9aCOmQKk4XGC8Xoo4BjQS1ovfkBESSJ+LNklgrp1njLqeoqMbWzQNL99VZBeQjicJ3KOV8XMY1yCk43fRun8r3XYGgneXy1HqfQ+SX5exulEU3yf3UUWABH7Q4jfTYFBqObD995Ri6LXxsE2Lp8e4exIB+8Gc3YknMx/7vf3wLkbXJ4w3XQ6LJU/CKVh84KkTH2LlOmQdroawVkOBalbxvZ1CBOXfT1vxDsw=
sidebar_class_name: "patch api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update settlement split config settlementSplitConfigId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/settlement-split-config/{settlementSplitConfigId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update settlement split config settlementSplitConfigId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementSplitConfigId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"splitType":{"enum":["flat","percentage"],"type":"string","title":"SplitType"},"splitAccounts":{"type":"array","items":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"},"nullable":true}},"additionalProperties":false,"title":"UpdateSettlementSplitCodeRequest"}},"text/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"splitType":{"enum":["flat","percentage"],"type":"string","title":"SplitType"},"splitAccounts":{"type":"array","items":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"},"nullable":true}},"additionalProperties":false,"title":"UpdateSettlementSplitCodeRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"splitType":{"enum":["flat","percentage"],"type":"string","title":"SplitType"},"splitAccounts":{"type":"array","items":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"},"nullable":true}},"additionalProperties":false,"title":"UpdateSettlementSplitCodeRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      