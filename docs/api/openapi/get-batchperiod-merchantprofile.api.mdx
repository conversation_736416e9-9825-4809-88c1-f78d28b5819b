---
id: get-batchperiod-merchantprofile
title: "Get BatchPeriod MerchantProfile settlementProfileId"
description: "Get BatchPeriod MerchantProfile settlementProfileId"
sidebar_label: "Get BatchPeriod MerchantProfile settlementProfileId"
hide_title: true
hide_table_of_contents: true
api: eJztWE1v2zAM/SsBTxugNV138y0diraHbkE/sEOQA2MxiTpZUiW6WGD4vw+yE9dN3DXtOmAFfLITUY/kE/1svAKsI4+srDmXkMCC+Bg5XTryysoL8ukSDTtv50oTCGBcBEgmUAWNqyCYCnDoMSMmHxcLMJgRJBCIWVNGhsc1wLkEAcpAAg55CQI83eXKk4SEfU4CQrqkDCEpgFeugmCvzAIEzK3PkCGBPFcSylI0WU5PLq5GN9dnG+gloST/AvCynMbg4KwJFOL60eFhvEgKqVcukgMJXOVpSiGAgNQaJsMVEv3iodMYMxe7GezsllIGAc5HnlnV+GEN9RA4s1YTGigFZBQCLqiLBJNrjTNNdUelAIm8T7qOc3ie4lhJffzHeVCGQvhWEb5HWZuN383MopfKLLoz7mzEnO1VU203P7OH0WsziN7jKs4AUxae56SFsicbrR1frdzzfGiOuX6ikbm32bXak1K2e4fWz+6ZzX0rXBmmRfVYNI0pw1+OYiGpJ2SSI/4jDxKZPnEsIp6wlWquXrfneLXfEDXhJxkqvdeenUn/iwn5H6c/BkmpoiKhHrfKnaMOJIAV60oTia+2qbhcK1w8iq3UL4BtCf/bAO7U+UPxsjPLPwDdXM/QSE2+zuGcVmn1ShzeBtsLey/svbD3wt4L+3sX9upbvVf0XtF7Re8VvVf0d6/oZVk9tm2P5pR40No72BhYa+hBtyOVES/t2v2KsxW9qQSG6NSwBTbcAhsWHWglxMn29xsnLPc6WlLMLiTD4YKykLuFR0kH8T7VNpcH6BxEC0qZua0mqSEnCw5XgweGBqPxOQiI6HW/95/jiTobOMPqtbbxxF7FwyMum4luWVylqBsq1hxNAJ0C8cgNFLCVDAQkXfmmApY2cEQpihkGuvG6LOPfdzn5FSSTqYB79Koe1kkBUoV4L5sx2iq48eXgw+Xa+/s4iKZlVyMbpTFRZ+5R5/EXCPhJqyesy3Jaio27+Obl1GlbXmZTUrQ669VRmpLj1lobYtqa49OTayjL3/Vhvnw=
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get BatchPeriod MerchantProfile settlementProfileId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/BatchPeriod/MerchantProfile/{settlementProfileId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get BatchPeriod MerchantProfile settlementProfileId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementProfileId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"batchPeriods":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponse"}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"batchPeriods":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponse"}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"batchPeriods":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponse"}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      