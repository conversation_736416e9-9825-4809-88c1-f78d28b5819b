---
id: update-auto-settlement-charge-setup
title: "Update auto settlement charge setup"
description: "Update auto settlement charge setup"
sidebar_label: "Update auto settlement charge setup"
hide_title: true
hide_table_of_contents: true
api: eJztWE1z2zYQ/SuaPbUpGDl2rLS8KZ5M4kNbj2WfPDqsiKWElAQQAHSi4fC/dxakJNqSG6nj5JBIF1HQfr59C2JRg7HkMCijLyWkUFmJgcZVMBMKoaCSdLhYoJvThEJlQUDAuYf0Dp6WmQqw6LCkQI5Fa9BYEqTg+e9LCQKUhhQshgUIcPSpUo4kpMFVJMBnCyoR0hrC0ka14JSeg4DcuBIDR1kpCU0j+pa7QK6cyVVB38TL+3d/Tsa3Nx9WpheEktwBxptm2gqTD2+NXLJEZnQgHfgRrS1UFosx/OiN5rUtU2b2kbIAAqzj0gVFPsrtQODryQnIYuF6oroqZzGptag01awgRgJQSsXhYXHV855j4UlAUKFgC7dPcui6TT2aCvQl/PhZ9kv64rcfPF025shbo30b/+nJCX9J8plTln1ACpMqy8h7EH3qRzbYAtVh+HSmNoIzYwpCzSCU5D0+QGENmK6KAjn92K+NAIlhD3fq2dGPwlpTsVeUW3T4esglOfYQ3lZeafL+r7iT7eFrpfi3nhl0Uun5bu5tKeIDiuwqzb6Ue09h8jjj645eETpHGEiOw39WhWmbBFVGldJIlatDdA4IdndzrOJ9JkOr7w+oZUFua5M5eIs5ttCxhY4t9P9OI8feOfbOz9478dT38ITXnhUHXIrBhjSDlpgD3w2SJYWFke2ElvGIFie1FIZo1ZB1k41u0uomUdcP626SbIYrnsSlx2flBpi07n41hlau4KktBOvT4XBOpa/s3KGkl/ycFaaSL9Fa4CmN94LrzaT27guWtmX8zkM5nOX4+3k+ep2cv3n1Jnl9PjpNZmd5lpxmf4zO8tEIcxzBpjlPGp4hcxN5sIa+9BaXgw32g/HVJQjgBFpk718xjazxocS4VXWT6X6IP6jSmn+9o3cjWozqrhZ3gFZB21RPVgMEpJvJflWQbnULqamAhfGBbdf1DD3duqJpePlTRW4J6d1UwD061Xb1XQ1SeX6Wa6o+SmM9RcAv190c/uuALyp2pdctol4yrlhU/AsE/EPL3gUFz/zf1+0WTs20EasLhmdHoXXbu85Yh8SZt/9etAaTGzawkdg6a240xllGNvRk+06nvYa/Gt9csNdZdwVSGsniDj/zXQp+brExMbf4GolrNRSo51V8p0Lrmj//As5smw8=
sidebar_class_name: "patch api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update auto settlement charge setup"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/auto-settlement-charge-setups/{setupId}/merchant/{settlementProfileId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update auto settlement charge setup

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"setupId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"settlementProfileId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"}},"additionalProperties":false,"title":"UpdateAutoSettlementChargeRequest"}},"text/json":{"schema":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"}},"additionalProperties":false,"title":"UpdateAutoSettlementChargeRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"}},"additionalProperties":false,"title":"UpdateAutoSettlementChargeRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      