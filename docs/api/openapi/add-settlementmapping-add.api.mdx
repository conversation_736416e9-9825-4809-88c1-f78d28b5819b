---
id: add-settlementmapping-add
title: "Add SettlementMapping add"
description: "Add SettlementMapping add"
sidebar_label: "Add SettlementMapping add"
hide_title: true
hide_table_of_contents: true
api: eJztV01T4zAM/SsdnfYjpXyW3dwKy7Ac2O1QOHV6UGOlNTixsZ1CJ5P/vqMktIW0DLDDrb00Ez09S7aeI+WgDVn0UqcXAkJAIQbkvaKEUp+gMTKd9ISAADxOHIRDWJovKzOMAjBoMSFPliE5pJgQhHB+djno3Vz/hgBkCiFMCQVZCMDSfSYtCQi9zSgAF00pQQhz8HPDns5bZi6KUQUm50+0mDMi0qmn1PMjGqNkVAbfuXU65XcNKj2+pchDAMZyql6SK3GLNPpWx1IRp/9y/QBibRP0EEKWSQFFUKfWBKaZUjhWVKVUBFBv3goWrcU574WnxL0nwl4U6Sz16yNsLNzw+/PWkJueWTIm+07fE0zvPrAku51q8UY3ndmI2MV9fIPH7wl0/PbwioBlJLksUfVXloxROQrAS89YGCyS+EUepXJlfX2Mq9bipRak/oOmtyr/mvOq0h8wiadHv5XaVmpbqX261FY/bt++bzW31dxWc5+rOaax5IxOXbWD+7u7/CfIRVYaZue0sigi50qNPrf0hGg16FtYds8J+anmBttoVx4V+imE0EEjOw2fTuXjyM6eOurMKm6gvTcu7HQmlLjMTCwK2uHnSOlM7KAxwA0z3xVXy6b57BETo2jjrQAHMf44iruH7aPjveP24VF3vz0+iKP2fvSzexB3uxhjF56uhmVZLG6A4QYxL6EbNPsaoJbmOshSgZusVSWvWFf1NFyVxhIzbvgVo2JU8OgS61IWdRGdU+IMzleOutXrX0AAfFhVJcz2uEj5qBMsr+16716rkGe1tBBh2XAZhTJlxrIK8rp4hoBGQrBmICvVwGPZlIstHEKej9HRjVVFwa/vM7JzCIejAGZoZSWkIedaT2i8R0I6NoiFiF4EuJjD4MtVPc59bfGcuC7wO5o/nwdnqDKGQRHktfW0ImxfM8ES0RjyuMQXcur/HVzz4dXDYVIdoMUHnjLxgVcIQJdBl5Iu3+WgMJ1kOGFsxcm/f8IJTv0=
sidebar_class_name: "post api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Add SettlementMapping add"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/SettlementMapping/add"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Add SettlementMapping add

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"mapping":{"type":"array","items":{"type":"object","properties":{"settlementAccountId":{"type":"string","nullable":true},"settlementAccountName":{"type":"string","nullable":true},"settlementAccountNumber":{"type":"string","nullable":true},"settlementBankName":{"type":"string","nullable":true},"settlementBankCode":{"type":"string","nullable":true},"sourceBanks":{"type":"array","items":{"type":"object","properties":{"bankName":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"SourceBankDetails"},"nullable":true}},"additionalProperties":false,"title":"MappingModel"},"nullable":true}},"additionalProperties":false,"title":"AddSettlementMappingRequest"}},"text/json":{"schema":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"mapping":{"type":"array","items":{"type":"object","properties":{"settlementAccountId":{"type":"string","nullable":true},"settlementAccountName":{"type":"string","nullable":true},"settlementAccountNumber":{"type":"string","nullable":true},"settlementBankName":{"type":"string","nullable":true},"settlementBankCode":{"type":"string","nullable":true},"sourceBanks":{"type":"array","items":{"type":"object","properties":{"bankName":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"SourceBankDetails"},"nullable":true}},"additionalProperties":false,"title":"MappingModel"},"nullable":true}},"additionalProperties":false,"title":"AddSettlementMappingRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"mapping":{"type":"array","items":{"type":"object","properties":{"settlementAccountId":{"type":"string","nullable":true},"settlementAccountName":{"type":"string","nullable":true},"settlementAccountNumber":{"type":"string","nullable":true},"settlementBankName":{"type":"string","nullable":true},"settlementBankCode":{"type":"string","nullable":true},"sourceBanks":{"type":"array","items":{"type":"object","properties":{"bankName":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"SourceBankDetails"},"nullable":true}},"additionalProperties":false,"title":"MappingModel"},"nullable":true}},"additionalProperties":false,"title":"AddSettlementMappingRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      