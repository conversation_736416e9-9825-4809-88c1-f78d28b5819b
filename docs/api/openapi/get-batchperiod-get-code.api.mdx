---
id: get-batchperiod-get-code
title: "Get Batch<PERSON>eriod Get batchPeriodCode Code"
description: "Get BatchPeriod Get batchPeriodCode Code"
sidebar_label: "Get BatchPeriod Get batchPeriodCode Code"
hide_title: true
hide_table_of_contents: true
api: eJztWE1v2kAQ/StoTq20jdP05hupIpJD2ygkp4jD4B1g0/XuZnccFVn+79XYfDiAWpL2yMmGnXk7n088avCBIrLx7kZDDnPiS+RiESgar0fEX70mUMA4T5A/Qnt42x7CREHAiCUxRTmswWFJkMN0a7RyNw5yCMgLUBDpuTKRNOQcK1KQigWVCHkNvAzinjgaN4emURvE0dW38fDh/noNtSDUFN8ENhHjFLxLlOT84vxcHppSEU2QAkAO46ooKCVQUHjH5LhFol+cBYtyc71/g58+UcGgIESpJZsOP62gtoZT7y2hg0ZBSSnhnPbjVOAqa3FqqcuoUaCRj7iuV3Np5D7uzMcSGXKoKqMlht0uHRULzbCyfDipWfTlvSmPQ2J/tGk3i9e+ij1z45jm7QhsEjOOv1xIIEUkZNJD/mMdNDJ9YglCGuK1mZn3+Vwuj0pja35VorFH+SRitlSS49voZ8ZSf54wRlzKRjCV6e8TUlIsFuj4skrGUUrf8cj6rx1/uKnHqI2bH56wPUes2I83GRwaGjHS2sj2ob3thTtDm0gBG7bt/hOPd0txt9pmacXO1W+A7RHaFvDf/NfPa3TaUuzgQrCmaHk2e0r+xCQnJjkxyYlJ3sEk7a+RE4WcKOREIScKeQ+FNE27O33ZMyIe9HwH8nlnIwcrJVcSL/xKKUpXRdPlkGEwWQ8hGxFn9Q5Ek60wEsWXtWSsohU9xxxSnmVzKlMV5hE1ncl7YX2lzzAEEP1m3My3jdtkXKaAy8G2G4Ph7Q0oEPQus5fP0pHgE5fYMuZaUB6f8atSbaamJwob1WVRr6rxCBgMqFdaWcmNoCDf18btY6Jg4ROLc11PMdFDtE0jXz9XFJeQP04UvGA03WA91qBNkne9mYOdODcCFj7crUTyx4Eo+UPxr5fYyQq/oK3kEyj4ScsDer6ZNGotwf97KN2VPcG/CUf+D+hOh0VBgXtnfYhJb0hHV/fQNL8BnPHrBw==
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get BatchPeriod Get batchPeriodCode Code"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/BatchPeriod/Get/{batchPeriodCode}/Code"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get BatchPeriod Get batchPeriodCode Code

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"batchPeriodCode","in":"path","required":true,"schema":{"type":"string"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      