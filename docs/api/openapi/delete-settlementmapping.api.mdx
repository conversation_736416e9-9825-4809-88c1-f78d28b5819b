---
id: delete-settlementmapping
title: "Delete SettlementMapping settlementMappingId"
description: "Delete SettlementMapping settlementMappingId"
sidebar_label: "Delete SettlementMapping settlementMappingId"
hide_title: true
hide_table_of_contents: true
api: eJytk09rGzEQxb+KeacGRDbpUbdATGtooNTJyexhshrbortaRZo1NYu+exn/Sxo7h0BPq9WM3sz89DSij5xIfB9mDhaOWxaes0jLHQfpKEYfVjAQWmXYBV5jD4dYbRApUcfCSVNGBOoYFvl96szBwAdYRJI1DBK/DD6xg5U0sEFu1twR7AjZxp2EpH35ZZ86ElgMg3coxZyqfJs+zO+eHr8fpddMjtMnxEupNTnHPmTOGv96c6Mfx7lJPiocWMyHpuGctfa7yP0O2uSMzOQygI5l3b/ChtnTsKgo+upMpRovyBQYZE6bI/EhtTq6SMy2qlbc5SGuEjm+1nXT9oO7phiho/qw7HcUvLQ7gNzlSNs3/U/ufs5goOr7CTe3KAaxz9JR0LMH9p+c/B9sp2sQ/iNVbMkHLbKbZDwgWYCih7lgOgN7qUZtsO6z6MlxfKbMT6ktRbdfBk5b2EVtsKHk6VlnX4xwPuvawS6pzXzWZNMH4aDO+/LrYKirib6HS80fNilsFR+1g/7B4DdvP3gPpS7maNn/3s6+7JsHcmpJfXDy4f30x/RxilL+AsoeZ3c=
sidebar_class_name: "delete api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete SettlementMapping settlementMappingId"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/SettlementMapping/{settlementMappingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete SettlementMapping settlementMappingId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementMappingId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      