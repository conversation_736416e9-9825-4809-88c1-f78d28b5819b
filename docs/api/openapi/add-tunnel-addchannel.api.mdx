---
id: add-tunnel-addchannel
title: "Add tunnel settlementTunnelId add channel"
description: "Add tunnel settlementTunnelId add channel"
sidebar_label: "Add tunnel settlementTunnelId add channel"
hide_title: true
hide_table_of_contents: true
api: eJztV01v2zAM/SsBT/tQm25H37KuaHsYVrTdKciBsZhEnSypEt02MPzfB9pO4jbBkAzdpWguVmT6iXx8ksgKfKCIbLy71JABan1bOkd2pHW+QBmBAsZ5gmwMN8RsqSDHrRFMFASMWBBTFIsKHBYEGaQXlpcaFBgHGQTkBSiIdF+aSBoyjiUpSPmCCoSsAl6GBoGjcXNQMPOxQIYMytJoqGu1XuT87MfN6NftxQp6QagpHgBe15PWmBJ/83opFrl3TI5liCFYkzfkDO+SdzK3BeWnd5QzKAhRqGRDqYHpyNsRkCutxaml1ru6oZAdxVOvaQ/7WkmWjHiF9qq36AxtIgVsWGxhpPVp68TLtF23EQuXwPTEbza4fgI/fX6bUQpYpBS8S63bX09O5KEp5dEEWQMyuCnznFIC1dd3k/tg0RxES+qgNoZT7y2hg1pBQSnhfB8OFGjkPZYzeo9DQf3HZCkw6TvNsLS8O2aTRjmbB9r9No+ETHrEfw1DI9MRm4IaEr02M3PIN/sK6px4S1DXnXheB2X1vECnLcWtbXjoJnxX27vaDlPbP91o7zJ7l9khMmuu3edX7EjrATeX82C7+h2g1oNNQV0QL7zU28GnRh1SFGcwxGCGLcaw2gaph6j10QYlUXxY1d1ltFIBM4eUDYdzKlIZ5hE1Hcs4t77UxxgCSMUrm+N6U/WePWERLD0reTaZeaardeEsFffMN5lc81ekgMvBhr3B6OoSFIiLLUMPX+RDCbnAZnd2dfwhzD3jfK2jXiVTq5aLqiN1DBiM9DBlh5Dt7E36zE4ULCQv2RiqaoqJfkVb1zJ9X1JcQjaeKHjAaNoNNa5AmyRjvVbVCzfXRRd8uO56k48DcWqX+90kuqWwh7aUf6DgNy13d1b1pFar5ufVvWlX7bVaa4+kE2vfnraAR7cCsLHYuvc3X4zynAL3bPuLTnob5OrnzS0omHbdWdHKMOKjtHn42DLjm9Cak7WZq8Cim5fNmQ3tyvL7A+a5Pa8=
sidebar_class_name: "post api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Add tunnel settlementTunnelId add channel"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/tunnel/{settlementTunnelId}/add-channel"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Add tunnel settlementTunnelId add channel

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementTunnelId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddChannelSettlementTunnelRequest"}},"text/json":{"schema":{"type":"object","properties":{"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddChannelSettlementTunnelRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddChannelSettlementTunnelRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"}},"additionalProperties":false,"title":"GetChannelSettlementResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"}},"additionalProperties":false,"title":"GetChannelSettlementResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"}},"additionalProperties":false,"title":"GetChannelSettlementResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      