---
id: get-serviceproviderconnectormaps
title: "Get service provider connector maps"
description: "Get service provider connector maps"
sidebar_label: "Get service provider connector maps"
hide_title: true
hide_table_of_contents: true
api: eJztWFFv2kAM/ivIT5sUSte95Q1NFa20Vqi0TxUPJmfguuTueuewMpT/PjkJIRTaMVRpe+CJkvizfZ99H7VXYB15ZG3NtYIYZsQj8gudkPN2oRX5xBpDCVufoQsQAeMsQPwItdmwNrvV7tva8gYdjCNw6DEjJi/2KzCYEcQwxBnd5tmEPESgDcTwnJNfQgQhmVOGEK+Al05MtWGalXZTCc/Vo68XUBTRlr+R/kUf421weTPqP9xfrb3NCVWJ8fSca08KYvY57XEf2Gszg6IYi3Fw1gQK8v7i/Fw+FIXEaydMQwyjPEkoCJ+JNUyGS0/0wj2XokRe7UawkydKGCJwXorGuvIfalcbw4m1KaGBIoKMQsAZ7eYZgcnTFCcpVScqIlDI7XDoPQqRmikLf05Dq31BGqrzXCtJKGy3zfVRqNuyWAccybS6UgBk8kya1+hJSf5PYXgc7ThizeIF2l0dJJHEEzKpPr+btkKmLuuMyhJYpaf6bzBFBKiUllbBdNhieYppoE12g+ayvnEL7+o+lDR2uHGbi3jIJakA5U07zHyqfeDhG823qbDX+wqX4vFYtoypgMOBmZaAO0qsV4dCDL0cnZ/ztNA2D8fhP7w5vuuKarV+cIVGpeSrPnQu1Un5+9B7CvYkTCdhOgnTSZj+vTCV/yudFOmkSCdFOinSf6BIhUTbnvEGxJ36NnbW03SnGac79TydEc9tPXqLUiDPIYYeOt2rwd01uNuAuzVYLNbzde5TGViZXYh7vRllIXczj4rO5O8ktbk6Q+dABlRtprbks2EgCw6XnRExp5SR4U5/eA0RiPfqNIsvZWPbwBmWoruemA865RYzTR1bA28RVQdY1Qw8AjoNjZy9ycE4grkNLIDVaoKBHnxaFPK4WgQIM0oH6QrVFP4HLV9vIhaY5pISyDLgHUC9atiYj+WL11XbPY6LaL0z2Bv5FQ/N8A+f7uoFw+cORPv5qZJobSi2cq7e9pOEHLfetV2MW902uLyHovgNvWo+Kw==
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get service provider connector maps"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/service-provider-connector-maps"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get service provider connector maps

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"serviceProviderId":{"type":"string","format":"uuid"},"serviceProviderName":{"type":"string","nullable":true},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"serviceProviderId":{"type":"string","format":"uuid"},"serviceProviderName":{"type":"string","nullable":true},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"serviceProviderId":{"type":"string","format":"uuid"},"serviceProviderName":{"type":"string","nullable":true},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      