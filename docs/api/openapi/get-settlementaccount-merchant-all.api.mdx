---
id: get-settlementaccount-merchant-all
title: "Get SettlementAccount merchant merchantId all"
description: "Get SettlementAccount merchant merchantId all"
sidebar_label: "Get SettlementAccount merchant merchantId all"
hide_title: true
hide_table_of_contents: true
api: eJytVE1v2zAM/SsGTxsg1F138y2HIsuhQ7G0pyIHxmZsYbasSlQwz9B/H+iPfGPYgJ5sS+Qj3+Oje2gtOWTdmlUBGZTEa2KuqSHDmOdtMPxELq/Q8KKuQQFj6SF7g2PYYgyDjQKLDhtichLSg8GGIINmAlgVoEAbyMAiV6DA0XvQjgrI2AVS4POKGoSsB+6sZHp22pSgYNe6BhkyCEEXEKM6gD9jSd9DsyU3g78Hch3cQNOGqRziDnDa8NeHK7y1/k0fg7Z8fFovXl++zWgVYTHk/Cv1GDcS7G1rPHm5f7i/l0dBPnfayuQgg3XIc/Jeal/cLImTq1kl80iS42wSHObbEFftZAVQ46QySNHq9AomnbPT/ogT0xHIk9vPRgiuFvLM1mdpWlLjgy0dFnQn73ndhuIOrQUhq82uHXTQXA8SUuMtdickksXzChQI+shx/wWiAtt6btBI7qz+f3I/U+4wCaZfnNoatZEqA5V+0uUN0GpQN5ZBHVwPCrKzDZBaGwVV61kQ+n6Lnl5dHaMcj34T0QrtcVuLRXZYe1Lwk7pLw++xDtIjiOf+kjA5+hi+kQ+nJf52sQst8tYwGTH5px+TdT8n8ju4pdF0iKY7rTn3cyJG3EQ178SHdzFWO9nAM/YHny8fXyDGP9TBthM=
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementAccount merchant merchantId all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementAccount/merchant/{merchantId}/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementAccount merchant merchantId all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      