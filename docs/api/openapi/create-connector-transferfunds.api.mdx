---
id: create-connector-transferfunds
title: "Create connector connector transfer funds"
description: "Create connector connector transfer funds"
sidebar_label: "Create connector connector transfer funds"
hide_title: true
hide_table_of_contents: true
api: eJztmt1v2zgMwP+VgE/34S673Vveuqy3FbfrBU12wKEIDozMJNpsSZPobkGQ/32gHX+0STs72w7Y4L7UkUiKpsWfZUpbsI48srbmMoYRKE/INLbGkGLrZx5NWJJfZiYOEAHjKsDoBioBmEfg0GNKTF66tmAwJbFUiUSgDYzAIa8hAk/vM+0phhH7jCIIak0pwmgLvHGiGNhrs4LdLqpsvbz4a3r+ZvaqNLUmjMl3MjYvhCnwcxtvREJZw2RYLtG5RKs8CsO3wRppOzBlF29JMUTgvMSMNYVczmZe0aUJrDkTC8rGdOhBBCZLElwkVPi6iwBTmxXjf1Z0QYaWWmn0m3OlRO0qj8xpqlm6IN9V+Tmad/+Q18t9oE6z8ue/49d0S0krPbVGYygZtw2o9XqlDbL1XYN0qNn+7mrdLwhRbaRThGIKLGqSvvUMbB2wFE2MTNe0JE9GUQeHJTMvjGTf5pqWHe/xCn3BnFZ6DjcpGa68bKXEQi5UJdi6aby2qr17jdnd7b4WOknIt/JOaBHHWkxjMmnwZ4lJoAhYs8jCH5mJS2ZfF7gTkALTR+7J1pOtJ1tPth+LbM2l2y+/9ojrEdcjrkfcD4Q4UfcUnDWhYNazp0/lX0xBee0Kh2GaKUVBPtEbn7X5qs8lqLsxcW+qFlxYmxAakKxuPTspBFy1k42RW/jlif1mfI+62jCt8lLA0voUuWj6/Rk0otY6o5oKL2ipjf620zxQCCdN8ZYaptOM5vD+RcsH8f/F9VuH6GAB0PqmhL0TG5g6uzbLZb8aNma15SkjZyFnh5C9iDqcCKBSWx6Cv9WKJt7e6rjlK4Z1SlPG1B2TrnJVgngmol/m5NXl5L/y+hWaOCF/sDjsvDTsMdhjsMdgj8HvH4OnVf96/vX86/nX8+9751/+BX33a3mc73IPqi3qxhXvbQ7KLe+UeG1la9zZkCeBbGOPYIhODyu14ba63A1LE2elCQlcuTue+UQ2sJldGA2HK0pD5lYeY3oi1yqxWfwEnQPZsBZkX9eb1hcfMXUJPVK3rENblifrloeqkI9K7EtKR2UeKpgdFa4LY3X3nQph3fxAIfAxgYORP1/WOyZ7zMnHinS11EO1uEZm3Cu5HRu/UYGquw9fKXXfPdAd7ajLYUcfzNEx6+JW2baTkxdLm2fxPgNfUhocbgZTYk5IfBycTy4hApnrhcHb30RREifFHPT78xxd8u9O5lYMaZS2dlGRVNt9at4AOl0UwqoDKKPmj3v5OY9gLak9uoHtdoGB3vhkt5PmnJ8wuplHcIteF2C72UKsg1zHFZTu+ViV4OCn6/0BlZ8HMvAx3/eNaDYSOkwy+QURvKPNnVM0u/kuKg++fHUnisEax2wqR+QUTtE7Lgye5e+tWuLgG7vWOFeKHDdkm4POG2id/D2dybTbn8xJi+zy+EGO+OCHIiA2v7Vi6SFtW0jQrLJ8dQnFyPL3CcXSKv8=
sidebar_class_name: "post api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create connector connector transfer funds"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/connector/{connector}/transfer-funds"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create connector connector transfer funds

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"connector","in":"path","required":true,"schema":{"type":"string"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"sourceInstitutioncode":{"type":"string","nullable":true},"amount":{"type":"string","nullable":true},"beneficiaryAccountName":{"type":"string","nullable":true},"beneficiaryAccountNumber":{"type":"string","nullable":true},"beneficiaryBankVerificationNumber":{"type":"string","nullable":true},"beneficiaryKYCLevel":{"type":"string","nullable":true},"channelCode":{"type":"string","nullable":true},"originatorAccountName":{"type":"string","nullable":true},"originatorAccountNumber":{"type":"string","nullable":true},"originatorBankVerificationNumber":{"type":"string","nullable":true},"originatorKYCLevel":{"type":"string","nullable":true},"destinationInstitutionCode":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"nameEnquiryRef":{"type":"string","nullable":true},"originatorNarration":{"type":"string","nullable":true},"paymentReference":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"transactionLocation":{"type":"string","nullable":true},"beneficiaryNarration":{"type":"string","nullable":true},"billerId":{"type":"string","nullable":true}},"additionalProperties":false,"title":"FundTransferRequest"}},"text/json":{"schema":{"type":"object","properties":{"sourceInstitutioncode":{"type":"string","nullable":true},"amount":{"type":"string","nullable":true},"beneficiaryAccountName":{"type":"string","nullable":true},"beneficiaryAccountNumber":{"type":"string","nullable":true},"beneficiaryBankVerificationNumber":{"type":"string","nullable":true},"beneficiaryKYCLevel":{"type":"string","nullable":true},"channelCode":{"type":"string","nullable":true},"originatorAccountName":{"type":"string","nullable":true},"originatorAccountNumber":{"type":"string","nullable":true},"originatorBankVerificationNumber":{"type":"string","nullable":true},"originatorKYCLevel":{"type":"string","nullable":true},"destinationInstitutionCode":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"nameEnquiryRef":{"type":"string","nullable":true},"originatorNarration":{"type":"string","nullable":true},"paymentReference":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"transactionLocation":{"type":"string","nullable":true},"beneficiaryNarration":{"type":"string","nullable":true},"billerId":{"type":"string","nullable":true}},"additionalProperties":false,"title":"FundTransferRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"sourceInstitutioncode":{"type":"string","nullable":true},"amount":{"type":"string","nullable":true},"beneficiaryAccountName":{"type":"string","nullable":true},"beneficiaryAccountNumber":{"type":"string","nullable":true},"beneficiaryBankVerificationNumber":{"type":"string","nullable":true},"beneficiaryKYCLevel":{"type":"string","nullable":true},"channelCode":{"type":"string","nullable":true},"originatorAccountName":{"type":"string","nullable":true},"originatorAccountNumber":{"type":"string","nullable":true},"originatorBankVerificationNumber":{"type":"string","nullable":true},"originatorKYCLevel":{"type":"string","nullable":true},"destinationInstitutionCode":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"nameEnquiryRef":{"type":"string","nullable":true},"originatorNarration":{"type":"string","nullable":true},"paymentReference":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"transactionLocation":{"type":"string","nullable":true},"beneficiaryNarration":{"type":"string","nullable":true},"billerId":{"type":"string","nullable":true}},"additionalProperties":false,"title":"FundTransferRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"retryCount":{"type":"integer","format":"int32"},"responseCode":{"type":"string","nullable":true},"responseCodeDefinition":{"type":"string","nullable":true},"paymentReference":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"narration":{"type":"string","nullable":true},"tsqData":{"type":"object","properties":{"responseCode":{"type":"string","nullable":true},"responseCodeDefinition":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"sourceInstitutionCode":{"type":"string","nullable":true},"datePosted":{"type":"string","nullable":true},"transactionType":{"type":"string","nullable":true}},"additionalProperties":false,"title":"TransactionStatusRequeryResponse"}},"additionalProperties":false,"title":"FundTransferResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"FundTransferResponseNIP_ResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"retryCount":{"type":"integer","format":"int32"},"responseCode":{"type":"string","nullable":true},"responseCodeDefinition":{"type":"string","nullable":true},"paymentReference":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"narration":{"type":"string","nullable":true},"tsqData":{"type":"object","properties":{"responseCode":{"type":"string","nullable":true},"responseCodeDefinition":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"sourceInstitutionCode":{"type":"string","nullable":true},"datePosted":{"type":"string","nullable":true},"transactionType":{"type":"string","nullable":true}},"additionalProperties":false,"title":"TransactionStatusRequeryResponse"}},"additionalProperties":false,"title":"FundTransferResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"FundTransferResponseNIP_ResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"retryCount":{"type":"integer","format":"int32"},"responseCode":{"type":"string","nullable":true},"responseCodeDefinition":{"type":"string","nullable":true},"paymentReference":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"narration":{"type":"string","nullable":true},"tsqData":{"type":"object","properties":{"responseCode":{"type":"string","nullable":true},"responseCodeDefinition":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"sourceInstitutionCode":{"type":"string","nullable":true},"datePosted":{"type":"string","nullable":true},"transactionType":{"type":"string","nullable":true}},"additionalProperties":false,"title":"TransactionStatusRequeryResponse"}},"additionalProperties":false,"title":"FundTransferResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"FundTransferResponseNIP_ResponseHandler"}}}}}}
>
  
</StatusCodes>


      