---
id: create-settlements-marksettlementlogassettled
title: "Create settlements mark settlement log as settled"
description: "Create settlements mark settlement log as settled"
sidebar_label: "Create settlements mark settlement log as settled"
hide_title: true
hide_table_of_contents: true
api: eJztWc9v2zoM/lcCnt4PZ+naNXvzLeuGrcD6GjTtqciBsRhHqyxpktwtCPy/P9B2ayfNNqd4hwFzL2lkkhL5kfoccgPGksMgjT4XEEPiCAPNKARFGengL9Dd+cevyqToq68CIgiYeohvoZGHeQQWHWYUyPGzDWjMCGL48P5iNrm5/ggRSA0xrAgFOYjA0ZdcOhIQB5dTBD5ZUYYQbyCsLWv64KROoSjmlTD58NaINUskRgfeNd4AWqtkUnoy+uyN5rUnpsziMyUBIrCO/Q6SPD81Wq1vrMBA18qkLfmFMYpQQxFBE4RPJuVY7Z4vgqVxGQaIIc+lYB2La1a4oiU50gntU9K5UrhQVLlfbuR9DUcHaT701PhA3cQdBbc+M3kVs1pc6kBpicWjB1KHk2MoighQCMlBRTVtxWyJylMEQQY2Dpwln0w68VUmiKsKptJAoG+hR+RXQqRdKn/93UPzy0BTlJt6a7SvYn18dMQfgnzipGXLEMMsTxLyHqL29VdWmVUoD8LS16b2gpiR95h2w0Fg6LJd5XDHhGiSaJIkjML5u05nCQ61x4SjdVgyYbaDtc6zxTbUwuSsUUSQrNBtBedHwlsAHubCu+erzg4qjibcF0Z0C1hGLlmhDh0BrV4uxCT8UJqLdBhkRntzoGuStdOm2+kWqO/OunrOwv+WLzYdheuzPEvHdNKQfupkhm69v5qTbq51vcQ+UJjtBvpSq/VVfX1tgzd1ZikV/Ry8h4x6m3upyfvO8XpQvNQLg05I/R3aeVr0eTCt99c9sXtWSGqPm3AcbEX8H7oPnx9RC0XuCf0fSv49YfSE0RNGTxg9YfxGhPGsX/A9U/RM0TNFzxQ9U/w2TFH2r7Z7VWdlXQ4auPwgQ3fXWhgokw7QD5qpQkZhZXgYYY0vYcSwghhGaOWoZWjEhobNwlCZdIh+2Bjy5O4fphC5Uzx0CMH6eDRKKfO5TR0KesH/J8rk4gVaCzxkYKq7agYN779hZqsM2+191hOL3W4nnCzxn9Pl+NXw9PXL18NXp+Pj4eJkmQyPkzfjk+V4jEscw76WZ5NNrc5ms9huYDar7T7lEZerXlZ1/Yhd5i2uB00KDSbTc4iAo1PhdP+ybMEaHzIsyaCe2jwHv/2M1GpQFlEFx6aG9hbQSmjHkZubP4F3HsGK8yO+hc1mgZ5unCoKXv6SE99Ut/MI7tHJqiRv50X0MHPifBDS8wPxmOg7p35srcIfV/WA6s8Bj7z2eXNH6+0J1z2qnMWgiDb107PK4PCaDTQST36MNxqTJCEbWrLtTeetOplezq4hgkU9FMvK6xgcfuXcwK98jghM6Vp5E5ZrG1Co07x8C4NqZ/77DxFI8NQ=
sidebar_class_name: "post api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create settlements mark settlement log as settled"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/settlements/mark-settlement-log-as-settled"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create settlements mark settlement log as settled

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"onlyUpdateTlog":{"type":"boolean"},"settlementLogId":{"type":"string","format":"uuid"},"paymentReference":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"datePosted":{"type":"string","nullable":true},"retryCount":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"MarkLogAsSettledRequest"}},"text/json":{"schema":{"type":"object","properties":{"onlyUpdateTlog":{"type":"boolean"},"settlementLogId":{"type":"string","format":"uuid"},"paymentReference":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"datePosted":{"type":"string","nullable":true},"retryCount":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"MarkLogAsSettledRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"onlyUpdateTlog":{"type":"boolean"},"settlementLogId":{"type":"string","format":"uuid"},"paymentReference":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"datePosted":{"type":"string","nullable":true},"retryCount":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"MarkLogAsSettledRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settledId":{"type":"string","format":"uuid"},"settlementAccountID":{"type":"string","nullable":true},"transactionReference":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"charge":{"type":"number","format":"double"},"description":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionSessionId":{"type":"string","nullable":true},"settlementMode":{"type":"string","nullable":true},"merchantId":{"type":"string","format":"uuid"},"createdAt":{"type":"string","format":"date-time"},"settlementAccount":{"type":"object","properties":{"settlementAccountId":{"type":"string","format":"uuid"},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"code":{"type":"string","nullable":true}},"additionalProperties":false,"title":"GetSettlementAccountOnlyResponse"},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"}},"additionalProperties":false,"title":"GetSettledResponse"}},"additionalProperties":false,"title":"GetSettledResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settledId":{"type":"string","format":"uuid"},"settlementAccountID":{"type":"string","nullable":true},"transactionReference":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"charge":{"type":"number","format":"double"},"description":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionSessionId":{"type":"string","nullable":true},"settlementMode":{"type":"string","nullable":true},"merchantId":{"type":"string","format":"uuid"},"createdAt":{"type":"string","format":"date-time"},"settlementAccount":{"type":"object","properties":{"settlementAccountId":{"type":"string","format":"uuid"},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"code":{"type":"string","nullable":true}},"additionalProperties":false,"title":"GetSettlementAccountOnlyResponse"},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"}},"additionalProperties":false,"title":"GetSettledResponse"}},"additionalProperties":false,"title":"GetSettledResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settledId":{"type":"string","format":"uuid"},"settlementAccountID":{"type":"string","nullable":true},"transactionReference":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"charge":{"type":"number","format":"double"},"description":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionSessionId":{"type":"string","nullable":true},"settlementMode":{"type":"string","nullable":true},"merchantId":{"type":"string","format":"uuid"},"createdAt":{"type":"string","format":"date-time"},"settlementAccount":{"type":"object","properties":{"settlementAccountId":{"type":"string","format":"uuid"},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"code":{"type":"string","nullable":true}},"additionalProperties":false,"title":"GetSettlementAccountOnlyResponse"},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"}},"additionalProperties":false,"title":"GetSettledResponse"}},"additionalProperties":false,"title":"GetSettledResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      