---
id: update-serviceproviderconnectormaps
title: "Update service provider connector maps id"
description: "Update service provider connector maps id"
sidebar_label: "Update service provider connector maps id"
hide_title: true
hide_table_of_contents: true
api: eJztV0tz0zAQ/iuZPfGQG2hpAN9ChwEOMJ0+Tp0cNtI6EdiSkOSWjMf/nVnbcdyW0qZTOCWXOMq+9Olbeb8KrCOPUVvzRUEKpVMY6ZT8pZbkvL3Uiry0xpCM1hfoAgiIuAiQXkBndtyZfdPuaG35FR3MBDj0WFAkz/YVGCwIUtAKBGgDKTiMSxDg6WepPSlIoy9JQJBLKhDSCuLKsUeIXpsFCMi4iMiFllpBXYs+6KePX0+n52ef16GXhIr8FsHretYaU4gfrFqxhbQmkon8iM7lWjZIjb8Ha3jtVig7/04yggDnGdeoKTR214FipO/dmgAzwJMdyJQFw270PPAxXHHumbgVKOqY88LwPAJjBaiU5g1gfjyoL8M80MZtqtT5kAV3HO9Ji1QTN9KvuAPlBihDwrx4uUNniA5H9hScNaHd7v6rV/ylKEivHSeEFE5LKampe9CHDddcjno7OLtQG8O5tTmhYdQKCgEX9CeETZnnOOdN8uVRC1AYH5BOP+y4HnfIN7y+NfffA0p/YnIIkJ4wkprGv5bNvEiiLqiB2iqd6W18HsrBTxTvY1/Lt38Qcv39GY3Kyd9q/62bf8fWHVv/K1sf9wbf0XRH0/9I02ZsuD4itGPHqDu90VqxjHrJMmLNMmoUR0FxaVWrOiTLjkZ9pDBGp8ddhGQdIekjJBxhXGlVQ0uTtZopfc46I0YX0vF4QUUo3cKjoj1+lrkt1R46B6wruLVONtri4y8sXE53DHpwkOG7w2zyJjl8+/pt8uZwsp/MDzKZ7Mv3k4NsMsEMJ3CTex3latY/mW2Y0ENeBIer0SnFmFNBJo6mx19AAG+lxfHyNTs6G2KBzR3Qqapt8L12Mj0PB/NaLVrMqg75C0Cnoe+9u7AHAalW3ERLGyK7VdUcA537vK55+WdJfgXpxUzAJXrd9udFBUoHflY9B29U2E+V8OykE4nPRyD+XHm3iGbFwGFe8i8Q8INWraStZ7VYq84nz95mGWjcvgKWwO2/R23A5IwDbCxuDSIbj6mU5OLAdph0NuiY4+nZEWedd7q4sIrNPV6xwMarFgrb7K25q5u1CnI0i7J5C0Cbmj+/AfrdxvU=
sidebar_class_name: "patch api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update service provider connector maps id"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/service-provider-connector-maps/{id}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update service provider connector maps id

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"id","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"serviceProviderId":{"type":"string","format":"uuid"},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"}},"additionalProperties":false,"title":"AddUpdateServiceProviderNipConnectorMapRequest"}},"text/json":{"schema":{"type":"object","properties":{"serviceProviderId":{"type":"string","format":"uuid"},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"}},"additionalProperties":false,"title":"AddUpdateServiceProviderNipConnectorMapRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"serviceProviderId":{"type":"string","format":"uuid"},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"}},"additionalProperties":false,"title":"AddUpdateServiceProviderNipConnectorMapRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"serviceProviderId":{"type":"string","format":"uuid"},"serviceProviderName":{"type":"string","nullable":true},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponse"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"serviceProviderId":{"type":"string","format":"uuid"},"serviceProviderName":{"type":"string","nullable":true},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponse"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"serviceProviderId":{"type":"string","format":"uuid"},"serviceProviderName":{"type":"string","nullable":true},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponse"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      