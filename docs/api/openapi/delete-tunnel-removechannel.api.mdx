---
id: delete-tunnel-removechannel
title: "Delete tunnel settlementTunnelId remove channel channelSettlementId"
description: "Delete tunnel settlementTunnelId remove channel channelSettlementId"
sidebar_label: "Delete tunnel settlementTunnelId remove channel channelSettlementId"
hide_title: true
hide_table_of_contents: true
api: eJztVk1v2zAM/SsGTxug1d2OvhVo0BbYgKIfpyAHxmITdbKkSnSwwNB/H2g7H2196IB2p54sS9Qj+UTxqQMfKCIb7640VKDJEtNd6xzZG2r8huo1yg8oYFwlqOZwS8yWGnI82MFCQcCIDTFFsejAYUNQQXpheaVBgXFQQUBeg4JIT62JpKHi2JKCVK+pQag64G3oETgatwIFDz42yFBB2xoNOau9kzHAQ1Qf4uVi9uv27P7ucge9JtQU/wE854UYp+BdoiTrP05P5aMp1dEEOQKo4Lata0oJFNTeMTnukegPl8GieO5ee/DLR6oZFIQop8lmwE8j1MFw6b0ldJAVNJQSrmiKBNdai0tLQ0ZZgUZ+gzuj38QooNZGkkV7fbT/AW0iBWxYHMN5X4cvK+1mpO+9cHbfS3TaUhxgQ7Cm7m9E+Zj8J+Mfznhf3Z9UfzzVWZCft5sBouB+Z/G6XxexF4FibLLFdLNtiNf+IB/CnnTeCkoMphzAy+41ei4H+G8jbNlN4GdQkChudtrSRiv9lzmkqixX1KQ2rCJqOpFxbX2rTzAEkH5r3IPvD25k7IKaFHBbHPCLs+srUCDoAyOb71JBwSdusK/IUQDeh6ln7O8L6qi/ZzUk2I0UzgGDEfFtRxWuJkX1OY9iNuV/oWDtEwtq1y0x0X20Ocv0U0txC9V8oWCD0Qw3ZN6BNknGel97LxLYixR8uRmF8Gsh4U4lNk6i2wrjaFv5AwW/aTv9WBD9/Y8RTFGWF1ntxP7dCRncHj0t9iFJ5sPqWV1T4KO1Y4jF0d07n/2c3c0g578lCXdV
sidebar_class_name: "delete api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete tunnel settlementTunnelId remove channel channelSettlementId"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/tunnel/{settlementTunnelId}/remove-channel/{channelSettlementId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete tunnel settlementTunnelId remove channel channelSettlementId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementTunnelId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"channelSettlementId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponse"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponse"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponse"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      