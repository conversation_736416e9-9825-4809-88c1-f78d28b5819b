---
id: get-settlementmapping-merchant-all
title: "Get SettlementMapping merchant merchantId all"
description: "Get SettlementMapping merchant merchantId all"
sidebar_label: "Get SettlementMapping merchant merchantId all"
hide_title: true
hide_table_of_contents: true
api: eJytVE1v2zAM/SsGTxsg1G138y2HIsshRbG0pyIHxmZsYZasSnSwzNB/H+iPfGPYgJ5sS+Qj3+OjO2gceWTd2EUBGZTEK2KuyZBlg85pWy7J5xVantU1KGAsA2TvcAxbDmGwVuDQoyEmLyEdWDQEGZgRYFGAAm0hA4dcgQJPH632VEDGviUFIa/IIGQd8N5JZmAvyAq2jTfIkEHb6gJiVAfwFyzpuTUb8hP4R0t+DzfQtGUq+7gDnLb87fEKb6V/0+egzZ+Wq9nb6/cJrSIs+px/pR7jWoKDa2ygIPeP9/fyKCjkXjuZHGSwavOcQpDaFzdz4uRqVsk0kuQ4mwT7+RriqhmtAGqYVAYpOp1ewaRTdtodcWI6AAXyu8kIra+FPLMLWZqWZELrSo8F3cl7XjdtcYfOgZDVdtv0OmiuewnJBIf7ExLJ7GUBCgR94Lh7gKjANYENWsmd1P9P7mfKHSbB9ItTV6O2UqWn0o26vAM6DerGMqiD60FBdrYBUmutoGoCC0LXbTDQm69jlOPBbyJaoQNuarHIFutACn7S/tLwO6xb6RHEc39JGB19DF/Lh9cSf7vYhRZ5Y5msmPzLj9G6XxP5HdzSaDxEuz+tOfVzIkZcRzXtxKd3MVQ72cAz9gefz59eIcY/w6K2DQ==
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementMapping merchant merchantId all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementMapping/merchant/{merchantId}/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementMapping merchant merchantId all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      