---
id: get-settlements-merchant
title: "Get settlements merchant merchantId"
description: "Get settlements merchant merchantId"
sidebar_label: "Get settlements merchant merchantId"
hide_title: true
hide_table_of_contents: true
api: eJztWk1v2zgQ/SvGnHYBbd12b7o5aZEGaFojTvZS+DAWxzZbiWTIkVGvof++oCVZSqK0lBHsiSfHJt98c8iHzAG0IYsstboWkMKGeEHMORWk2N2QzbaoGBJg3DhIv0G3CssEDFosiMn6tQMoLAhSKBrYtYAEpIIUDPIWErD0UEpLAlK2JSXgsi0VCOkBeG880rGVagMJrLUtkCGFspQCqio5CZ/jhr6UxYpsK/yhJLuHAWlSMW2O+07ipOK/3z+Tt5D/0utIu/p4s5jd331qpW0JxRET6npVLf1mZ7Ry5Pz6+7dv/Ycgl1lpfKYghUWZZeQcJJBpxT4bXhL95KnJ0Ws+PNegV98p87k01iedZS3fNaK6jSutc0IFVQIFOYcbGkqRKvMcVznVHlUJCOS+OrQWfSAlU+ECzDgV1me98bX426JIgC0qh1lbveMQt7QmSyoLc64H/NDPxDjo5Rbto2iqtpJPlgpdevAQ7hLNudB/MC/P0PsBeTA8HQqZ/mJZDGhVivKg+NSZF3Oyz1G9UnRkdzKjudU7Kch+OR64IPGPcJdahOGw0KXiOUoRGLcacKfrFhkIcrq0GV2g+hFsWAcJj8ERMssyb+GZqNqJEFyv/4ecyXb7iIy2reImOGYnyNzqtcxpYXLJwSGX7gI52w4XJpase9diwJ5RbcAS2/2lT0HQZZTAyps6rru1KbhX8qGki/ECnA/nSMgpHNdqbvXGvngJZZaQScw4vBUVWsi1HIfpDGpKfsyl1UACS3415ryvxpx0v3nsOe9jdBAiCz84cysLtPuXnhejOsX/XghNsxhTCA1kZO+7KJ1U5FxwzlrgV7XSaIVUL7yZnl9rv+1WfpMQ0l/jmM97bq4xd5QAy+P1BlfELUFYPHX/tnnBjpK2eHqazpLy3KZOzEA8TMcnwtqraQlD2Pa1tI7nL7yhu3qwcihZOZ6PZc2Ye7ALtPQIuKVMWxEKUfTzbPuMpZ3UpTsP/yoF8VnW4RXtD59QiZxsXW/G5DI7UuPpd6cjp4qcKnKqyKkip4qcKnKqyKkip4qcKnKqyKkipxrBqY7/oYpkKpKpSKYimYpkKpKpSKYimYpkKpKpSKYimYpkagyZqryGx0OBV8ST7py7SXsEJ48GMgvirW5mPX2b8KOZKUzRyGkPPG0x00OHrqB+rbbznqXN/Ywjs3HpdLqhwpVmY1HQG/93lutSvEFjwM80SrWuW/fJ78IZ3E86pyez+TUk4KXX/uzeHctZOy7wyGTaIcsgP8UgC+rNSFZJ7cChicE3QCOh310ddH0MEkh78pcJbLVjjzocVujo3uZV5X+uB0h9eIR0viDEKec/aP90gnVX8x4AP0T6C0AzotptX/ovVtYVN6Tsif+nOVH447aZRf1z4gd7h+LSMmS17+ts7emFoVpWSTvk+upW1Np6I7WPglWvzrKMDPfW+iKWvWq/+ngHVfUfL8jJmQ==
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settlements merchant merchantId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settlements/merchant/{merchantId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settlements merchant merchantId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"settlementLogId":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"transactionReference":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionCharge":{"type":"number","format":"double"},"transactionChargeCap":{"type":"number","format":"double"},"transactionChargeValue":{"type":"number","format":"double"},"transactionDate":{"type":"string","format":"date-time"},"transactionChannel":{"type":"string","nullable":true},"settledPerChannel":{"type":"boolean"},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"amountPaid":{"type":"number","format":"double"},"amountToSettle":{"type":"number","format":"double"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"merchantId":{"type":"string","format":"uuid"},"merchantName":{"type":"string","nullable":true},"settlementMode":{"type":"string","nullable":true},"settlementProfileSplitCode":{"type":"string","nullable":true},"isBatch":{"type":"boolean"},"autoSettlement":{"type":"boolean"},"autoSettlementCharge":{"type":"number","format":"double"},"retryCount":{"type":"integer","format":"int32"},"batchReference":{"type":"string","nullable":true},"merchantUniqueBatchReference":{"type":"string","nullable":true},"splitReference":{"type":"string","nullable":true},"settlementInProgress":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementAccount":{"type":"object","properties":{"settlementAccountId":{"type":"string","format":"uuid"},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"code":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"merchantId":{"type":"string","format":"uuid"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementProfile":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetSettlementAccountResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetMerchantSettlementResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"settlementLogId":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"transactionReference":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionCharge":{"type":"number","format":"double"},"transactionChargeCap":{"type":"number","format":"double"},"transactionChargeValue":{"type":"number","format":"double"},"transactionDate":{"type":"string","format":"date-time"},"transactionChannel":{"type":"string","nullable":true},"settledPerChannel":{"type":"boolean"},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"amountPaid":{"type":"number","format":"double"},"amountToSettle":{"type":"number","format":"double"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"merchantId":{"type":"string","format":"uuid"},"merchantName":{"type":"string","nullable":true},"settlementMode":{"type":"string","nullable":true},"settlementProfileSplitCode":{"type":"string","nullable":true},"isBatch":{"type":"boolean"},"autoSettlement":{"type":"boolean"},"autoSettlementCharge":{"type":"number","format":"double"},"retryCount":{"type":"integer","format":"int32"},"batchReference":{"type":"string","nullable":true},"merchantUniqueBatchReference":{"type":"string","nullable":true},"splitReference":{"type":"string","nullable":true},"settlementInProgress":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementAccount":{"type":"object","properties":{"settlementAccountId":{"type":"string","format":"uuid"},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"code":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"merchantId":{"type":"string","format":"uuid"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementProfile":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetSettlementAccountResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetMerchantSettlementResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"settlementLogId":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"transactionReference":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionCharge":{"type":"number","format":"double"},"transactionChargeCap":{"type":"number","format":"double"},"transactionChargeValue":{"type":"number","format":"double"},"transactionDate":{"type":"string","format":"date-time"},"transactionChannel":{"type":"string","nullable":true},"settledPerChannel":{"type":"boolean"},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"amountPaid":{"type":"number","format":"double"},"amountToSettle":{"type":"number","format":"double"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"merchantId":{"type":"string","format":"uuid"},"merchantName":{"type":"string","nullable":true},"settlementMode":{"type":"string","nullable":true},"settlementProfileSplitCode":{"type":"string","nullable":true},"isBatch":{"type":"boolean"},"autoSettlement":{"type":"boolean"},"autoSettlementCharge":{"type":"number","format":"double"},"retryCount":{"type":"integer","format":"int32"},"batchReference":{"type":"string","nullable":true},"merchantUniqueBatchReference":{"type":"string","nullable":true},"splitReference":{"type":"string","nullable":true},"settlementInProgress":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementAccount":{"type":"object","properties":{"settlementAccountId":{"type":"string","format":"uuid"},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"code":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"merchantId":{"type":"string","format":"uuid"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementProfile":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetSettlementAccountResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetMerchantSettlementResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      