---
id: get-wemanipservice-nipbanks
title: "Get WemaNIPService NipBanks"
description: "Get WemaNIPService NipBanks"
sidebar_label: "Get WemaNIPService NipBanks"
hide_title: true
hide_table_of_contents: true
api: eJztVcFu2zAM/ZWApw0Q6m5H37KhSANsQdC02CHIgbHYRK0sqRIdLDD07wMdN3XaYiuwSw892TCfyMf3ZLIFHygiG++mGkrYEP+iGp0JieLOVDQzYY3uPoECxk2CcgkCmE3niwMAVgoCRqyJKUq8BYc1QQmTi5+L8c31JSgwDkrYEmqKoCDSQ2MiaSg5NqQgVVuqEcoWeB/kZOJo3AZyXgk4Be8SJYl/PT+Xh6ZURROENZSwaKqKkjCsvGNy3GWi31wEi1K5fVnBr++oYl<PERSON>QogjA5pA/9amegGvvLaGDrKCmlHBDL3kqcI21uLZ06Cgr0MjDchgj7kUHpjr9m4YoPutEfEMpAX/3+i3grAC1NqIb2vmg5C3aRArYsGBhZsK3zvT83yl+mMRXvYWX6LSlCF2SEKypuotX3CX/4dK7dKn7iz7seZ/2ZMlzOgonxKPT8Tw60hDteev7KS9yIW+hhAKDKU4PFYNDsgceB3sTrcxx5pDKothQnZqwiajpTN4r6xt9hiGAzG3jbn2nZt/LhOoUcD9aELOlmhyPxvMpKJDsB/a7L6JV8Ilr7G7c4yL5a1cnChzdG8z/rA7E277jJWAwoJ7vMfXk2ErB1icWaNuuMdFNtDnL54eG4h7K5UrBDqM5eLpcZfW43UQmbZIE9NHPZxSPawo+XfWr8PNIFuxr1O9pf7pLd2gbgUFWbR8dVxUFHsSGKVYD4ycX15DzH6W8xB0=
sidebar_class_name: "get api-method"
info_path: docs/api/openapi/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get WemaNIPService NipBanks"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/WemaNIPService/NipBanks"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get WemaNIPService NipBanks

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"bankName":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NipBanks"},"nullable":true}},"additionalProperties":false,"title":"NipBanksListResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"bankName":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NipBanks"},"nullable":true}},"additionalProperties":false,"title":"NipBanksListResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"bankName":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NipBanks"},"nullable":true}},"additionalProperties":false,"title":"NipBanksListResponseHandler"}}}}}}
>
  
</StatusCodes>


      