openapi: 3.0.1
info:
  title: Gemspay Settlement API
  version: v1
servers:
- url: https://gemsupgrade.gemscloud.app
paths:
  /api/auto-settlement-charge-setups:
    get:
      summary: Get all auto settlement charge setups
      operationId: getAllAutoSettlementChargeSetups
      tags:
        - AutoSettlementChargeSetup
      parameters:
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetAutoSettlementChargeResponseListPagedResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetAutoSettlementChargeResponseListPagedResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetAutoSettlementChargeResponseListPagedResponseHandler'
  /api/auto-settlement-charge-setups/merchant/{settlementProfileId}:
    get:
      summary: Get auto settlement charge setups by merchant settlement profile
      operationId: getAutoSettlementChargeSetupsByMerchant
      tags:
        - AutoSettlementChargeSetup
      parameters:
        - name: settlementProfileId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetAutoSettlementChargeResponseListResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetAutoSettlementChargeResponseListResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetAutoSettlementChargeResponseListResponseHandler'
  /api/auto-settlement-charge-setups/add:
    post:
      summary: Add new auto settlement charge setup
      operationId: addAutoSettlementChargeSetup
      tags:
        - AutoSettlementChargeSetup
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddAutoSettlementChargeRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AddAutoSettlementChargeRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AddAutoSettlementChargeRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetAutoSettlementChargeResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetAutoSettlementChargeResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetAutoSettlementChargeResponseResponseHandler'
  /api/auto-settlement-charge-setups/{setupId}/merchant/{settlementProfileId}:
    get:
      summary: Get auto settlement charge setup by ID and merchant
      operationId: getAutoSettlementChargeSetupByIdAndMerchant
      tags:
        - AutoSettlementChargeSetup
      parameters:
        - name: setupId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: settlementProfileId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetAutoSettlementChargeResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetAutoSettlementChargeResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetAutoSettlementChargeResponseResponseHandler'
    patch:
      summary: Update auto settlement charge setup
      operationId: updateAutoSettlementChargeSetup
      tags:
        - AutoSettlementChargeSetup
      parameters:
        - name: setupId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: settlementProfileId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAutoSettlementChargeRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateAutoSettlementChargeRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateAutoSettlementChargeRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetAutoSettlementChargeResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetAutoSettlementChargeResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetAutoSettlementChargeResponseResponseHandler'
    delete:
      summary: Delete auto settlement charge setup
      operationId: deleteAutoSettlementChargeSetup
      tags:
        - AutoSettlementChargeSetup
      parameters:
        - name: setupId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: settlementProfileId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/DeleteAutoSettlementChargeResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteAutoSettlementChargeResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/DeleteAutoSettlementChargeResponseResponseHandler'
  /api/BackgroundJob/settle:
    post:
      summary: Trigger background settlement job
      operationId: triggerBackgroundSettlement
      tags:
        - BackgroundJob
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/bank-reconciliation/import-statement:
    post:
      summary: Import bank statement for reconciliation
      operationId: importBankStatement
      tags:
        - BankReconciliation
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                StatementFile:
                  type: string
                  format: binary
                StatementFileFormat:
                  $ref: '#/components/schemas/BankStatementFormat'
                CollectionAccountId:
                  type: string
                  format: uuid
                StartDate:
                  type: string
                  format: date-time
                EndDate:
                  type: string
                  format: date-time
            encoding:
              StatementFile:
                style: form
              StatementFileFormat:
                style: form
              CollectionAccountId:
                style: form
              StartDate:
                style: form
              EndDate:
                style: form
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/StringResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/StringResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/StringResponseHandler'
  /api/bank-reconciliation/bank-statements:
    get:
      summary: Get all bank statements
      operationId: getAllBankStatements
      tags:
        - BankReconciliation
      parameters:
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetBankStatementResponseListPagedResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetBankStatementResponseListPagedResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetBankStatementResponseListPagedResponseHandler'
  /api/bank-reconciliation/bank-statements/{statementId}:
    get:
      summary: Get bank statement by ID
      operationId: getBankStatementById
      tags:
        - BankReconciliation
      parameters:
        - name: statementId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetBankStatementResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetBankStatementResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetBankStatementResponseResponseHandler'
  /api/bank-reconciliation/bank-statements/{statementId}/lines:
    get:
      summary: Get bank statement transaction lines
      operationId: getBankStatementLines
      tags:
        - BankReconciliation
      parameters:
        - name: statementId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetBankStatementTransactionResponseListPagedResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetBankStatementTransactionResponseListPagedResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetBankStatementTransactionResponseListPagedResponseHandler'
  /api/bank-reconciliation/bank-statements/{reference}/by-ref:
    get:
      summary: Get bank statement by reference
      operationId: getBankStatementByReference
      tags:
        - BankReconciliation
      parameters:
        - name: reference
          in: path
          required: true
          schema:
            type: string
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetBankStatementResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetBankStatementResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetBankStatementResponseResponseHandler'
  /api/BatchPeriod/AddBatchPeriod:
    post:
      summary: Add new batch period
      operationId: addBatchPeriod
      tags:
        - BatchPeriod
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddBatchPeriodRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AddBatchPeriodRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AddBatchPeriodRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseResponseHandler'
  /api/BatchPeriod/GetAll:
    get:
      summary: Get all batch periods
      operationId: getAllBatchPeriods
      tags:
        - BatchPeriod
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseListResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseListResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseListResponseHandler'
  /api/BatchPeriod/Get/{batchPeriodId}:
    get:
      summary: Get BatchPeriod Get batchPeriodId
      operationId: getBatchperiodGet
      tags:
        - BatchPeriod
      parameters:
        - name: batchPeriodId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseResponseHandler'
  /api/BatchPeriod/Get/{batchPeriodCode}/Code:
    get:
      summary: Get BatchPeriod Get batchPeriodCode Code
      operationId: getBatchperiodGetCode
      tags:
        - BatchPeriod
      parameters:
        - name: batchPeriodCode
          in: path
          required: true
          schema:
            type: string
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseResponseHandler'
  /api/BatchPeriod/Update/{batchPeriodId}:
    patch:
      summary: Update BatchPeriod Update batchPeriodId
      operationId: updateBatchperiodUpdate
      tags:
        - BatchPeriod
      parameters:
        - name: batchPeriodId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateBatchPeriodRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateBatchPeriodRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateBatchPeriodRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseResponseHandler'
  /api/BatchPeriod/Remove/{batchPeriodId}:
    delete:
      summary: Delete BatchPeriod Remove batchPeriodId
      operationId: deleteBatchperiodRemove
      tags:
        - BatchPeriod
      parameters:
        - name: batchPeriodId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/RemoveBatchPeriodResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/RemoveBatchPeriodResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/RemoveBatchPeriodResponseResponseHandler'
  /api/BatchPeriod/GetDefault:
    get:
      summary: Get BatchPeriod GetDefault
      operationId: getBatchperiodGetdefault
      tags:
        - BatchPeriod
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseResponseHandler'
  /api/BatchPeriod/SwitchDefault:
    patch:
      summary: Update BatchPeriod SwitchDefault
      operationId: updateBatchperiodSwitchdefault
      tags:
        - BatchPeriod
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SwitchBatchPeriodToDefaultRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/SwitchBatchPeriodToDefaultRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/SwitchBatchPeriodToDefaultRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetBatchPeriodResponseResponseHandler'
  /api/BatchPeriod/Subscribe/{batchPeriodId}/MerchantProfile/{settlementProfileId}:
    post:
      summary: Create BatchPeriod Subscribe batchPeriodId MerchantProfile settlementProfileId
      operationId: createBatchperiodSubscribeMerchantprofile
      tags:
        - BatchPeriod
      parameters:
        - name: batchPeriodId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: settlementProfileId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetSettlementProfileWithBatchPeriodResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetSettlementProfileWithBatchPeriodResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetSettlementProfileWithBatchPeriodResponseResponseHandler'
  /api/BatchPeriod/Update/MerchantProfile/{settlementProfileId}:
    patch:
      summary: Update BatchPeriod Update MerchantProfile settlementProfileId
      operationId: updateBatchperiodUpdateMerchantprofile
      tags:
        - BatchPeriod
      parameters:
        - name: settlementProfileId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/UpdateMerchantBatchPeriodRequest'
          text/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/UpdateMerchantBatchPeriodRequest'
          application/*+json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/UpdateMerchantBatchPeriodRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetSettlementProfileWithBatchPeriodResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetSettlementProfileWithBatchPeriodResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetSettlementProfileWithBatchPeriodResponseResponseHandler'
  /api/BatchPeriod/MerchantProfile/{settlementProfileId}:
    get:
      summary: Get BatchPeriod MerchantProfile settlementProfileId
      operationId: getBatchperiodMerchantprofile
      tags:
        - BatchPeriod
      parameters:
        - name: settlementProfileId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetSettlementProfileWithBatchPeriodResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetSettlementProfileWithBatchPeriodResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetSettlementProfileWithBatchPeriodResponseResponseHandler'
  /api/charge-revenues:
    get:
      summary: Get charge revenues
      operationId: getChargerevenues
      tags:
        - ChargeRevenue
      parameters:
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: isLog
          in: query
          schema:
            type: boolean
            default: false
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetChargeRevenueResponseListPagedResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetChargeRevenueResponseListPagedResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetChargeRevenueResponseListPagedResponseHandler'
  /api/charge-revenues/{recordId}:
    get:
      summary: Get charge revenues recordId
      operationId: getChargerevenues
      tags:
        - ChargeRevenue
      parameters:
        - name: recordId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: isLog
          in: query
          schema:
            type: boolean
            default: false
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetChargeRevenueResponseListPagedResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetChargeRevenueResponseListPagedResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetChargeRevenueResponseListPagedResponseHandler'
  /api/charge-revenues/log-settled-revenue:
    post:
      summary: Create charge revenues log settled revenue
      operationId: createChargerevenuesLogsettledrevenue
      tags:
        - ChargeRevenue
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetChargeRevenueResponseListPagedResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetChargeRevenueResponseListPagedResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetChargeRevenueResponseListPagedResponseHandler'
  /api/connector/{connector}/balance-inquiry:
    post:
      summary: Create connector connector balance inquiry
      operationId: createConnectorBalanceinquiry
      tags:
        - Connector
      parameters:
        - name: connector
          in: path
          required: true
          schema:
            type: string
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BalanceInquiryRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/BalanceInquiryRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/BalanceInquiryRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/BalanceInquiryResponseNIP_ResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/BalanceInquiryResponseNIP_ResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/BalanceInquiryResponseNIP_ResponseHandler'
  /api/connector/{connector}/name-inquiry:
    post:
      summary: Create connector connector name inquiry
      operationId: createConnectorNameinquiry
      tags:
        - Connector
      parameters:
        - name: connector
          in: path
          required: true
          schema:
            type: string
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NameInquiryRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/NameInquiryRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/NameInquiryRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/NameInquiryResponseNIP_ResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/NameInquiryResponseNIP_ResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/NameInquiryResponseNIP_ResponseHandler'
  /api/connector/{connector}/transfer-funds:
    post:
      summary: Create connector connector transfer funds
      operationId: createConnectorTransferfunds
      tags:
        - Connector
      parameters:
        - name: connector
          in: path
          required: true
          schema:
            type: string
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FundTransferRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/FundTransferRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/FundTransferRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/FundTransferResponseNIP_ResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/FundTransferResponseNIP_ResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/FundTransferResponseNIP_ResponseHandler'
  /api/connector/{connector}/transaction-status-requery:
    post:
      summary: Create connector connector transaction status requery
      operationId: createConnectorTransactionstatusrequery
      tags:
        - Connector
      parameters:
        - name: connector
          in: path
          required: true
          schema:
            type: string
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionStatusRequeryRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/TransactionStatusRequeryRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/TransactionStatusRequeryRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/TransactionStatusRequeryResponseNIP_ResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionStatusRequeryResponseNIP_ResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/TransactionStatusRequeryResponseNIP_ResponseHandler'
  /api/fund-reversals:
    get:
      summary: Get fund reversals
      operationId: getFundreversals
      tags:
        - FundReversal
      parameters:
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: processed
          in: query
          schema:
            type: boolean
            default: false
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetFundReversalResponseListPagedResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetFundReversalResponseListPagedResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetFundReversalResponseListPagedResponseHandler'
  /api/fund-reversals/recordId:
    get:
      summary: Get fund reversals recordId
      operationId: getFundreversalsRecordid
      tags:
        - FundReversal
      parameters:
        - name: recordId
          in: query
          schema:
            type: string
            format: uuid
        - name: processed
          in: query
          schema:
            type: boolean
            default: false
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetFundReversalResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetFundReversalResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetFundReversalResponseResponseHandler'
  /api/fund-reversals/{transactionId}/reverse:
    post:
      summary: Create fund reversals transactionId reverse
      operationId: createFundreversalsReverse
      tags:
        - FundReversal
      parameters:
        - name: transactionId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FundReversalRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/FundReversalRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/FundReversalRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/StringResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/StringResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/StringResponseHandler'
  /api/fund-reversals/{unprocessedReversalId}/retry:
    post:
      summary: Create fund reversals unprocessedReversalId retry
      operationId: createFundreversalsRetry
      tags:
        - FundReversal
      parameters:
        - name: unprocessedReversalId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/StringResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/StringResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/StringResponseHandler'
  /api/FundTransferTrace/all:
    get:
      summary: Get FundTransferTrace all
      operationId: getFundtransfertraceAll
      tags:
        - FundTransferTrace
      parameters:
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/gemspay-bank-account/add:
    post:
      summary: Add gemspay bank account add
      operationId: addGemspaybankaccountAdd
      tags:
        - GemspayBankAccount
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddUpdateGemspayBankAccountRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AddUpdateGemspayBankAccountRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AddUpdateGemspayBankAccountRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetGemspayBankAccountResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetGemspayBankAccountResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetGemspayBankAccountResponseResponseHandler'
  /api/gemspay-bank-account/all:
    get:
      summary: Get gemspay bank account all
      operationId: getGemspaybankaccountAll
      tags:
        - GemspayBankAccount
      parameters:
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetGemspayBankAccountResponseListPagedResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetGemspayBankAccountResponseListPagedResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetGemspayBankAccountResponseListPagedResponseHandler'
  /api/gemspay-bank-account/{id}:
    get:
      summary: Get gemspay bank account id
      operationId: getGemspaybankaccount
      tags:
        - GemspayBankAccount
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: withChangeLogs
          in: query
          schema:
            type: boolean
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetGemspayBankAccountResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetGemspayBankAccountResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetGemspayBankAccountResponseResponseHandler'
    patch:
      summary: Update gemspay bank account id
      operationId: updateGemspaybankaccount
      tags:
        - GemspayBankAccount
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddUpdateGemspayBankAccountRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AddUpdateGemspayBankAccountRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AddUpdateGemspayBankAccountRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetGemspayBankAccountResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetGemspayBankAccountResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetGemspayBankAccountResponseResponseHandler'
    delete:
      summary: Delete gemspay bank account id
      operationId: deleteGemspaybankaccount
      tags:
        - GemspayBankAccount
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/DeleteGemspayBankAccountResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteGemspayBankAccountResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/DeleteGemspayBankAccountResponseResponseHandler'
  /api/gemspay-bank-account/{id}/active-deactivate:
    patch:
      summary: Update gemspay bank account id active deactivate
      operationId: updateGemspaybankaccountActivedeactivate
      tags:
        - GemspayBankAccount
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateGemspayBankAccountActiveStateRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateGemspayBankAccountActiveStateRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateGemspayBankAccountActiveStateRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetGemspayBankAccountResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetGemspayBankAccountResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetGemspayBankAccountResponseResponseHandler'
  /health-check:
    get:
      summary: Get health check
      operationId: getHealthcheck
      tags:
        - HealthCheck
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /health-check/bugsnag:
    get:
      summary: Get health check bugsnag
      operationId: getHealthcheckBugsnag
      tags:
        - HealthCheck
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/service-provider-connector-maps:
    get:
      summary: Get service provider connector maps
      operationId: getServiceproviderconnectormaps
      tags:
        - ServiceProviderNipConnectorMap
      parameters:
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetServiceProviderNipConnectorMapResponseListPagedResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetServiceProviderNipConnectorMapResponseListPagedResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetServiceProviderNipConnectorMapResponseListPagedResponseHandler'
    post:
      summary: Create service provider connector maps
      operationId: createServiceproviderconnectormaps
      tags:
        - ServiceProviderNipConnectorMap
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddUpdateServiceProviderNipConnectorMapRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AddUpdateServiceProviderNipConnectorMapRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AddUpdateServiceProviderNipConnectorMapRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetServiceProviderNipConnectorMapResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetServiceProviderNipConnectorMapResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetServiceProviderNipConnectorMapResponseResponseHandler'
  /api/service-provider-connector-maps/{id}:
    get:
      summary: Get service provider connector maps id
      operationId: getServiceproviderconnectormaps
      tags:
        - ServiceProviderNipConnectorMap
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetServiceProviderNipConnectorMapResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetServiceProviderNipConnectorMapResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetServiceProviderNipConnectorMapResponseResponseHandler'
    patch:
      summary: Update service provider connector maps id
      operationId: updateServiceproviderconnectormaps
      tags:
        - ServiceProviderNipConnectorMap
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddUpdateServiceProviderNipConnectorMapRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AddUpdateServiceProviderNipConnectorMapRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AddUpdateServiceProviderNipConnectorMapRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetServiceProviderNipConnectorMapResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetServiceProviderNipConnectorMapResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetServiceProviderNipConnectorMapResponseResponseHandler'
  /api/service-setups/{id}:
    get:
      summary: Get service setups id
      operationId: getServicesetups
      tags:
        - ServiceSetup
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetServiceSetupResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetServiceSetupResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetServiceSetupResponseResponseHandler'
    patch:
      summary: Update service setups id
      operationId: updateServicesetups
      tags:
        - ServiceSetup
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddUpdateServiceSetupRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AddUpdateServiceSetupRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AddUpdateServiceSetupRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetServiceSetupResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetServiceSetupResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetServiceSetupResponseResponseHandler'
  /api/service-setups/{slug}:
    get:
      summary: Get service setups slug
      operationId: getServicesetups
      tags:
        - ServiceSetup
      parameters:
        - name: slug
          in: path
          required: true
          schema:
            type: string
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetServiceSetupResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetServiceSetupResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetServiceSetupResponseResponseHandler'
  /api/service-setups:
    post:
      summary: Create service setups
      operationId: createServicesetups
      tags:
        - ServiceSetup
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddUpdateServiceSetupRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AddUpdateServiceSetupRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AddUpdateServiceSetupRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetServiceSetupResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetServiceSetupResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetServiceSetupResponseResponseHandler'
    get:
      summary: Get service setups
      operationId: getServicesetups
      tags:
        - ServiceSetup
      parameters:
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetServiceSetupResponseListPagedResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetServiceSetupResponseListPagedResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetServiceSetupResponseListPagedResponseHandler'
  /api/settled/{settledId}:
    get:
      summary: Get settled settledId
      operationId: getSettled
      tags:
        - Settled
      parameters:
        - name: settledId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/settled/all:
    get:
      summary: Get settled all
      operationId: getSettledAll
      tags:
        - Settled
      parameters:
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: Channel
          in: query
          schema:
            type: string
        - name: Amount
          in: query
          schema:
            type: number
            format: double
        - name: TransactionReference
          in: query
          schema:
            type: string
        - name: Period
          in: query
          schema:
            type: string
        - name: From
          in: query
          schema:
            type: string
            format: date-time
        - name: To
          in: query
          schema:
            type: string
            format: date-time
        - name: Day
          in: query
          schema:
            type: string
            format: date-time
        - name: MerchantId
          in: query
          schema:
            type: string
        - name: IsInProgress
          in: query
          schema:
            type: boolean
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/settled/merchant/{merchantId}/aggregated:
    get:
      summary: Get settled merchant merchantId aggregated
      operationId: getSettledMerchantAggregated
      tags:
        - Settled
      parameters:
        - name: withTransactions
          in: query
          schema:
            type: boolean
        - name: merchantId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/settled/merchant/{merchantId}:
    get:
      summary: Get settled merchant merchantId
      operationId: getSettledMerchant
      tags:
        - Settled
      parameters:
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: Channel
          in: query
          schema:
            type: string
        - name: Amount
          in: query
          schema:
            type: number
            format: double
        - name: TransactionReference
          in: query
          schema:
            type: string
        - name: Period
          in: query
          schema:
            type: string
        - name: From
          in: query
          schema:
            type: string
            format: date-time
        - name: To
          in: query
          schema:
            type: string
            format: date-time
        - name: Day
          in: query
          schema:
            type: string
            format: date-time
        - name: MerchantId
          in: query
          schema:
            type: string
        - name: IsInProgress
          in: query
          schema:
            type: boolean
        - name: merchantId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/settled/merchant/{merchantId}/list:
    get:
      summary: Get settled merchant merchantId list
      operationId: getSettledMerchantList
      tags:
        - Settled
      parameters:
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: merchantId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/settled/{settledId}/merchant/{merchantId}:
    get:
      summary: Get settled settledId merchant merchantId
      operationId: getSettledMerchant
      tags:
        - Settled
      parameters:
        - name: settledId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: merchantId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/settlements/all:
    get:
      summary: Get settlements all
      operationId: getSettlementsAll
      tags:
        - Settlement
      parameters:
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: Channel
          in: query
          schema:
            type: string
        - name: Amount
          in: query
          schema:
            type: number
            format: double
        - name: TransactionReference
          in: query
          schema:
            type: string
        - name: Period
          in: query
          schema:
            type: string
        - name: From
          in: query
          schema:
            type: string
            format: date-time
        - name: To
          in: query
          schema:
            type: string
            format: date-time
        - name: Day
          in: query
          schema:
            type: string
            format: date-time
        - name: MerchantId
          in: query
          schema:
            type: string
        - name: IsInProgress
          in: query
          schema:
            type: boolean
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementResponseListPagedResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementResponseListPagedResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementResponseListPagedResponseHandler'
  /api/settlements/{settlementId}:
    get:
      summary: Get settlements settlementId
      operationId: getSettlements
      tags:
        - Settlement
      parameters:
        - name: settlementId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/settlements/merchant/{merchantId}:
    get:
      summary: Get settlements merchant merchantId
      operationId: getSettlementsMerchant
      tags:
        - Settlement
      parameters:
        - name: merchantId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementResponseListPagedResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementResponseListPagedResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementResponseListPagedResponseHandler'
  /api/settlements/{settlementId}/merchant/{merchantId}:
    get:
      summary: Get settlements settlementId merchant merchantId
      operationId: getSettlementsMerchant
      tags:
        - Settlement
      parameters:
        - name: settlementId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: merchantId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementResponseResponseHandler'
  /api/settlements/settle-merchant/{merchantId}:
    post:
      summary: Create settlements settle merchant merchantId
      operationId: createSettlementsSettlemerchant
      tags:
        - Settlement
      parameters:
        - name: merchantId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SettleMerchantRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/SettleMerchantRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/SettleMerchantRequest'
      responses:
        '200':
          description: Success
  /api/settlements/{settlementId}/retry:
    post:
      summary: Create settlements settlementId retry
      operationId: createSettlementsRetry
      tags:
        - Settlement
      parameters:
        - name: settlementId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/SettleMerchantResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/SettleMerchantResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/SettleMerchantResponseResponseHandler'
  /api/settlements/mark-settlement-log-as-settled:
    post:
      summary: Create settlements mark settlement log as settled
      operationId: createSettlementsMarksettlementlogassettled
      tags:
        - Settlement
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MarkLogAsSettledRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/MarkLogAsSettledRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/MarkLogAsSettledRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetSettledResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetSettledResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetSettledResponseResponseHandler'
  /api/SettlementAccount/add:
    post:
      summary: Add SettlementAccount add
      operationId: addSettlementaccountAdd
      tags:
        - SettlementAccount
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddSettlementAccountRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AddSettlementAccountRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AddSettlementAccountRequest'
      responses:
        '200':
          description: Success
  /api/SettlementAccount/{settlementAccountId}:
    get:
      summary: Get SettlementAccount settlementAccountId
      operationId: getSettlementaccount
      tags:
        - SettlementAccount
      parameters:
        - name: settlementAccountId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
    patch:
      summary: Update SettlementAccount settlementAccountId
      operationId: updateSettlementaccount
      tags:
        - SettlementAccount
      parameters:
        - name: settlementAccountId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementAccountRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementAccountRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementAccountRequest'
      responses:
        '200':
          description: Success
    delete:
      summary: Delete SettlementAccount settlementAccountId
      operationId: deleteSettlementaccount
      tags:
        - SettlementAccount
      parameters:
        - name: settlementAccountId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/SettlementAccount/{settlementAccountId}/merchant/{merchantId}:
    get:
      summary: Get SettlementAccount settlementAccountId merchant merchantId
      operationId: getSettlementaccountMerchant
      tags:
        - SettlementAccount
      parameters:
        - name: settlementAccountId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: merchantId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
    patch:
      summary: Update SettlementAccount settlementAccountId merchant merchantId
      operationId: updateSettlementaccountMerchant
      tags:
        - SettlementAccount
      parameters:
        - name: settlementAccountId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: merchantId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementAccountRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementAccountRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementAccountRequest'
      responses:
        '200':
          description: Success
    delete:
      summary: Delete SettlementAccount settlementAccountId merchant merchantId
      operationId: deleteSettlementaccountMerchant
      tags:
        - SettlementAccount
      parameters:
        - name: settlementAccountId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: merchantId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/SettlementAccount/all:
    get:
      summary: Get SettlementAccount all
      operationId: getSettlementaccountAll
      tags:
        - SettlementAccount
      parameters:
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/SettlementAccount/merchant/{merchantId}/all:
    get:
      summary: Get SettlementAccount merchant merchantId all
      operationId: getSettlementaccountMerchantAll
      tags:
        - SettlementAccount
      parameters:
        - name: merchantId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/SettlementMapping/add:
    post:
      summary: Add SettlementMapping add
      operationId: addSettlementmappingAdd
      tags:
        - SettlementMapping
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddSettlementMappingRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AddSettlementMappingRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AddSettlementMappingRequest'
      responses:
        '200':
          description: Success
  /api/SettlementMapping/{settlementMappingId}:
    get:
      summary: Get SettlementMapping settlementMappingId
      operationId: getSettlementmapping
      tags:
        - SettlementMapping
      parameters:
        - name: settlementMappingId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
    patch:
      summary: Update SettlementMapping settlementMappingId
      operationId: updateSettlementmapping
      tags:
        - SettlementMapping
      parameters:
        - name: settlementMappingId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementMappingRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementMappingRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementMappingRequest'
      responses:
        '200':
          description: Success
    delete:
      summary: Delete SettlementMapping settlementMappingId
      operationId: deleteSettlementmapping
      tags:
        - SettlementMapping
      parameters:
        - name: settlementMappingId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/SettlementMapping/all:
    get:
      summary: Get SettlementMapping all
      operationId: getSettlementmappingAll
      tags:
        - SettlementMapping
      parameters:
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/SettlementMapping/merchant/{merchantId}/all:
    get:
      summary: Get SettlementMapping merchant merchantId all
      operationId: getSettlementmappingMerchantAll
      tags:
        - SettlementMapping
      parameters:
        - name: merchantId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/settlement-modes:
    get:
      summary: Get settlement modes
      operationId: getSettlementmodes
      tags:
        - SettlementMode
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetSettlementModeResponseListResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetSettlementModeResponseListResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetSettlementModeResponseListResponseHandler'
  /api/settlement-modes/{settlementMode}/code/{settlementModeCode}/merchant/{merchantId}:
    post:
      summary: Create settlement modes settlementMode code settlementModeCode merchant merchantId
      operationId: createSettlementmodesCodeMerchant
      tags:
        - SettlementMode
      parameters:
        - name: settlementMode
          in: path
          required: true
          schema:
            type: string
        - name: settlementModeCode
          in: path
          required: true
          schema:
            type: string
        - name: merchantId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ValidateSettlementModeCodeResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateSettlementModeCodeResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/ValidateSettlementModeCodeResponseResponseHandler'
  /api/SettlementProfile/all:
    get:
      summary: Get SettlementProfile all
      operationId: getSettlementprofileAll
      tags:
        - SettlementProfile
      parameters:
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementProfileResponseListPagedResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementProfileResponseListPagedResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementProfileResponseListPagedResponseHandler'
  /api/SettlementProfile/merchant/{merchantId}:
    post:
      summary: Create SettlementProfile merchant merchantId
      operationId: createSettlementprofileMerchant
      tags:
        - SettlementProfile
      parameters:
        - name: merchantId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddUpdateMerchantSettlementProfileRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AddUpdateMerchantSettlementProfileRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AddUpdateMerchantSettlementProfileRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementProfileResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementProfileResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementProfileResponseResponseHandler'
  /api/SettlementProfile/merchant/{merchantId}/profile:
    get:
      summary: Get SettlementProfile merchant merchantId profile
      operationId: getSettlementprofileMerchantProfile
      tags:
        - SettlementProfile
      parameters:
        - name: merchantId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementProfileResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementProfileResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementProfileResponseResponseHandler'
  /api/SettlementProfile/merchant/{merchantId}/profile/{profileId}:
    patch:
      summary: Update SettlementProfile merchant merchantId profile profileId
      operationId: updateSettlementprofileMerchantProfile
      tags:
        - SettlementProfile
      parameters:
        - name: merchantId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: profileId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddUpdateMerchantSettlementProfileRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AddUpdateMerchantSettlementProfileRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AddUpdateMerchantSettlementProfileRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementProfileResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementProfileResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetMerchantSettlementProfileResponseResponseHandler'
  /api/settlement-split-config/add:
    post:
      summary: Add settlement split config add
      operationId: addSettlementsplitconfigAdd
      tags:
        - SettlementSplitCode
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddSettlementSplitCodeRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AddSettlementSplitCodeRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AddSettlementSplitCodeRequest'
      responses:
        '200':
          description: Success
  /api/settlement-split-config/{settlementSplitConfigId}:
    get:
      summary: Get settlement split config settlementSplitConfigId
      operationId: getSettlementsplitconfig
      tags:
        - SettlementSplitCode
      parameters:
        - name: settlementSplitConfigId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
    patch:
      summary: Update settlement split config settlementSplitConfigId
      operationId: updateSettlementsplitconfig
      tags:
        - SettlementSplitCode
      parameters:
        - name: settlementSplitConfigId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementSplitCodeRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementSplitCodeRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementSplitCodeRequest'
      responses:
        '200':
          description: Success
    delete:
      summary: Delete settlement split config settlementSplitConfigId
      operationId: deleteSettlementsplitconfig
      tags:
        - SettlementSplitCode
      parameters:
        - name: settlementSplitConfigId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/settlement-split-config/all:
    get:
      summary: Get settlement split config all
      operationId: getSettlementsplitconfigAll
      tags:
        - SettlementSplitCode
      parameters:
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/settlement-split-config/merchant/{merchantId}/all:
    get:
      summary: Get settlement split config merchant merchantId all
      operationId: getSettlementsplitconfigMerchantAll
      tags:
        - SettlementSplitCode
      parameters:
        - name: merchantId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/settlement-split-config/{settlementSplitConfigId}/SplitAccount/add:
    post:
      summary: Add settlement split config settlementSplitConfigId SplitAccount add
      operationId: addSettlementsplitconfigSplitaccountAdd
      tags:
        - SettlementSplitCode
      parameters:
        - name: settlementSplitConfigId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddSettlementSplitAccountRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AddSettlementSplitAccountRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AddSettlementSplitAccountRequest'
      responses:
        '200':
          description: Success
  /api/settlement-split-config/{settlementSplitConfigId}/SplitAccount/{splitAccountId}:
    patch:
      summary: Update settlement split config settlementSplitConfigId SplitAccount splitAccountId
      operationId: updateSettlementsplitconfigSplitaccount
      tags:
        - SettlementSplitCode
      parameters:
        - name: settlementSplitConfigId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: splitAccountId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementSplitAccountRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementSplitAccountRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementSplitAccountRequest'
      responses:
        '200':
          description: Success
    delete:
      summary: Delete settlement split config settlementSplitConfigId SplitAccount splitAccountId
      operationId: deleteSettlementsplitconfigSplitaccount
      tags:
        - SettlementSplitCode
      parameters:
        - name: settlementSplitConfigId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: splitAccountId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/SettlementSplitConfigAccount/add:
    post:
      summary: Add SettlementSplitConfigAccount add
      operationId: addSettlementsplitconfigaccountAdd
      tags:
        - SettlementSplitConfigAccount
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddSettlementSplitAccountRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AddSettlementSplitAccountRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AddSettlementSplitAccountRequest'
      responses:
        '200':
          description: Success
  /api/SettlementSplitConfigAccount/{settlementSplitAccountId}:
    get:
      summary: Get SettlementSplitConfigAccount settlementSplitAccountId
      operationId: getSettlementsplitconfigaccount
      tags:
        - SettlementSplitConfigAccount
      parameters:
        - name: settlementSplitAccountId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
    patch:
      summary: Update SettlementSplitConfigAccount settlementSplitAccountId
      operationId: updateSettlementsplitconfigaccount
      tags:
        - SettlementSplitConfigAccount
      parameters:
        - name: settlementSplitAccountId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementSplitAccountRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementSplitAccountRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementSplitAccountRequest'
      responses:
        '200':
          description: Success
    delete:
      summary: Delete SettlementSplitConfigAccount settlementSplitAccountId
      operationId: deleteSettlementsplitconfigaccount
      tags:
        - SettlementSplitConfigAccount
      parameters:
        - name: settlementSplitAccountId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/SettlementSplitConfigAccount/all:
    get:
      summary: Get SettlementSplitConfigAccount all
      operationId: getSettlementsplitconfigaccountAll
      tags:
        - SettlementSplitConfigAccount
      parameters:
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
  /api/tunnel/add:
    post:
      summary: Add tunnel add
      operationId: addTunnelAdd
      tags:
        - SettlementTunnel
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddSettlementTunnelRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AddSettlementTunnelRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AddSettlementTunnelRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetSettlementTunnelResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetSettlementTunnelResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetSettlementTunnelResponseResponseHandler'
  /api/tunnel/{settlementTunnelId}/activate-deactivate:
    patch:
      summary: Update tunnel settlementTunnelId activate deactivate
      operationId: updateTunnelActivatedeactivate
      tags:
        - SettlementTunnel
      parameters:
        - name: settlementTunnelId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ActivateDeactivateSettlementTunnelRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/ActivateDeactivateSettlementTunnelRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/ActivateDeactivateSettlementTunnelRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetSettlementTunnelResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetSettlementTunnelResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetSettlementTunnelResponseResponseHandler'
  /api/tunnel/{settlementTunnelId}:
    get:
      summary: Get tunnel settlementTunnelId
      operationId: getTunnel
      tags:
        - SettlementTunnel
      parameters:
        - name: settlementTunnelId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: withChannels
          in: query
          schema:
            type: boolean
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetSettlementTunnelResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetSettlementTunnelResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetSettlementTunnelResponseResponseHandler'
    patch:
      summary: Update tunnel settlementTunnelId
      operationId: updateTunnel
      tags:
        - SettlementTunnel
      parameters:
        - name: settlementTunnelId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementTunnelRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementTunnelRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateSettlementTunnelRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetSettlementTunnelResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetSettlementTunnelResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetSettlementTunnelResponseResponseHandler'
    delete:
      summary: Delete tunnel settlementTunnelId
      operationId: deleteTunnel
      tags:
        - SettlementTunnel
      parameters:
        - name: settlementTunnelId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/DeleteSettlementTunnelResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteSettlementTunnelResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/DeleteSettlementTunnelResponseResponseHandler'
  /api/tunnel/all:
    get:
      summary: Get tunnel all
      operationId: getTunnelAll
      tags:
        - SettlementTunnel
      parameters:
        - name: PageNumber
          in: query
          schema:
            type: integer
            format: int32
        - name: PageSize
          in: query
          schema:
            type: integer
            format: int32
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetSettlementTunnelResponseListPagedResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetSettlementTunnelResponseListPagedResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetSettlementTunnelResponseListPagedResponseHandler'
  /api/tunnel/{settlementTunnelId}/add-channel:
    post:
      summary: Add tunnel settlementTunnelId add channel
      operationId: addTunnelAddchannel
      tags:
        - SettlementTunnel
      parameters:
        - name: settlementTunnelId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddChannelSettlementTunnelRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AddChannelSettlementTunnelRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AddChannelSettlementTunnelRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetChannelSettlementResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetChannelSettlementResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetChannelSettlementResponseResponseHandler'
  /api/tunnel/{settlementTunnelId}/update-channel/{channelSettlementId}:
    patch:
      summary: Update tunnel settlementTunnelId update channel channelSettlementId
      operationId: updateTunnelUpdatechannel
      tags:
        - SettlementTunnel
      parameters:
        - name: settlementTunnelId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: channelSettlementId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateChannelSettlementTunnelRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateChannelSettlementTunnelRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateChannelSettlementTunnelRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/GetChannelSettlementResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/GetChannelSettlementResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/GetChannelSettlementResponseResponseHandler'
  /api/tunnel/{settlementTunnelId}/remove-channel/{channelSettlementId}:
    delete:
      summary: Delete tunnel settlementTunnelId remove channel channelSettlementId
      operationId: deleteTunnelRemovechannel
      tags:
        - SettlementTunnel
      parameters:
        - name: settlementTunnelId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: channelSettlementId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/DeleteSettlementTunnelResponseResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteSettlementTunnelResponseResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/DeleteSettlementTunnelResponseResponseHandler'
  /api/WemaNIPService/GetBalance:
    post:
      summary: Create WemaNIPService GetBalance
      operationId: createWemanipserviceGetbalance
      tags:
        - WemaNIPService
      parameters:
        - name: accountNumber
          in: query
          schema:
            type: string
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/WemaGetAccountStatementResponseListResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/WemaGetAccountStatementResponseListResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/WemaGetAccountStatementResponseListResponseHandler'
  /api/WemaNIPService/ResendFunds:
    post:
      summary: Create WemaNIPService ResendFunds
      operationId: createWemanipserviceResendfunds
      tags:
        - WemaNIPService
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResendFundsRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/ResendFundsRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/ResendFundsRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/WemaGetAccountStatementResponseListResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/WemaGetAccountStatementResponseListResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/WemaGetAccountStatementResponseListResponseHandler'
  /api/WemaNIPService/GetStatement:
    post:
      summary: Create WemaNIPService GetStatement
      operationId: createWemanipserviceGetstatement
      tags:
        - WemaNIPService
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WemaGetAccountStatementRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/WemaGetAccountStatementRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/WemaGetAccountStatementRequest'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/WemaGetAccountStatementResponseListResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/WemaGetAccountStatementResponseListResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/WemaGetAccountStatementResponseListResponseHandler'
  /api/WemaNIPService/NameInquiry:
    post:
      summary: Create WemaNIPService NameInquiry
      operationId: createWemanipserviceNameinquiry
      tags:
        - WemaNIPService
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NameInquiry'
          text/json:
            schema:
              $ref: '#/components/schemas/NameInquiry'
          application/*+json:
            schema:
              $ref: '#/components/schemas/NameInquiry'
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/StringResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/StringResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/StringResponseHandler'
  /api/WemaNIPService/NipBanks:
    get:
      summary: Get WemaNIPService NipBanks
      operationId: getWemanipserviceNipbanks
      tags:
        - WemaNIPService
      parameters:
        - name: GEMSAUTH
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/NipBanksListResponseHandler'
            application/json:
              schema:
                $ref: '#/components/schemas/NipBanksListResponseHandler'
            text/json:
              schema:
                $ref: '#/components/schemas/NipBanksListResponseHandler'
components:
  schemas:
    ActivateDeactivateSettlementTunnelRequest:
      type: object
      properties:
        active:
          type: boolean
      additionalProperties: false
    AddAutoSettlementChargeRequest:
      type: object
      properties:
        settlementProfileId:
          type: string
          format: uuid
        charge:
          type: number
          format: double
        channel:
          type: string
          nullable: true
      additionalProperties: false
    AddBatchPeriodRequest:
      type: object
      properties:
        fromHour:
          type: integer
          format: int32
        fromMinute:
          type: integer
          format: int32
        toHour:
          type: integer
          format: int32
        toMinute:
          type: integer
          format: int32
        periodHour:
          type: integer
          format: int32
      additionalProperties: false
    AddChannelSettlementTunnelRequest:
      type: object
      properties:
        channel:
          type: string
          nullable: true
        partnerCode:
          type: string
          nullable: true
      additionalProperties: false
    AddSettlementAccountRequest:
      type: object
      properties:
        bankCode:
          type: string
          nullable: true
        bankName:
          type: string
          nullable: true
        bankAccountName:
          type: string
          nullable: true
        bankAccountNo:
          type: string
          nullable: true
        isPrimary:
          type: boolean
        settlementProfileId:
          type: string
          format: uuid
      additionalProperties: false
    AddSettlementMappingRequest:
      type: object
      properties:
        settlementProfileId:
          type: string
          format: uuid
        name:
          type: string
          nullable: true
        mapping:
          type: array
          items:
            $ref: '#/components/schemas/MappingModel'
          nullable: true
      additionalProperties: false
    AddSettlementSplitAccountRequest:
      type: object
      properties:
        splitValue:
          type: number
          format: double
        settlementAccountId:
          type: string
          format: uuid
      additionalProperties: false
    AddSettlementSplitCodeRequest:
      type: object
      properties:
        name:
          type: string
          nullable: true
        splitType:
          $ref: '#/components/schemas/SplitType'
        merchantId:
          type: string
          format: uuid
        splitAccounts:
          type: array
          items:
            $ref: '#/components/schemas/AddSettlementSplitAccountRequest'
          nullable: true
      additionalProperties: false
    AddSettlementTunnelRequest:
      type: object
      properties:
        name:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        slug:
          type: string
          nullable: true
        active:
          type: boolean
        channels:
          type: array
          items:
            $ref: '#/components/schemas/AddChannelSettlementTunnelRequest'
          nullable: true
      additionalProperties: false
    AddUpdateGemspayBankAccountRequest:
      type: object
      properties:
        accountName:
          type: string
          nullable: true
        accountNumber:
          type: string
          nullable: true
        bankCode:
          type: string
          nullable: true
        bankName:
          type: string
          nullable: true
        kycLevel:
          type: string
          nullable: true
        bvn:
          type: string
          nullable: true
        mandateReferenceNumber:
          type: string
          nullable: true
        type:
          $ref: '#/components/schemas/GemspayBankAccountTypes'
        purpose:
          type: string
          nullable: true
        remark:
          type: string
          nullable: true
        paymentChannelCode:
          type: string
          nullable: true
        paymentChannelName:
          type: string
          nullable: true
        paymentChannelSlug:
          type: string
          nullable: true
        paymentChannelId:
          type: string
          nullable: true
        partnerId:
          type: string
          format: uuid
        initiatorName:
          type: string
          nullable: true
        initiatorEmail:
          type: string
          nullable: true
      additionalProperties: false
    AddUpdateMerchantSettlementProfileRequest:
      type: object
      properties:
        merchantBusinessName:
          type: string
          nullable: true
        autoSettlement:
          type: boolean
        isAdminRequest:
          type: boolean
        settlementAccount:
          $ref: '#/components/schemas/AddSettlementAccountRequest'
      additionalProperties: false
    AddUpdateServiceProviderNipConnectorMapRequest:
      type: object
      properties:
        serviceProviderId:
          type: string
          format: uuid
        nipConnector:
          $ref: '#/components/schemas/NipConnectors'
      additionalProperties: false
    AddUpdateServiceSetupRequest:
      type: object
      properties:
        displayName:
          type: string
          nullable: true
        value:
          type: string
          nullable: true
      additionalProperties: false
    BalanceInquiryRequest:
      type: object
      properties:
        accountNumber:
          type: string
          nullable: true
        accountName:
          type: string
          nullable: true
        bvn:
          type: string
          nullable: true
        bankCode:
          type: string
          nullable: true
      additionalProperties: false
    BalanceInquiryResponse:
      type: object
      properties:
        sessionId:
          type: string
          nullable: true
        transactionId:
          type: string
          nullable: true
        accountName:
          type: string
          nullable: true
        accountNumber:
          type: string
          nullable: true
        availableBalance:
          type: string
          nullable: true
      additionalProperties: false
    BalanceInquiryResponseNIP_ResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        code:
          type: string
          nullable: true
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/BalanceInquiryResponse'
        serviceProvider:
          type: string
          nullable: true
        timeStamp:
          type: string
          format: date-time
      additionalProperties: false
    BankStatementFormat:
      enum:
        - none
        - mt940
      type: string
    BankStatementTransactionType:
      enum:
        - credit
        - debit
      type: string
    DeleteAutoSettlementChargeResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
      additionalProperties: false
    DeleteAutoSettlementChargeResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/DeleteAutoSettlementChargeResponse'
      additionalProperties: false
    DeleteGemspayBankAccountRequest:
      type: object
      properties:
        initiatorEmail:
          type: string
          nullable: true
        initiatorName:
          type: string
          nullable: true
      additionalProperties: false
    DeleteGemspayBankAccountResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
      additionalProperties: false
    DeleteGemspayBankAccountResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/DeleteGemspayBankAccountResponse'
      additionalProperties: false
    DeleteSettlementTunnelResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
      additionalProperties: false
    DeleteSettlementTunnelResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/DeleteSettlementTunnelResponse'
      additionalProperties: false
    FundReversalRequest:
      type: object
      properties:
        transactionId:
          type: string
          format: uuid
        merchantId:
          type: string
          format: uuid
        amountPaid:
          type: number
          format: double
        transactionReference:
          type: string
          nullable: true
        serviceProviderName:
          type: string
          nullable: true
        serviceProviderCode:
          type: string
          nullable: true
        serviceProviderSlug:
          type: string
          nullable: true
        channel:
          type: string
          nullable: true
        reference:
          type: string
          nullable: true
        customerBankCode:
          type: string
          nullable: true
        customerBankName:
          type: string
          nullable: true
        customerAccountName:
          type: string
          nullable: true
        customerAccountNumber:
          type: string
          nullable: true
      additionalProperties: false
    FundTransferRequest:
      type: object
      properties:
        sourceInstitutioncode:
          type: string
          nullable: true
        amount:
          type: string
          nullable: true
        beneficiaryAccountName:
          type: string
          nullable: true
        beneficiaryAccountNumber:
          type: string
          nullable: true
        beneficiaryBankVerificationNumber:
          type: string
          nullable: true
        beneficiaryKYCLevel:
          type: string
          nullable: true
        channelCode:
          type: string
          nullable: true
        originatorAccountName:
          type: string
          nullable: true
        originatorAccountNumber:
          type: string
          nullable: true
        originatorBankVerificationNumber:
          type: string
          nullable: true
        originatorKYCLevel:
          type: string
          nullable: true
        destinationInstitutionCode:
          type: string
          nullable: true
        mandateReferenceNumber:
          type: string
          nullable: true
        nameEnquiryRef:
          type: string
          nullable: true
        originatorNarration:
          type: string
          nullable: true
        paymentReference:
          type: string
          nullable: true
        transactionId:
          type: string
          nullable: true
        transactionLocation:
          type: string
          nullable: true
        beneficiaryNarration:
          type: string
          nullable: true
        billerId:
          type: string
          nullable: true
      additionalProperties: false
    FundTransferResponse:
      type: object
      properties:
        retryCount:
          type: integer
          format: int32
        responseCode:
          type: string
          nullable: true
        responseCodeDefinition:
          type: string
          nullable: true
        paymentReference:
          type: string
          nullable: true
        sessionId:
          type: string
          nullable: true
        transactionId:
          type: string
          nullable: true
        narration:
          type: string
          nullable: true
        tsqData:
          $ref: '#/components/schemas/TransactionStatusRequeryResponse'
      additionalProperties: false
    FundTransferResponseNIP_ResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        code:
          type: string
          nullable: true
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/FundTransferResponse'
        serviceProvider:
          type: string
          nullable: true
        timeStamp:
          type: string
          format: date-time
      additionalProperties: false
    GemspayBankAccountChangeLogResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        preImage:
          $ref: '#/components/schemas/GetGemspayBankAccountOnlyResponse'
        postImage:
          $ref: '#/components/schemas/GetGemspayBankAccountOnlyResponse'
        initiatorName:
          type: string
          nullable: true
        initiatorEmail:
          type: string
          nullable: true
        updateNote:
          type: string
          nullable: true
        action:
          type: string
          nullable: true
        createdAt:
          type: string
          format: date-time
        modifiedAt:
          type: string
          format: date-time
      additionalProperties: false
    GemspayBankAccountTypes:
      enum:
        - collection
        - settlement
        - operation
      type: string
    GetAutoSettlementChargeResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        charge:
          type: number
          format: double
        channel:
          type: string
          nullable: true
        settlementProfile:
          $ref: '#/components/schemas/GetSettlementProfileResponse'
        createdAt:
          type: string
          format: date-time
        modifiedAt:
          type: string
          format: date-time
      additionalProperties: false
    GetAutoSettlementChargeResponseListPagedResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetAutoSettlementChargeResponse'
          nullable: true
        pageNumber:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
        firstPage:
          type: string
          format: uri
          nullable: true
        lastPage:
          type: string
          format: uri
          nullable: true
        totalPages:
          type: integer
          format: int32
        totalRecords:
          type: integer
          format: int32
        nextPage:
          type: string
          format: uri
          nullable: true
        previousPage:
          type: string
          format: uri
          nullable: true
      additionalProperties: false
    GetAutoSettlementChargeResponseListResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetAutoSettlementChargeResponse'
          nullable: true
      additionalProperties: false
    GetAutoSettlementChargeResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/GetAutoSettlementChargeResponse'
      additionalProperties: false
    GetBankStatementResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        reference:
          type: string
          nullable: true
        bankStatementFormat:
          $ref: '#/components/schemas/BankStatementFormat'
        openingBalance:
          type: number
          format: double
        closingBalance:
          type: number
          format: double
        startDate:
          type: string
          format: date-time
        endDate:
          type: string
          format: date-time
        transactionCount:
          type: integer
          format: int32
        transactions:
          type: array
          items:
            $ref: '#/components/schemas/GetBankStatementTransactionResponse'
          nullable: true
      additionalProperties: false
    GetBankStatementResponseListPagedResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetBankStatementResponse'
          nullable: true
        pageNumber:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
        firstPage:
          type: string
          format: uri
          nullable: true
        lastPage:
          type: string
          format: uri
          nullable: true
        totalPages:
          type: integer
          format: int32
        totalRecords:
          type: integer
          format: int32
        nextPage:
          type: string
          format: uri
          nullable: true
        previousPage:
          type: string
          format: uri
          nullable: true
      additionalProperties: false
    GetBankStatementResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/GetBankStatementResponse'
      additionalProperties: false
    GetBankStatementTransactionResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        statementLineDetails:
          type: string
          nullable: true
        transactionRemark:
          type: string
          nullable: true
        amount:
          type: number
          format: double
        transactionType:
          $ref: '#/components/schemas/BankStatementTransactionType'
        reconciled:
          type: boolean
        reconciledAt:
          type: string
          format: date-time
        isServiceCharge:
          type: boolean
        createdAt:
          type: string
          format: date-time
        modifiedAt:
          type: string
          format: date-time
      additionalProperties: false
    GetBankStatementTransactionResponseListPagedResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetBankStatementTransactionResponse'
          nullable: true
        pageNumber:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
        firstPage:
          type: string
          format: uri
          nullable: true
        lastPage:
          type: string
          format: uri
          nullable: true
        totalPages:
          type: integer
          format: int32
        totalRecords:
          type: integer
          format: int32
        nextPage:
          type: string
          format: uri
          nullable: true
        previousPage:
          type: string
          format: uri
          nullable: true
      additionalProperties: false
    GetBatchPeriodResponse:
      type: object
      properties:
        batchPeriodId:
          type: string
          format: uuid
        batchPeriodCode:
          type: string
          nullable: true
        default:
          type: boolean
        fromTime:
          type: string
          nullable: true
        toTime:
          type: string
          nullable: true
        periodHour:
          type: integer
          format: int32
        createdAt:
          type: string
          format: date-time
        modifiedAt:
          type: string
          format: date-time
        modifiedBy:
          type: string
          nullable: true
        modifiedByEmail:
          type: string
          nullable: true
        settlementProfiles:
          type: array
          items:
            $ref: '#/components/schemas/GetSettlementProfileResponse'
          nullable: true
      additionalProperties: false
    GetBatchPeriodResponseListResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetBatchPeriodResponse'
          nullable: true
      additionalProperties: false
    GetBatchPeriodResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/GetBatchPeriodResponse'
      additionalProperties: false
    GetChannelSettlementResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        channel:
          type: string
          nullable: true
        partnerCode:
          type: string
          nullable: true
        isDefault:
          type: boolean
        isActive:
          type: boolean
        createdAt:
          type: string
          format: date-time
        modifiedAt:
          type: string
          format: date-time
      additionalProperties: false
    GetChannelSettlementResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/GetChannelSettlementResponse'
      additionalProperties: false
    GetChargeRevenueResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        amount:
          type: number
          format: double
        settlementSourceReference:
          type: string
          nullable: true
        settlementSourceTransactionCount:
          type: integer
          format: int32
        settlementType:
          $ref: '#/components/schemas/SettlementTypes'
        settledId:
          type: string
          format: uuid
        processed:
          type: boolean
        processingRetryCount:
          type: integer
          format: int32
        createdAt:
          type: string
          format: date-time
        modifiedAt:
          type: string
          format: date-time
      additionalProperties: false
    GetChargeRevenueResponseListPagedResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetChargeRevenueResponse'
          nullable: true
        pageNumber:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
        firstPage:
          type: string
          format: uri
          nullable: true
        lastPage:
          type: string
          format: uri
          nullable: true
        totalPages:
          type: integer
          format: int32
        totalRecords:
          type: integer
          format: int32
        nextPage:
          type: string
          format: uri
          nullable: true
        previousPage:
          type: string
          format: uri
          nullable: true
      additionalProperties: false
    GetFundReversalResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        transactionId:
          type: string
          format: uuid
        merchantId:
          type: string
          format: uuid
        amountPaid:
          type: number
          format: double
        transactionReference:
          type: string
          nullable: true
        serviceProviderName:
          type: string
          nullable: true
        serviceProviderCode:
          type: string
          nullable: true
        serviceProviderSlug:
          type: string
          nullable: true
        channel:
          type: string
          nullable: true
        reference:
          type: string
          nullable: true
        customerBankCode:
          type: string
          nullable: true
        customerBankName:
          type: string
          nullable: true
        customerAccountName:
          type: string
          nullable: true
        customerAccountNumber:
          type: string
          nullable: true
        retryCount:
          type: integer
          format: int32
        fundReservalReference:
          type: string
          nullable: true
        processed:
          type: boolean
        sourceBankCode:
          type: string
          nullable: true
        sourceBankName:
          type: string
          nullable: true
        sourceAccountName:
          type: string
          nullable: true
        sourceAccountNumber:
          type: string
          nullable: true
        createdAt:
          type: string
          format: date-time
        modifiedAt:
          type: string
          format: date-time
      additionalProperties: false
    GetFundReversalResponseListPagedResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetFundReversalResponse'
          nullable: true
        pageNumber:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
        firstPage:
          type: string
          format: uri
          nullable: true
        lastPage:
          type: string
          format: uri
          nullable: true
        totalPages:
          type: integer
          format: int32
        totalRecords:
          type: integer
          format: int32
        nextPage:
          type: string
          format: uri
          nullable: true
        previousPage:
          type: string
          format: uri
          nullable: true
      additionalProperties: false
    GetFundReversalResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/GetFundReversalResponse'
      additionalProperties: false
    GetGemspayBankAccountOnlyResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        accountName:
          type: string
          nullable: true
        accountNumber:
          type: string
          nullable: true
        bankCode:
          type: string
          nullable: true
        bankName:
          type: string
          nullable: true
        kycLevel:
          type: string
          nullable: true
        bvn:
          type: string
          nullable: true
        mandateReferenceNumber:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        purpose:
          type: string
          nullable: true
        remark:
          type: string
          nullable: true
        paymentChannelCode:
          type: string
          nullable: true
        paymentChannelName:
          type: string
          nullable: true
        paymentChannelSlug:
          type: string
          nullable: true
        active:
          type: boolean
        lastModifiedBy:
          type: string
          nullable: true
        lastModifiedByEmail:
          type: string
          nullable: true
        createdAt:
          type: string
          format: date-time
        modifiedAt:
          type: string
          format: date-time
      additionalProperties: false
    GetGemspayBankAccountResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        accountName:
          type: string
          nullable: true
        accountNumber:
          type: string
          nullable: true
        bankCode:
          type: string
          nullable: true
        bankName:
          type: string
          nullable: true
        kycLevel:
          type: string
          nullable: true
        bvn:
          type: string
          nullable: true
        mandateReferenceNumber:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        purpose:
          type: string
          nullable: true
        remark:
          type: string
          nullable: true
        paymentChannelCode:
          type: string
          nullable: true
        paymentChannelName:
          type: string
          nullable: true
        paymentChannelSlug:
          type: string
          nullable: true
        partnerName:
          type: string
          nullable: true
        partnerCode:
          type: string
          nullable: true
        partnerId:
          type: string
          nullable: true
        active:
          type: boolean
        lastModifiedBy:
          type: string
          nullable: true
        lastModifiedByEmail:
          type: string
          nullable: true
        createdAt:
          type: string
          format: date-time
        modifiedAt:
          type: string
          format: date-time
        changeLogs:
          type: array
          items:
            $ref: '#/components/schemas/GemspayBankAccountChangeLogResponse'
          nullable: true
      additionalProperties: false
    GetGemspayBankAccountResponseListPagedResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetGemspayBankAccountResponse'
          nullable: true
        pageNumber:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
        firstPage:
          type: string
          format: uri
          nullable: true
        lastPage:
          type: string
          format: uri
          nullable: true
        totalPages:
          type: integer
          format: int32
        totalRecords:
          type: integer
          format: int32
        nextPage:
          type: string
          format: uri
          nullable: true
        previousPage:
          type: string
          format: uri
          nullable: true
      additionalProperties: false
    GetGemspayBankAccountResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/GetGemspayBankAccountResponse'
      additionalProperties: false
    GetMerchantSettlementProfileResponse:
      type: object
      properties:
        settlementProfileId:
          type: string
          format: uuid
        merchantBusinessName:
          type: string
          nullable: true
        merchantOnboardingId:
          type: string
          nullable: true
        autoSettlement:
          type: boolean
      additionalProperties: false
    GetMerchantSettlementProfileResponseListPagedResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetMerchantSettlementProfileResponse'
          nullable: true
        pageNumber:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
        firstPage:
          type: string
          format: uri
          nullable: true
        lastPage:
          type: string
          format: uri
          nullable: true
        totalPages:
          type: integer
          format: int32
        totalRecords:
          type: integer
          format: int32
        nextPage:
          type: string
          format: uri
          nullable: true
        previousPage:
          type: string
          format: uri
          nullable: true
      additionalProperties: false
    GetMerchantSettlementProfileResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/GetMerchantSettlementProfileResponse'
      additionalProperties: false
    GetMerchantSettlementResponse:
      type: object
      properties:
        settlementLogId:
          type: string
          format: uuid
        transactionId:
          type: string
          format: uuid
        transactionReference:
          type: string
          nullable: true
        transactionDescription:
          type: string
          nullable: true
        transactionCharge:
          type: number
          format: double
        transactionChargeCap:
          type: number
          format: double
        transactionChargeValue:
          type: number
          format: double
        transactionDate:
          type: string
          format: date-time
        transactionChannel:
          type: string
          nullable: true
        settledPerChannel:
          type: boolean
        serviceProviderName:
          type: string
          nullable: true
        serviceProviderCode:
          type: string
          nullable: true
        amountPaid:
          type: number
          format: double
        amountToSettle:
          type: number
          format: double
        sourceBankCode:
          type: string
          nullable: true
        sourceBankName:
          type: string
          nullable: true
        sourceAccountName:
          type: string
          nullable: true
        sourceAccountNumber:
          type: string
          nullable: true
        merchantId:
          type: string
          format: uuid
        merchantName:
          type: string
          nullable: true
        settlementMode:
          type: string
          nullable: true
        settlementProfileSplitCode:
          type: string
          nullable: true
        isBatch:
          type: boolean
        autoSettlement:
          type: boolean
        autoSettlementCharge:
          type: number
          format: double
        retryCount:
          type: integer
          format: int32
        batchReference:
          type: string
          nullable: true
        merchantUniqueBatchReference:
          type: string
          nullable: true
        splitReference:
          type: string
          nullable: true
        settlementInProgress:
          type: boolean
        createdAt:
          type: string
          format: date-time
        modifiedAt:
          type: string
          format: date-time
        settlementAccount:
          $ref: '#/components/schemas/GetSettlementAccountResponse'
      additionalProperties: false
    GetMerchantSettlementResponseListPagedResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetMerchantSettlementResponse'
          nullable: true
        pageNumber:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
        firstPage:
          type: string
          format: uri
          nullable: true
        lastPage:
          type: string
          format: uri
          nullable: true
        totalPages:
          type: integer
          format: int32
        totalRecords:
          type: integer
          format: int32
        nextPage:
          type: string
          format: uri
          nullable: true
        previousPage:
          type: string
          format: uri
          nullable: true
      additionalProperties: false
    GetMerchantSettlementResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/GetMerchantSettlementResponse'
      additionalProperties: false
    GetServiceProviderNipConnectorMapResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        serviceProviderId:
          type: string
          format: uuid
        serviceProviderName:
          type: string
          nullable: true
        nipConnector:
          $ref: '#/components/schemas/NipConnectors'
        createdAt:
          type: string
          format: date-time
        modifiedAt:
          type: string
          format: date-time
      additionalProperties: false
    GetServiceProviderNipConnectorMapResponseListPagedResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetServiceProviderNipConnectorMapResponse'
          nullable: true
        pageNumber:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
        firstPage:
          type: string
          format: uri
          nullable: true
        lastPage:
          type: string
          format: uri
          nullable: true
        totalPages:
          type: integer
          format: int32
        totalRecords:
          type: integer
          format: int32
        nextPage:
          type: string
          format: uri
          nullable: true
        previousPage:
          type: string
          format: uri
          nullable: true
      additionalProperties: false
    GetServiceProviderNipConnectorMapResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/GetServiceProviderNipConnectorMapResponse'
      additionalProperties: false
    GetServiceSetupResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        displayName:
          type: string
          nullable: true
        value:
          type: string
          nullable: true
        slug:
          type: string
          nullable: true
        createdAt:
          type: string
          format: date-time
        modifiedAt:
          type: string
          format: date-time
      additionalProperties: false
    GetServiceSetupResponseListPagedResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetServiceSetupResponse'
          nullable: true
        pageNumber:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
        firstPage:
          type: string
          format: uri
          nullable: true
        lastPage:
          type: string
          format: uri
          nullable: true
        totalPages:
          type: integer
          format: int32
        totalRecords:
          type: integer
          format: int32
        nextPage:
          type: string
          format: uri
          nullable: true
        previousPage:
          type: string
          format: uri
          nullable: true
      additionalProperties: false
    GetServiceSetupResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/GetServiceSetupResponse'
      additionalProperties: false
    GetSettledResponse:
      type: object
      properties:
        settledId:
          type: string
          format: uuid
        settlementAccountID:
          type: string
          nullable: true
        transactionReference:
          type: string
          nullable: true
        amount:
          type: number
          format: double
        charge:
          type: number
          format: double
        description:
          type: string
          nullable: true
        transactionDescription:
          type: string
          nullable: true
        transactionSessionId:
          type: string
          nullable: true
        settlementMode:
          type: string
          nullable: true
        merchantId:
          type: string
          format: uuid
        createdAt:
          type: string
          format: date-time
        settlementAccount:
          $ref: '#/components/schemas/GetSettlementAccountOnlyResponse'
        settlementProfile:
          $ref: '#/components/schemas/GetSettlementProfileResponse'
      additionalProperties: false
    GetSettledResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/GetSettledResponse'
      additionalProperties: false
    GetSettlementAccountOnlyResponse:
      type: object
      properties:
        settlementAccountId:
          type: string
          format: uuid
        bankCode:
          type: string
          nullable: true
        bankName:
          type: string
          nullable: true
        bankAccountName:
          type: string
          nullable: true
        bankAccountNo:
          type: string
          nullable: true
        isPrimary:
          type: boolean
        code:
          type: string
          nullable: true
      additionalProperties: false
    GetSettlementAccountResponse:
      type: object
      properties:
        settlementAccountId:
          type: string
          format: uuid
        bankCode:
          type: string
          nullable: true
        bankName:
          type: string
          nullable: true
        bankAccountName:
          type: string
          nullable: true
        bankAccountNo:
          type: string
          nullable: true
        code:
          type: string
          nullable: true
        isPrimary:
          type: boolean
        merchantId:
          type: string
          format: uuid
        createdAt:
          type: string
          format: date-time
        modifiedAt:
          type: string
          format: date-time
        settlementProfile:
          $ref: '#/components/schemas/GetMerchantSettlementProfileResponse'
      additionalProperties: false
    GetSettlementModeResponse:
      type: object
      properties:
        name:
          type: string
          nullable: true
        slug:
          type: string
          nullable: true
      additionalProperties: false
    GetSettlementModeResponseListResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetSettlementModeResponse'
          nullable: true
      additionalProperties: false
    GetSettlementProfileResponse:
      type: object
      properties:
        merchantBusinessName:
          type: string
          nullable: true
        merchantOnboardingId:
          type: string
          nullable: true
        autoSettlement:
          type: boolean
      additionalProperties: false
    GetSettlementProfileWithBatchPeriodResponse:
      type: object
      properties:
        settlementProfileId:
          type: string
          format: uuid
        merchantBusinessName:
          type: string
          nullable: true
        merchantOnboardingId:
          type: string
          nullable: true
        autoSettlement:
          type: boolean
        batchPeriods:
          type: array
          items:
            $ref: '#/components/schemas/GetBatchPeriodResponse'
          nullable: true
      additionalProperties: false
    GetSettlementProfileWithBatchPeriodResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/GetSettlementProfileWithBatchPeriodResponse'
      additionalProperties: false
    GetSettlementTunnelResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        slug:
          type: string
          nullable: true
        active:
          type: boolean
        createdAt:
          type: string
          format: date-time
        modifiedAt:
          type: string
          format: date-time
        channels:
          type: array
          items:
            $ref: '#/components/schemas/GetChannelSettlementResponse'
          nullable: true
      additionalProperties: false
    GetSettlementTunnelResponseListPagedResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetSettlementTunnelResponse'
          nullable: true
        pageNumber:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
        firstPage:
          type: string
          format: uri
          nullable: true
        lastPage:
          type: string
          format: uri
          nullable: true
        totalPages:
          type: integer
          format: int32
        totalRecords:
          type: integer
          format: int32
        nextPage:
          type: string
          format: uri
          nullable: true
        previousPage:
          type: string
          format: uri
          nullable: true
      additionalProperties: false
    GetSettlementTunnelResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/GetSettlementTunnelResponse'
      additionalProperties: false
    MappingModel:
      type: object
      properties:
        settlementAccountId:
          type: string
          nullable: true
        settlementAccountName:
          type: string
          nullable: true
        settlementAccountNumber:
          type: string
          nullable: true
        settlementBankName:
          type: string
          nullable: true
        settlementBankCode:
          type: string
          nullable: true
        sourceBanks:
          type: array
          items:
            $ref: '#/components/schemas/SourceBankDetails'
          nullable: true
      additionalProperties: false
    MarkLogAsSettledRequest:
      type: object
      properties:
        onlyUpdateTlog:
          type: boolean
        settlementLogId:
          type: string
          format: uuid
        paymentReference:
          type: string
          nullable: true
        sessionId:
          type: string
          nullable: true
        datePosted:
          type: string
          nullable: true
        retryCount:
          type: integer
          format: int32
      additionalProperties: false
    NameInquiry:
      type: object
      properties:
        bankCode:
          type: string
          nullable: true
        accountNumber:
          type: string
          nullable: true
      additionalProperties: false
    NameInquiryRequest:
      type: object
      properties:
        accountNumber:
          type: string
          nullable: true
        bankCode:
          type: string
          nullable: true
      additionalProperties: false
    NameInquiryResponse:
      type: object
      properties:
        accountNumber:
          type: string
          nullable: true
        accountName:
          type: string
          nullable: true
        accountInstitutionCode:
          type: string
          nullable: true
        sessionId:
          type: string
          nullable: true
        transactionId:
          type: string
          nullable: true
        bankVerificationNumber:
          type: string
          nullable: true
        kycLevel:
          type: string
          nullable: true
      additionalProperties: false
    NameInquiryResponseNIP_ResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        code:
          type: string
          nullable: true
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/NameInquiryResponse'
        serviceProvider:
          type: string
          nullable: true
        timeStamp:
          type: string
          format: date-time
      additionalProperties: false
    NipBanks:
      type: object
      properties:
        bankName:
          type: string
          nullable: true
        bankCode:
          type: string
          nullable: true
      additionalProperties: false
    NipBanksListResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/NipBanks'
          nullable: true
      additionalProperties: false
    NipConnectors:
      enum:
        - nibss
        - wema
      type: string
    RemoveBatchPeriodResponse:
      type: object
      properties:
        batchPeriodId:
          type: string
          format: uuid
      additionalProperties: false
    RemoveBatchPeriodResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/RemoveBatchPeriodResponse'
      additionalProperties: false
    ResendFundsRequest:
      type: object
      properties:
        fundTransferTraceId:
          type: string
          format: uuid
      additionalProperties: false
    SettlementMode:
      enum:
        - Mapping
        - SplitCode
        - Default
        - SubAccount
        - Batch
      type: string
    SettlementTypes:
      enum:
        - INSTANT
        - BATCH
      type: string
    SettleMerchantRequest:
      type: object
      properties:
        merchantID:
          type: string
          format: uuid
        merchantName:
          type: string
          nullable: true
        amountPaid:
          type: number
          format: double
        amountToSettle:
          type: number
          format: double
        transactionId:
          type: string
          format: uuid
        transactionReference:
          type: string
          nullable: true
        transactionDescription:
          type: string
          nullable: true
        transactionCharge:
          type: number
          format: double
        expectedTransactionCharge:
          type: number
          format: double
        transactionChargeValue:
          type: number
          format: double
        transactionChargeCap:
          type: number
          format: double
        isFlatCharge:
          type: boolean
        transactionDate:
          type: string
          format: date-time
        serviceProviderName:
          type: string
          nullable: true
        serviceProviderCode:
          type: string
          nullable: true
        channel:
          type: string
          nullable: true
        reference:
          type: string
          nullable: true
        sourceBankCode:
          type: string
          nullable: true
        sourceBankName:
          type: string
          nullable: true
        sourceAccountName:
          type: string
          nullable: true
        sourceAccountNumber:
          type: string
          nullable: true
        settlementMode:
          $ref: '#/components/schemas/SettlementMode'
        settlementProfileSplitCode:
          type: string
          nullable: true
        splitReference:
          type: string
          nullable: true
        batchReference:
          type: string
          nullable: true
        isBatch:
          type: boolean
        autoSettlement:
          type: boolean
      additionalProperties: false
    SettleMerchantResponse:
      type: object
      additionalProperties: false
    SettleMerchantResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/SettleMerchantResponse'
      additionalProperties: false
    SourceBankDetails:
      type: object
      properties:
        bankName:
          type: string
          nullable: true
        bankCode:
          type: string
          nullable: true
      additionalProperties: false
    SplitType:
      enum:
        - flat
        - percentage
      type: string
    StringResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          type: string
          nullable: true
      additionalProperties: false
    SwitchBatchPeriodToDefaultRequest:
      type: object
      properties:
        batchPeriodId:
          type: string
          format: uuid
      additionalProperties: false
    TransactionStatusRequeryRequest:
      type: object
      properties:
        transactionId:
          type: string
          nullable: true
      additionalProperties: false
    TransactionStatusRequeryResponse:
      type: object
      properties:
        responseCode:
          type: string
          nullable: true
        responseCodeDefinition:
          type: string
          nullable: true
        sessionId:
          type: string
          nullable: true
        transactionId:
          type: string
          nullable: true
        sourceInstitutionCode:
          type: string
          nullable: true
        datePosted:
          type: string
          nullable: true
        transactionType:
          type: string
          nullable: true
      additionalProperties: false
    TransactionStatusRequeryResponseNIP_ResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        code:
          type: string
          nullable: true
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/TransactionStatusRequeryResponse'
        serviceProvider:
          type: string
          nullable: true
        timeStamp:
          type: string
          format: date-time
      additionalProperties: false
    UpdateAutoSettlementChargeRequest:
      type: object
      properties:
        settlementProfileId:
          type: string
          format: uuid
        charge:
          type: number
          format: double
      additionalProperties: false
    UpdateBatchPeriodRequest:
      type: object
      properties:
        fromHour:
          type: integer
          format: int32
        fromMinute:
          type: integer
          format: int32
        toHour:
          type: integer
          format: int32
        toMinute:
          type: integer
          format: int32
        periodHour:
          type: integer
          format: int32
        modifiedBy:
          type: string
          nullable: true
        modifiedByEmail:
          type: string
          nullable: true
      additionalProperties: false
    UpdateChannelSettlementTunnelRequest:
      type: object
      properties:
        channel:
          type: string
          nullable: true
        partnerCode:
          type: string
          nullable: true
        isActive:
          type: boolean
      additionalProperties: false
    UpdateGemspayBankAccountActiveStateRequest:
      type: object
      properties:
        active:
          type: boolean
        initiatorName:
          type: string
          nullable: true
        initiatorEmail:
          type: string
          nullable: true
      additionalProperties: false
    UpdateMerchantBatchPeriodRequest:
      type: object
      properties:
        batchPeriodId:
          type: string
          format: uuid
      additionalProperties: false
    UpdateSettlementAccountRequest:
      type: object
      properties:
        bankCode:
          type: string
          nullable: true
        bankAccountName:
          type: string
          nullable: true
        bankAccountNo:
          type: string
          nullable: true
      additionalProperties: false
    UpdateSettlementMappingRequest:
      type: object
      properties:
        name:
          type: string
          nullable: true
        mapping:
          type: array
          items:
            $ref: '#/components/schemas/MappingModel'
          nullable: true
      additionalProperties: false
    UpdateSettlementSplitAccountRequest:
      type: object
      properties:
        splitValue:
          type: number
          format: double
      additionalProperties: false
    UpdateSettlementSplitCodeRequest:
      type: object
      properties:
        name:
          type: string
          nullable: true
        splitType:
          $ref: '#/components/schemas/SplitType'
        splitAccounts:
          type: array
          items:
            $ref: '#/components/schemas/AddSettlementSplitAccountRequest'
          nullable: true
      additionalProperties: false
    UpdateSettlementTunnelRequest:
      type: object
      properties:
        name:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        slug:
          type: string
          nullable: true
      additionalProperties: false
    ValidateSettlementModeCodeResponse:
      type: object
      properties:
        code:
          type: string
          nullable: true
        isValid:
          type: boolean
        settlementMode:
          type: string
          nullable: true
      additionalProperties: false
    ValidateSettlementModeCodeResponseResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          $ref: '#/components/schemas/ValidateSettlementModeCodeResponse'
      additionalProperties: false
    WemaGetAccountStatementRequest:
      type: object
      properties:
        accountNumber:
          type: string
          nullable: true
        startDate:
          type: string
          format: date-time
        endDate:
          type: string
          format: date-time
      additionalProperties: false
    WemaGetAccountStatementResponse:
      type: object
      properties:
        accountname:
          type: string
          nullable: true
        rcrE_TIME:
          type: string
          format: date-time
        lchG_TIME:
          type: string
          format: date-time
        vfD_DATE:
          type: string
          format: date-time
        pstD_DATE:
          type: string
          format: date-time
        entrY_DATE:
          type: string
          format: date-time
        traN_DATE:
          type: string
          format: date-time
        valuE_DATE:
          type: string
          format: date-time
        tranid:
          type: string
          nullable: true
        particulars:
          type: string
          nullable: true
        tranremarks:
          type: string
          nullable: true
        dr:
          nullable: true
        cr:
          nullable: true
        balance:
          type: number
          format: double
        parT_TRAN_SRL_NUM:
          type: integer
          format: int32
        instrmnT_NUM:
          type: string
          nullable: true
        gL_DATE:
          type: string
          format: date-time
      additionalProperties: false
    WemaGetAccountStatementResponseListResponseHandler:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/WemaGetAccountStatementResponse'
          nullable: true
      additionalProperties: false
