---
id: add-settlementsplitconfig-add
title: "Add settlement split config add"
description: "Add settlement split config add"
sidebar_label: "Add settlement split config add"
hide_title: true
hide_table_of_contents: true
api: eJztVktv2kAQ/itoTn0sIU/S+kajKM2hahSSXhCHwTuGTe1dZ3dNgiz/92rWxDbQKqhnuBh5Z755fJ93pgSTk0WvjL6VEAFKOSbvU8pIe5enysdGJ2o+khIEeJw7iCbQmozZ5MpIgqmAHC1m5MmyUQkaM4IIbq5/jEePD99BgNIQwYJQkgUBlp4LZUlC5G1BAly8oAwhKsGvcvZ03io9h6qa1sbk/DcjV2wRG+1Je/6LeZ6qOJQweHJG87sdKDN7otiDgNxywV6R49M6xe2AAnSRpjhLqU6tEhBa8RCsSiBdZNyGJMUASTYm7XEemrAN5ZVnHBg3CJWAjGy8QO2557vRE2Mz9BBBUSgJb9FHcWwK7V3HA63FFffVU+berzbA/MK06Nasi2wW6GiiSlNw6Ry34XkdfK98K8EyUkwIpnedDBJMHbUdGXW1Nu6UeF9zzRlsEfH/0KzRBrcS4OnVH+RykMueculeMp8+H3Rz0M0+umEgSy432tWtOT0+5ockF1uVMz5TVsQxORcq2jwZSdlrm9MLje3VA7mHYSJn5BeGB3duXGAC/QIiGGCuBq1nP3j2a89B7enILt8mdWFTHsze5y4adP0kLY/mlLkcV86kBad1FJsMeCLzR3DfTuXrV8zylFq5t8R1VP0m5q4y4SzBLxfJ8Lx/cXly2T+/GJ72Z2dJ3D+Nvw7PkuEQExzCjjwnm0o7/oeO9kOvphWvJ4kJwlvTe1NX3ms57o3ubkEAN65maHnC8uHmZ6g7tb/P3AbTjdjDWMpTVJpxAy/lmtQJYK6gW+UGrRA0y1fDgqUQTaAsZ+jo0aZVxa+fC7IriCZTAUu0qpb7hOteb2TcUakcH8hG6ltpNnsXfLhfr28feyD+nv5vWm3uf8uaKoBKlOvTqxqwH+TRWuwsday4Rux3P8cPIGC2XgYz3j8jsPjCWyW+cAQBJiQdPrvwroQU9bzg+zOCGpN/fwAW5dT7
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Add settlement split config add"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/settlement-split-config/add"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Add settlement split config add

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"splitType":{"enum":["flat","percentage"],"type":"string","title":"SplitType"},"merchantId":{"type":"string","format":"uuid"},"splitAccounts":{"type":"array","items":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"},"nullable":true}},"additionalProperties":false,"title":"AddSettlementSplitCodeRequest"}},"text/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"splitType":{"enum":["flat","percentage"],"type":"string","title":"SplitType"},"merchantId":{"type":"string","format":"uuid"},"splitAccounts":{"type":"array","items":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"},"nullable":true}},"additionalProperties":false,"title":"AddSettlementSplitCodeRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"splitType":{"enum":["flat","percentage"],"type":"string","title":"SplitType"},"merchantId":{"type":"string","format":"uuid"},"splitAccounts":{"type":"array","items":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"},"nullable":true}},"additionalProperties":false,"title":"AddSettlementSplitCodeRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      