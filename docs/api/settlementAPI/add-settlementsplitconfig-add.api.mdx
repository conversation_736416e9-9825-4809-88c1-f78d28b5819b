---
id: add-settlementsplitconfig-add
title: "Add settlement split config add"
description: "Add settlement split config add"
sidebar_label: "Add settlement split config add"
hide_title: true
hide_table_of_contents: true
api: eJztlktT2zAQx79KZk99KIRnaH1LGYZy6JQh0Esmh421TkRtSUgykPH4u3dWDrZD2oHpmVySsVb7+v/i3QqMJYdBGX0pIQGUckoh5FSQDt7mKqRGZ2o5kRIEBFx6SGbQmUzZ5MxIgrkAiw4LCuTYqAKNBUECF+c/ppPbm+8gQGlIYEUoyYEAR/elciQhCa4kAT5dUYGQVBDWlm/64JReQl3PG2Py4ZuRa7ZIjQ6kA/9Ea3OVxhJGd95ofrbjyizuKA0gwDouOCjyfNqk+DKgAF3mOS5yalKrBcRW3ESrCkiXBbchyzG6JJeSDriMTXjpKqjAfmDaeqgFFOTSFerAPd+NnhlXYIAEylJJeI4+SVNT6uB7N9A5XHNfAxX+9Wqjm1+Yl/2adVksohxtVGlKLp3jtjpvgr8p31owRooFwfyql0GGuaeuI5M+a9NeideN1pzBCyH+3zUz2vqtBQR6Cu+4vOPyRlz6L5lPn9+5eefmLdywI0feGu2b1hzu7/OXJJ86Zdk/S1amKXkfK9o+mUg56JoziI0dNAN5gHEiFxRWhge3NT4qgWEFCYzQqlF3cxhvDpubo+amJ/fwPKlLl/NgDsH6ZDRaUuFLu3QoaY9/p7kp5R5aCzyImf3rbhifP2Fhc+oo7/TqwfzMcB9IOMrwy0k2Ph6enB6cDo9PxofDxVGWDg/Tr+OjbDzGDMewQ+VsG7D9f+DzNu/1vOatJDORt42qF1R4i+tBJ+1gcnUJArhfjTAPB0wN97xA3av9dcG2BG4Zj9PI5qg0+41yVBstZ4BWQb/KLTUhospvhBUTkMygqhbo6dbldc2P70tya0hmcwEP6FRD+Yzr3ixi3FGpPB/IlvAXabbrFny43mxtHwcg/p7+b1pvr30PjVQAtag2p2eNw2HEo7PY2eWYuJbxq5/TGxCw2OyABa+dCTh85GUSHzmCABOTjv+2+KyCHPWy5NdmAo1P/vwBXfHRMA==
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Add settlement split config add"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/settlement-split-config/add"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Add settlement split config add

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"splitType":{"enum":["flat","percentage"],"type":"string","title":"SplitType"},"merchantId":{"type":"string","format":"uuid"},"splitAccounts":{"type":"array","items":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"},"nullable":true}},"additionalProperties":false,"title":"AddSettlementSplitCodeRequest"}},"text/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"splitType":{"enum":["flat","percentage"],"type":"string","title":"SplitType"},"merchantId":{"type":"string","format":"uuid"},"splitAccounts":{"type":"array","items":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"},"nullable":true}},"additionalProperties":false,"title":"AddSettlementSplitCodeRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"splitType":{"enum":["flat","percentage"],"type":"string","title":"SplitType"},"merchantId":{"type":"string","format":"uuid"},"splitAccounts":{"type":"array","items":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"},"nullable":true}},"additionalProperties":false,"title":"AddSettlementSplitCodeRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      