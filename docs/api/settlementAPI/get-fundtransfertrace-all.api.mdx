---
id: get-fundtransfertrace-all
title: "Get FundTransferTrace all"
description: "Get FundTransferTrace all"
sidebar_label: "Get FundTransferTrace all"
hide_title: true
hide_table_of_contents: true
api: eJytU7Fu2zAQ/RXjTQ1AREm6afOQuhlaBLUzGRrO0lkmSlEMeTLqCvr34iQ7rR0jUydJvHeP99499WgDRxLb+qcKOWqWL52vJJJPW44SqeS5czAQqhPyNbS8OpZXWkZhEChSw8JRIT08NYwcz1Tz967ZcISB9cjx2nE8wCCVO24IeQ85BIVaL1yPuG0bG5Lp6PMDhsGc8S3tb/4/bIvHb8v5y+rriW3HVI09kV87G7lCLrHjK/RJovU1hqFQcAqtT5y0/nB3p4+KUxltUFORY9mVJaekd19UFiyzd3bOaLS7Ydm1x41ADZYdcmQUbPauJZtaEsf9aQNddCpJJKQ8y2puUhfqSBXf6nvp2q66pRCgEqzftqM6K240hpsU6DBbsojjhr3M5s9PMFD2afL9PQaD0CZpyGvvydMPFJ1pf/NS+JdkwZH1yjiO3R/VrkHBwlxJnIFSFga7NokC+35DiV+iGwY9noKhPlQ20cbpLrfkEhv85MNlMvfkOh0FGo4PGo7R+wsv9CNaxSNfF4M5ZejqzRf6y9YLe43mpx/HwN3M9D+75ss0xD+JPRviLSuLxxWG4Q8Z7kop
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get FundTransferTrace all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/FundTransferTrace/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get FundTransferTrace all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      