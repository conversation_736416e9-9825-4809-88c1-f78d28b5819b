---
id: get-fundtransfertrace-all
title: "Get FundTransferTrace all"
description: "Get FundTransferTrace all"
sidebar_label: "Get FundTransferTrace all"
hide_title: true
hide_table_of_contents: true
api: eJytUz1v20AM/SsCpwa4Rmm6afOQuBlaBLUzGRpoiZYOua/cUUZdQf+9oCS7tWNk6iTc8T2S791TDz5QRNbePdVQQEP82LmaI7q0o8gRK1oYAwoYmwTFBqS8nstrKUOpIGBES0xRID04tAQFPGNDPzq7pQgKtIMC3jqKB1CQqpYsQtEDH4JAtWNqRtzOR4s8XX29h2FQZ/1W+jf9n27Lh++rxcv627FbS1iPnEhvnY5UQ8GxoyvtE0ftGhiGUsApeJcoSf3+7k4+NaUq6iCmQgGrrqooJZl9UVkSZ+/szHC02xK3fn4REIO5hQJyDDp/R8knSqK4P75AF41IYg6pyPNEzIYsOf5c0/62IZsCHpI3nWxyW3kLokW7nR9lajajQxMuW53o2eL5CRTImEnC/gsMCoJPbNEJ92juB9LOTDiZyvSL82BQO+k47t/PsjeAQYO6Ej0F0rJU0PrEAuz7LSZ6iWYY5HpKiBhS64RbI4+6Q5NIwSsdLiO6R9PJKiAp+YAwZ/AvvJRD1IKHYlMO6himq5Mv9FfeMTnJ6Kefc/JuMvnhrvkyLfFPdM+WOIVm+bCGYfgDP9ZN9A==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get FundTransferTrace all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/FundTransferTrace/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get FundTransferTrace all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      