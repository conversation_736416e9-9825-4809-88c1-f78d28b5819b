---
id: update-tunnel-updatechannel
title: "Update tunnel settlementTunnelId update channel channelSettlementId"
description: "Update tunnel settlementTunnelId update channel channelSettlementId"
sidebar_label: "Update tunnel settlementTunnelId update channel channelSettlementId"
hide_title: true
hide_table_of_contents: true
api: eJztV01v2zgQ/SvGnLZbJu72qJubLdocFgjycQp8GItjm12KZMmRW0PQf1+QlC05FgJ7kfaS+CKJHr35eI/iTAPWkUdW1lxLKKB2Epnua2NIP6T7co3xAQQwrgIUj3BHzJoqMpztYC7AoceKmHy0aMBgRVBAeGJ5LUGAMlCAQ16DAE/fa+VJQsG+JgGhXFOFUDTAW5cQ2CuzAgFL6yvkGGGtJLSt2DvpAuyj+iVevnz+5272cP91B70mlOTPAG/beTamwJ+s3EaL0homw/EWndOqTERMvwVr4toRlF18o5JBgPORNlYUEkxH0UhCptYaF5pydG0iig35KyvpJHsVZiWrzdB4Ya0mNLE8gFKqGDPqm0FIS9SBBLDiiARZSFdPecqauM0lSWhMP/n1Zj+UwJ/vX2kZIp6n4KwJOa+PHz7Ei6RQeuWiGyjgri5LCgHEcAsl9TiN6qy6hQ5qJDUBFYWAq9OKJJFPcKfkCd8d8YvZ/JuWWGsez/k5sgWUnpBJzvjZNCLVF6wqSkW0Ui3VOe+cqqkvxEeCuu3E8zIou+tXNFKTP9qn5+7SN7W9qe08tf2vM/FNZm8yO0dm6dg9PGLzeT3hdD5Pjjv5SR4VJh2pk/E2vCJeW5lb8TL24qklL2CKTk0z9rQ5Bm+nGf2iQ502I/AtCAjkN7uho/Y6NubMLhTTaQ96IWlzuaIqONwGq+uY4GVpK4gdedxZt31X/vknVk7TQUPV03ogyn65F9dOjGZpkyz2ZCTnkz76yezmGgTE2HO5N39FVTkbuMK01bu542VoOKB2L9dBw9SKXL6mI+gR0Kk489Xd8FeMznKHLEWzMf9zAWsbOKI2zQIDPXjdtnH5e01+C8XjXMAGvco7+rEBqUK8l3tZP0lg3/XBH7fd/PVuEsMdS6xbRLONFUddxycQ8C9tx2fUOPb9xgjGStbOW7GbMV+8INntYKLdhxQzz/9eZcCL+wjQWxz1Pv0bs7IkxwPbodP54FNwM7u/il4X3RRc5e3k8Uccp/FHro1NuaXjJa01oNGs6nRwQXYdf/8Bv7zk5w==
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update tunnel settlementTunnelId update channel channelSettlementId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/tunnel/{settlementTunnelId}/update-channel/{channelSettlementId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update tunnel settlementTunnelId update channel channelSettlementId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementTunnelId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"channelSettlementId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isActive":{"type":"boolean"}},"additionalProperties":false,"title":"UpdateChannelSettlementTunnelRequest"}},"text/json":{"schema":{"type":"object","properties":{"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isActive":{"type":"boolean"}},"additionalProperties":false,"title":"UpdateChannelSettlementTunnelRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isActive":{"type":"boolean"}},"additionalProperties":false,"title":"UpdateChannelSettlementTunnelRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"}},"additionalProperties":false,"title":"GetChannelSettlementResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"}},"additionalProperties":false,"title":"GetChannelSettlementResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"}},"additionalProperties":false,"title":"GetChannelSettlementResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      