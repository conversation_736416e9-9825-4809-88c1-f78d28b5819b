---
id: delete-tunnel
title: "Delete tunnel settlementTunnelId"
description: "Delete tunnel settlementTunnelId"
sidebar_label: "Delete tunnel settlementTunnelId"
hide_title: true
hide_table_of_contents: true
api: eJztVU1v2zAM/SvBO22AVnc7+lagQRtgA4p+nAIfFItJ1MmSKtHBAkP/fZDtfLTNoQXaW0+yRfKRfCIeOzhPQbJ2dqZQQpEhpvvWWjIQYLmKKOe4I2ZDDVkeTZWAl0E2xBSyRwcrG0KJ+MJzpiCgLUp4yWsIBHpqdSCFkkNLArFeUyNRduCt7xE4aLuCwNKFRjJKtK1WSEnsk1xN/9xdPNxf76DXJBWFd4CnVGXn6J2NFLP91/l5PhTFOmif+UCJu7auKUYI1M4yWe6R6B8X3sicuXudwS0eqWYI+JCpZT3gxxHq4LhwzpC0SAINxShXdIoE2xojF4aGjpKAkvyGdFq9iVFIpXRuVpqbo/ilNJEEWHNOjMt+KF7OwO1I30fh7M5raZWhMMB6b3Tdj2fxGN0X45/OeD/dX1R/PtUpIz+XmwFiwn3k5KSSNsRrdxDqTE2W1RKF9LoYIovudWiCQKSw2al1G0zWTWYfy6I4+P9QtDlbURO93EZn2lzZWe0aZMHUdul65seWrwa/yaHpycXNDAI5zdDS5mceAe8iN7IfqVHB39DqM272z32kvkkMbXQjB3NIr/PS2m2v8gRuJbB2kbN31y1kpIdgUsrXTy2FLcp5JbCRQQ9zOe+gdMzfav/iLwrbrwZ8ux3Xz/dJLuNUweOltNtMkzRt/oPAX9qeXp6pSmK33z68miHr0TbdV5SX7WC9qGvyfGQ7hqiOJvJy+nt6P0VK/wF68u72
sidebar_class_name: "delete api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete tunnel settlementTunnelId"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/tunnel/{settlementTunnelId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete tunnel settlementTunnelId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementTunnelId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponse"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponse"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponse"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      