---
id: delete-tunnel
title: "Delete tunnel settlementTunnelId"
description: "Delete tunnel settlementTunnelId"
sidebar_label: "Delete tunnel settlementTunnelId"
hide_title: true
hide_table_of_contents: true
api: eJztVU1v2zAM/SvBO22A0HQ7+lagQRtgA4p+nIIcGItN1MmSKtHFAkP/fZDtpGmbQwe0t54si+Qj+UQ8dvCBI4nxbq5RQbNl4dvWObZQEFonVAvcsIjlhp2MpqVCoEgNC8fi0cFRw6iQXnnONRSMQ4VAsoFC5MfWRNaoJLaskOoNN4Sqg2xDjyDRuDUU7n1sSFChbY1Gzmqf5GL2++bs7vZyB71h0hz/AzznZXFOwbvEqdh/np6Wj+ZURxMKH6hw09Y1pwSF2jthJz0S/5VpsFQyd28z+NUD1wKFEAu1Ygb8NEI9O668t0wOWaHhlGjNx0hwrbW0sjx0lBU0yTvSGf0uRkFam9Is2auD+HuyiRXESEmM834oXs/A9UjfR+HsvpfktOU4wIZgTd2P5/Qh+S/GP53xfrq/qP58qnNBfik3A8RE+sjJUSVtWDb+WagLNUVWK0wpmOkQOe3ehmYoJI5PO7Vuoy26KRJSNZ2uuUltWEfSfFLOtfWtPqEQUHTSuHvfEz52esFNCrSdPPc6ObuaQ6GgD508/SgvH3yShvpJGoX7HR2+oGT/ygeim9VQfTe2vgAFU3bVbmlVR3CXChufpHh33YoS30Wbc7l+bDluUS2WCk8UzTCOiw7apHLW+4d+Vdh+I+Db9bh1vk9KGccKHi/JbQtNZNvyB4U/vD2+M/Myq91a+/BqhqwHS3RfUdmxg/WsrjnIge0QYnkwiOezX7PbGXL+B30z6ys=
sidebar_class_name: "delete api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete tunnel settlementTunnelId"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/tunnel/{settlementTunnelId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete tunnel settlementTunnelId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementTunnelId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponse"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponse"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponse"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      