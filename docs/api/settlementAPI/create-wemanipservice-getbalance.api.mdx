---
id: create-wemanipservice-getbalance
title: "Create WemaNIPService GetBalance"
description: "Create WemaNIPService GetBalance"
sidebar_label: "Create WemaNIPService GetBalance"
hide_title: true
hide_table_of_contents: true
api: eJztWMFuGjEQ/RU0p1ZyStre9kabiEZKaBSIqipCaPBOwK3XduxZ1Ajtv1ezC2RJaAtSpVw4gex5zzNj+2mfl+ADRWTj3UUOGehIyPSNCnQmJIoLo6lPPEWLThMoYJwlyO5AQgYX18MmBMYKAkYsiCnK/BIcFgQZoNa+dDwoiylFUGAcZPBQUnwEBUnPqUDIlsCPQaITR+NmUFVqQ9A/vxr2bkdf1tg5YV4zRXooTaQcMo4l/Y1sLMEpeJcoyfyH01P5ySnpaILUDhkMS60pJVCgvWNyXDPRL+4Gi7Ly8uUKfvqDNIOCEKWNbBr+tKJ6Cpx6bwkdVAoKSgln9DJPBa60FqeWmooqBTlyezmMEaVthqlI/05j1fqmj3usFnU8n4wurs53Rd/7WCBDJjnRCZuCpBir5/0DIYv7s8lZb3QAIiQ+FEKO4/cDMRxxcCBkgbY8P3wZZ/K9NiRgZKNLizHtFS/UkQqMP/eLz6OEvRjWu4fXGvDE7NaX+qlSXwqkyX00Gd30BpPhzeVkcHvVwhnHNNsGGscfPwjOuMSxcKNnkD8XMbs8aAMqBZjnRi492uvWfblHm0gBGxbyWt/6xL3mDg0ZmQpyfLPSEUn1WSr/ifnSpM3/L+hyS7HJOgRrdK3U3R/JHwXpKEhHQToK0usJUv1tdFSioxIdleioRK+oRJXQb3u5z7WL7Wx71E6f+NPGxxbEcy+ON/hU6wPyHDLoYjDdbVx3CyeeeG1xy2jFkDKHlHW7iZhtne9JTot3MypSwMfkbSlZvdO+AHGixt37ekdWNfebuM5wA+/0ri9AgSzTlLN4Xx8bn7jAWm1X1niPMrf6sjkGLVtbqaaM5aoDd4DBgHru7xW0aMcK5tK17A6Wyykmuo22qmS4MfbSm9wkOQD5Zo9/0uOO5wCRCcmobs0Co2lOzd24Umunv5PtWV0byw5vblbPAm878lqxq94mk9a7wlMSarma7WlNgVtzbYpx6/hcfx2OoKp+A5rz8aM=
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create WemaNIPService GetBalance"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/WemaNIPService/GetBalance"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create WemaNIPService GetBalance

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"accountNumber","in":"query","schema":{"type":"string"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"accountname":{"type":"string","nullable":true},"rcrE_TIME":{"type":"string","format":"date-time"},"lchG_TIME":{"type":"string","format":"date-time"},"vfD_DATE":{"type":"string","format":"date-time"},"pstD_DATE":{"type":"string","format":"date-time"},"entrY_DATE":{"type":"string","format":"date-time"},"traN_DATE":{"type":"string","format":"date-time"},"valuE_DATE":{"type":"string","format":"date-time"},"tranid":{"type":"string","nullable":true},"particulars":{"type":"string","nullable":true},"tranremarks":{"type":"string","nullable":true},"dr":{"nullable":true},"cr":{"nullable":true},"balance":{"type":"number","format":"double"},"parT_TRAN_SRL_NUM":{"type":"integer","format":"int32"},"instrmnT_NUM":{"type":"string","nullable":true},"gL_DATE":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementResponse"},"nullable":true}},"additionalProperties":false,"title":"WemaGetAccountStatementResponseListResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"accountname":{"type":"string","nullable":true},"rcrE_TIME":{"type":"string","format":"date-time"},"lchG_TIME":{"type":"string","format":"date-time"},"vfD_DATE":{"type":"string","format":"date-time"},"pstD_DATE":{"type":"string","format":"date-time"},"entrY_DATE":{"type":"string","format":"date-time"},"traN_DATE":{"type":"string","format":"date-time"},"valuE_DATE":{"type":"string","format":"date-time"},"tranid":{"type":"string","nullable":true},"particulars":{"type":"string","nullable":true},"tranremarks":{"type":"string","nullable":true},"dr":{"nullable":true},"cr":{"nullable":true},"balance":{"type":"number","format":"double"},"parT_TRAN_SRL_NUM":{"type":"integer","format":"int32"},"instrmnT_NUM":{"type":"string","nullable":true},"gL_DATE":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementResponse"},"nullable":true}},"additionalProperties":false,"title":"WemaGetAccountStatementResponseListResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"accountname":{"type":"string","nullable":true},"rcrE_TIME":{"type":"string","format":"date-time"},"lchG_TIME":{"type":"string","format":"date-time"},"vfD_DATE":{"type":"string","format":"date-time"},"pstD_DATE":{"type":"string","format":"date-time"},"entrY_DATE":{"type":"string","format":"date-time"},"traN_DATE":{"type":"string","format":"date-time"},"valuE_DATE":{"type":"string","format":"date-time"},"tranid":{"type":"string","nullable":true},"particulars":{"type":"string","nullable":true},"tranremarks":{"type":"string","nullable":true},"dr":{"nullable":true},"cr":{"nullable":true},"balance":{"type":"number","format":"double"},"parT_TRAN_SRL_NUM":{"type":"integer","format":"int32"},"instrmnT_NUM":{"type":"string","nullable":true},"gL_DATE":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementResponse"},"nullable":true}},"additionalProperties":false,"title":"WemaGetAccountStatementResponseListResponseHandler"}}}}}}
>
  
</StatusCodes>


      