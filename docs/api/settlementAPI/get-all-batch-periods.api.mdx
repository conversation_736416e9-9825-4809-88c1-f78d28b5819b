---
id: get-all-batch-periods
title: "Get all batch periods"
description: "Get all batch periods"
sidebar_label: "Get all batch periods"
hide_title: true
hide_table_of_contents: true
api: eJztV01vGjEQ/StoTq3khDS97Y1UEYnUDxSSU8RhWA/g1Gs79iwqWu1/r2b52gTa0ki57QmExzPvzYyfeBX4QBHZeHerIYM58cDaK+R8MaJovE6ggHGeIHuE1s8wURAwYkFMUQ4rcFgQZDC8/jYePNzfgALjIIMFoaYICiI9lyaShoxjSQpSvqACIauAV0FuJo7GzaGuJxKcgneJkpxfXlzIh6aURxMEK2QwLvOcksDLvWNy3GSiX9wPFqVydVjBT58oZ1AQotBms86fNqn2gVPvLaGDWkFBKeGcDnEqcKW1OLW0ZlQr0MjtchgjrqQPTEX6N4zpvr0yi8N6Mx8LZMigLI0WbK0bX7w+ESPNsLR8nOws+uLeFKdlYn9yaGgw3vgytsKNY5o3q7EjZhx/vhQgeSRk0gP+ax80Mp2xgJBBeW1m5m13rlYn0diHXxdo7El3EjFbKsjxKPqZsZTeviEFxXyBjq/KZByl9B1P7P/24g839Ri1cfPjG3ZwEUv24x2DY0sjQVobeZVoRy24M7SJFLBh2+gC8fh1K+42r1xG8ar0f6RtydK7JfxqEm+/36DTluKaegjW5I1+9p+S72Snk51OdjrZeW/Zaf7ndHrT6U2nN53evLve1HXzoNrua0jcQ2t7zdPshZ1VLIgXfmMkZa7IC8igj8H0W+X6w8ZmgqxNXG4NZBmt+EXmkLJ+f79QZ5qW53MqUsBV8rYUCOe5L0CMonEz38xrx6uJ6+2H0BuMbkGBlFljX36SvgWfuMBGQLfO9Q+cXjDfbUbLatZqjb3a8H0EDAbUC7+sYMN5omDhE0tUVU0x0UO0dS0/P5cUV5A9ThQsMZr1UB8ntdp6aGmSNkkO9G6gr9DtzDB8uNsY7o898fDHUP+k1UvHvkRbShjUqtqcDvKcArfO2ikmrYkPr++hrn8Dzxy6cw==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get all batch periods"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/BatchPeriod/GetAll"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get all batch periods

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponseListResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponseListResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponseListResponseHandler"}}}}}}
>
  
</StatusCodes>


      