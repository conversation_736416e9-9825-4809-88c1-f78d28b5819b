---
id: delete-batchperiod-remove
title: "Delete BatchPeriod Remove batchPeriodId"
description: "Delete BatchPeriod Remove batchPeriodId"
sidebar_label: "Delete BatchPeriod Remove batchPeriodId"
hide_title: true
hide_table_of_contents: true
api: eJztVctu2zAQ/BVjTi3ARmmPuqWIkQRogSCPk+HDWlzbTCmSISmjhsB/LyjJipzkkAJpTznJ1pKzu6PBTAvr2FNU1lxJlJCsOfJ3itXWsVdW3nBtdwyBSJuAcoGudt3VsBRw5KnmyD4XWxiqGSVWT4euJASUQQlHcQsBz4+N8ixRRt+wQKi2XBPKFnHv8uUQvTIbCKytrymiRNMoiZTEiH8x/3l7dn93eYDeMkn2fwGe0jIfDs6awCHXv52e5ofkUHnlMiEocdtUFYcAgcqayCZ2SPw7Fk5T7ty+7GBXD1xFCDifuY2qxw8D1NPBlbWaySAJ1BwCbfg1EkyjNa009xslAUnxDe2Ov8BbyAVJqfLepK8nUGvSgQWiinkG9HqYiOBmIPEdIA7PSzJSs+8RndOq6gRaPAT7Qfm/p7zT9wfX/4HrlEGPHee8M+DZ5PqsB5w999Sa49Y+eXamKBtsiYKcKiYARQ9QtEcICQKB/e7g3I3X2UhjdKEsisAxaq7ZxC+SdycbroOjfbC6yXOeVLZGdlBl1rZjf9j9oj83ux2vz86uryCQ2/QL7r5mRTgbYk2dwgZLf/viR4SNX37iykn027QDIwuQUxBH0SUwJlt53GApsLUh5mttu6LA916nlF8/Nuz3KBdLgR151ct20UKqkH/LUQrPJhyzA59uhnz6PMuR+trkw0sy+0wb6Sb/g8Av3r8I1rRM4pB97z5I33CStOMwOYj76llVsYuT2hRiOdHo+fzH/G6OlP4AtInzuQ==
sidebar_class_name: "delete api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete BatchPeriod Remove batchPeriodId"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/BatchPeriod/Remove/{batchPeriodId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete BatchPeriod Remove batchPeriodId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"batchPeriodId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"RemoveBatchPeriodResponse"}},"additionalProperties":false,"title":"RemoveBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"RemoveBatchPeriodResponse"}},"additionalProperties":false,"title":"RemoveBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"RemoveBatchPeriodResponse"}},"additionalProperties":false,"title":"RemoveBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      