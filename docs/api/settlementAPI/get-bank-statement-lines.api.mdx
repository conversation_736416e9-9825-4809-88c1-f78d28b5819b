---
id: get-bank-statement-lines
title: "Get bank statement transaction lines"
description: "Get bank statement transaction lines"
sidebar_label: "Get bank statement transaction lines"
hide_title: true
hide_table_of_contents: true
api: eJztWEtv2kAQ/itoTq3klD5uvqUPpZHaKgr0VHEYvANssw93d4xKLf/3amxsDKGpE+XoE8I7883sNw/DV4LPKSBr764VpLAmfo/ubsbIZMnxF+0oQgKM6wjpD5DDW8q8y7TRtRssEsgxoCWmIDYlOLQEKcQW5FpBAtpBCjnyBhII9KvQgRSkHApKIGYbsghpCbzLG9eg3RoSWPlgkSGFotAKqirp0G9wTd8Ku6TQgv8qKOzgDJp2TOvaroPTjt+9vYc303/oedCuPn2dXX6ff27RNoSq9hl69apaiHHMvYsU5fzt69fyoShmQec19SnMiiyjKBXKvGNyXCPRb57mBiVyeT+CX/6kjCGBPEjxWTf4cQ91MFx6bwgdVAlYihHXdK5ErjAGl4aaG1UJKOR+OAwBhUjNZOP/09BqQB8kh+aSDv1IjNrEQdlxQBcxE/5uyWK4G+SF1hd7chtT17Zel5nyhXgch5jX1iWQK6zMTxZIabm0oqVmmZ3T0KxZ4sLRGM5PECvpjWYKSZ2v2OH8kh9kVCHTBWtbo+o4o7DVGX3YYDiqdw86C4T8OFzrlV7px/gI6UppuTKam16LrNBEOvB0dbKx5v36NuMjCdyraH7YH0Nmu3GoF8Qw85UOkW/+MTOHdg76XLsZfLove0YjznFgprWDLPWghro4+v3k/PJAW+2L+DT/Z2yLL7ohWbUPPqNThkLTe3ludFa/4aY/ox836bhJx006btJxkz56k9a/RscVOq7QcYWOK3RcoY9foZXEOf7bf0U8WaK7m3SbZ9Kb84nZqzaWeOP3so7sNlFfUphirqfifRGOlJzmWYcYp2VPwammLWiksG2VniIYETeY85hOp5GYTW1/oWj7ak025riL3hQC/yrzFkTV0G7la647dmq7yaxzn1zeXEMCEqa57/ZN3fQ+ssX6PdLKLMN4OCKvK3JPJqmS5irlnqMfgLnU+wxL7dMDT5BAeqx1NXEXCWx8ZEEryyVG+h5MVcnjRlsSApWO0k+qa5k72p2KW1s0heQLoi894LBXrw7mC/kSdNOw54Kd8NJJSPDidi9TvZxAcp6v9oXqdv2YbT59PqpFlbQC2LOn0YTryW1HbDWnl1lGOffO+hCL3phcfZpDVf0FkA9PuA==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get bank statement transaction lines"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/bank-reconciliation/bank-statements/{statementId}/lines"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get bank statement transaction lines

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"statementId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetBankStatementTransactionResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetBankStatementTransactionResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetBankStatementTransactionResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      