---
id: get-bank-statement-lines
title: "Get bank statement transaction lines"
description: "Get bank statement transaction lines"
sidebar_label: "Get bank statement transaction lines"
hide_title: true
hide_table_of_contents: true
api: eJztWMGO2zYQ/RVjTi2gxGl6022bBpsF0mKxdk6BD2NxbDNLkQw5MuIK+vdiREuWvW6qXexRJ69Fzpvhm+GT99XgPAVk7eydghy2xH+gfVwwMpVk+bO2FCEDxm2E/CvI4gMVzhba6DYMVhl4DFgSU5A9NVgsCXKIHcidggy0hRw88g4yCPS90oEU5BwqyiAWOyoR8hr44FNo0HYLGWxcKJEhh6rSCpom69HvcUt/V+WaQgf+vaJwgCto2jJt2309nLb8+/sneAv9D70O2u3HvxY3X5afOrQdoWpjxh69aVayOXpnI0VZf//unXwoikXQvqU+h0VVFBSlQ4WzTJZbJPrBc29QMtdPM7j1NyoYMvBBms864ccj1Gnj2jlDaKHJoKQYcUvXWmQrY3BtKJ2oyUAhD9NhCChEaqYy/n8ZWo2Yg+w0XDKhfxKjNnFUdRzQRiyEvwcqMTyOisLSVUdy01bbjV5fmXKVRJynWLa7ayBblXJ/ikBKy6EVrTXL3blMzZolL5xdw+UFYiOzkW4hqesdO63f8E8ZVcj0hnXZouq4oLDXBX3YYTjr9wC6CIT8PNzSKb3Rz4kR0pXScmQ094MR2aCJdOLp9kKxlsP+pusjBTzpqD/px5i7nQJagRi3faND5Pv/uDOncQ762rgZfHksO0YjwXFkpW2AiHpQY0Ms/XhxfT7QXrsqviz+Fcfis04kq+7BJ7TKUEiz573RRfuGm3+LblLSSUknJZ2UdFLSZytp+2t0ktBJQicJnSR0ktDnS2gjec7/7b8lnq3RPs565ZkN7vnMHF2bknjnjraOaJu4LznM0eu5RL8JZ05OetYjxnk9cHCaeQcaKew7p6cKRswNZh/z+XxLZaz8NqCit/J3YVyl3qL3IGaGthvXUtyTUkaPh9mCmE06w839HWQg6OmY+9/aWXeRS2xfH527Mu74Z5z1vR24I02WTlAfqfkK6KXNV8jpnp7ogQzyc4sr5V1lsHORBa2u1xjpSzBNI4+TpSS8KR1ljFQ/KY90uPS09mgqqRfEVvpJwNG0Om1fyZeg05xeS3bBS+8cwS8PR3fq1xlk1/nq3qP2MMzZ1TPko1k1Wed7vXoZKd3AZTtjK63eFAV5HqwNIVaD23H7cQlN8y/HSkvt
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get bank statement transaction lines"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/bank-reconciliation/bank-statements/{statementId}/lines"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get bank statement transaction lines

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"statementId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetBankStatementTransactionResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetBankStatementTransactionResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetBankStatementTransactionResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      