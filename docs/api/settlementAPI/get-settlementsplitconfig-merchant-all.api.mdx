---
id: get-settlementsplitconfig-merchant-all
title: "Get settlement split config merchant merchantId all"
description: "Get settlement split config merchant merchantId all"
sidebar_label: "Get settlement split config merchant merchantId all"
hide_title: true
hide_table_of_contents: true
api: eJytlMtu2zAQRX9FmFULMFGa7rQzisD1IkVQJ6vAi7E0lohSJEOOjLqC/r0YPSzbNYqiyEqv4RnOvZdqwXkKyNrZVQEZlMRrYjZUk+Xojebc2Z0uHynkFVpeGAMKGMsI2SvMpWsp/eIKgo0CjwFrYgpS1ILFmiCDekSsClCgLWTgkStQEOit0YEKyDg0pCDmFdUIWQt88LIyctC2BAU7F2pkyKBpdAFdp47wJyzpW1NvKUzwt4bCAa7QtGUq+7ojTlv+fP8Hb61/0fvQlg+P68XL89eJVhEW/Zp/Hb3rNlIcvbORony/v7uTS0ExD9qLf5DBuslzilF6X3xZEifx6FbSO5sM1iaTMcnsUIK9zzVx5cZYgBr8yiBFr9MZdtPDbgZYOjHSdqZ16YCLFPZTKJpgRAhmH7M0LamOjS8DFnQr97lxTXGL3oMMru3O9ZpoNr2cVEePh2SOX7J4WoECoQ/z7j9Bp8C7yDVaWTs58V86nGl59IbpJ6feoLbSqx+oHTV6BfS6n/iqSr20Qw9QkJ2dDOm4UVC5yMJp2y1Gegmm6+T1kEMRsNARt0ais0MTScEPOlwehD2aRnYKksW/LBiTPpdv5CFoqb/e7EKR3FkmK+H/8H2M9MdEfhTXlBpfoj2c9pz2cyJGt+nUdFbefRdDt5OTeTb9MfnLh2fout8jEcGK
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settlement split config merchant merchantId all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settlement-split-config/merchant/{merchantId}/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settlement split config merchant merchantId all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      