---
id: get-settlementsplitconfig-merchant-all
title: "Get settlement split config merchant merchantId all"
description: "Get settlement split config merchant merchantId all"
sidebar_label: "Get settlement split config merchant merchantId all"
hide_title: true
hide_table_of_contents: true
api: eJytVMtu2zAQ/BVhTy3ARGl6480oAteHFEGdnAIfaGktEaVIhlwZdQX+e7F6WLZrFEWRkyBxOLM7s6sOnMegSDu7KkFChbRGIoMNWoreaCqc3enqEUNRK0sLY0AAqSqCfIUZumboF1cibAR4FVSDhIFBHVjVIEhoRopVCQK0BQleUQ0CAr61OmAJkkKLAmJRY6NAdkAHzzcjBW0rELBzoVEEEtpWl5CSOJI/qQq/tc0Ww0T+1mI4wBU2bQmrHnek05Y+3//Bt9a/8H3Ylg+P68XL89eJrUZV9nf+tfWUNgyO3tmIkc/v7+74UWIsgvacH0hYt0WBMbL2xckSKYvHtLI+2WyINpuCyeaEMtXn3CDVbhwLEENeEnLldT6T3fRkNwNZPnHk3cyW8oEuYthPQ9EGw0YQ+SjzU7YS97cVNtGrQ3Sm5fpvC9cAO6DtzvXmaDK9rwMum+cwWzytQADLDI3vP0ES4F2kRlm+O0XyX4acmXoMifAn5d4obVmr76wbzXoF5XXf+lW7eo8HDRAgz1aEFTcCaheJebpuqyK+BJMSfx4Gkp0sdVRbwzO0UyaigB94uNyIvTItVwo8lH+5MI78DN/wS9CMvy524UjhLKHlLfjwfZztjxn/Ma45NX5U9nCqOdVzYkbaJDEtzbtXMaidrOhZ98cVWD48Q0q/Afd7xVU=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settlement split config merchant merchantId all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settlement-split-config/merchant/{merchantId}/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settlement split config merchant merchantId all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      