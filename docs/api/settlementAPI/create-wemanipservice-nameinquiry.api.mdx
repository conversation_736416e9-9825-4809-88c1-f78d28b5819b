---
id: create-wemanipservice-nameinquiry
title: "Create WemaNIPService NameInquiry"
description: "Create WemaNIPService NameInquiry"
sidebar_label: "Create WemaNIPService NameInquiry"
hide_title: true
hide_table_of_contents: true
api: eJzdVktP4zAQ/ivVnPZhKLvH3AAh6GHZioL2UPUwcYbW4Be2U6ii/PfVJKFNKAfgBr2ksuf5zeNzBc5TwKScnRSQgQyEif6RQat8pLBWki7RkLIPpQobEJBwGSGbA8tcTqazVgYWAjwGNJQo8H0FFg1BBudnf2bHN9cXIEBZyGBFWFAAAYHYJBWQpVCSgChXZBCyCtLGs2ZMQdkl1PWiFaaYTlyxYQnpbCKb+C96r5VsMhjfRWf5bM+Uy+9IJhDgA+ebFEW+zdHen7qC9p0KsKXWmGtqw6sFoJSutOmyNDmFN2iwSlEoDgz1tOf3FnUkAUkllgWGd9LBy0qJntLXyKRfmh8/v0BKrBYoemdjG+PvoyP+FBRlUJ6tQQazUkqKEUS/TZuqeo3qXRjEztROMHdOE1qoBRiKEZdvw6fA9MpofRiWWaN/1UFxgbbQFPZq/t6Kf75sPzSrny3Npu2HLX7a0MRoyAGj/rBwJmnlmFO8iw0ImFaQwRi9Gg8Vx0NFpp1nEimDZs5IycdsPI6UkiZDNh0UtD5ckokeN9HpkuM6lM4AkwUX5GpHGGdPaLym4U7ZofVidWxph/nq1jX4dgCdt/5Gs20Yo+PpBARwuC0w61+syBkbbJqiY8G3ADaAeFvV3uKoRYtH1WE5B/QKxEsqFoOttRCw4gJkc6iqHCPdBF3XfPxQUthANl8IWGNQbcfMF7V45mguQKEiXxTbbnkR5XbFwberjtC/j/iZ8Fr097QZvgjWqEsWg1pU3e1pa/Dgmg3sJPb2yk7jWEryqSfbd7roNeL07+waBOTdI8K0jRDwkV8j+MhxCHBNas10NmcVaLTLsplJaD3z7z/ubTs9
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create WemaNIPService NameInquiry"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/WemaNIPService/NameInquiry"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create WemaNIPService NameInquiry

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NameInquiry"}},"text/json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NameInquiry"}},"application/*+json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NameInquiry"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}}}}}}
>
  
</StatusCodes>


      