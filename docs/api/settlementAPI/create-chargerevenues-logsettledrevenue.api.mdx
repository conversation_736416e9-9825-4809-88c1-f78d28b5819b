---
id: create-chargerevenues-logsettledrevenue
title: "Create charge revenues log settled revenue"
description: "Create charge revenues log settled revenue"
sidebar_label: "Create charge revenues log settled revenue"
hide_title: true
hide_table_of_contents: true
api: eJztWE2PGjkQ/SuoTlnJhGz21jcyijIjzc6OgJwQh6JdNJ51245dRiGt/u+Ru5vPYTcwufYJyfXhV6/KD6gKrCOPrKx5kJBB7gmZ7tboC/K0IRMpPNoiELMm2Z2AAMYiQDaH1nPSnS8EOPRYEpNP5goMlgQZfPn893T8dXYPApSBDNaEkjwI8PQtKk8SMvaRBIR8TSVCVgFvXYoM7JUpoK4XyTk4awKFZP/44UP6kBRyr1wqADKYxjynEEBAbg2T4SYTfeeR05hurl7fYJcvlDMIcD5xwarNH7pUB8eltZrQQC2gpBCwoNc4BZioNS41tRXVAiTy8XXoPW4TD0xl+DUMJS9dsrK+RIYMYlQyAcLSxq7a1tXEctkwvHeVNiZYtYC2myUZntroc5rQijyZ/Lp6zqNnHk3APHXg7gyEMkzFKQpl+K+PpyBmjXMFZGKZZurhaTobP81AwKfx7O4+TdU5KFacEMH0JEk45JUP1/HmvE1dJnm5z51ZmWJC7Le31Ne+JDnm/8UhkWnIqmz6UlqpVuqWmNR5KVXiHvXz0eCsUAc68PSF+OSlTrqXlG591WCHBT2143NdqSlgqn7Qle4r5QM//8fzOXTIq0vTp/HtsWwZdQoOVyJtAiaUWy+vDTH0/c34nKeNsjG8Lf53Z+FRtczK3cE9GqnJt1PmnFZ58z0xegm2V9JeSXsl7ZW0V9KblbT5NdpLaC+hvYT2EtpL6O0SWqfkp3/775rxHuRNpsFubzHQthh0L3BwWF2UxGubdh3OhkbikNeQwQidGrUZhrsMI22LYZdheMgQyG92C47oddpnMLuQjUYHHRlK2rwvqAwOt8HqmJC+z20JaZGhzMo2nO5ZaPwGBwUZjJ8fQEC6pi1x82cz3DZwic1XR7dZuan0E9b2LT3aj9SiLajqWJkDutTdM15AwCVmFgLWidNsDlW1xEBfva7rdPwtkt9CNl8I2KBX7aDMF7XYbYISlVKFZJD7ITmDu1/pwLtJtzb6YwDichn/0vZ077RBHZMb1KLqrOM8J8dHtuMUi6NJef5nOoO6/glHHqVY
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create charge revenues log settled revenue"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/charge-revenues/log-settled-revenue"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create charge revenues log settled revenue

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"amount":{"type":"number","format":"double"},"settlementSourceReference":{"type":"string","nullable":true},"settlementSourceTransactionCount":{"type":"integer","format":"int32"},"settlementType":{"enum":["INSTANT","BATCH"],"type":"string","title":"SettlementTypes"},"settledId":{"type":"string","format":"uuid"},"processed":{"type":"boolean"},"processingRetryCount":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChargeRevenueResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetChargeRevenueResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"amount":{"type":"number","format":"double"},"settlementSourceReference":{"type":"string","nullable":true},"settlementSourceTransactionCount":{"type":"integer","format":"int32"},"settlementType":{"enum":["INSTANT","BATCH"],"type":"string","title":"SettlementTypes"},"settledId":{"type":"string","format":"uuid"},"processed":{"type":"boolean"},"processingRetryCount":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChargeRevenueResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetChargeRevenueResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"amount":{"type":"number","format":"double"},"settlementSourceReference":{"type":"string","nullable":true},"settlementSourceTransactionCount":{"type":"integer","format":"int32"},"settlementType":{"enum":["INSTANT","BATCH"],"type":"string","title":"SettlementTypes"},"settledId":{"type":"string","format":"uuid"},"processed":{"type":"boolean"},"processingRetryCount":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChargeRevenueResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetChargeRevenueResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      