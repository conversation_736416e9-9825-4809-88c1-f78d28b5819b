---
id: add-batch-period
title: "Add new batch period"
description: "Add new batch period"
sidebar_label: "Add new batch period"
hide_title: true
hide_table_of_contents: true
api: eJztWE2P2zgM/SsBT92u2mS7N98yxaDTQ7fBZHoKcmAsJlErS6pEzzQI/N8L2vlwkmnrdNGbc3EgkRT5SD2b3IIPFJGNd+81ZIBa3yDn6wlF4zUoYFwlyGbQXp0rCBixIKYom1twWBBk8O72w3T86eEOFBgHGawJNUVQEOlraSJpyDiWpCDlayoQsi3wJohm4mjcCqpq3ghT4huvNyKRe8fkWP5iCNbktbfDz8k7Wbsw5RefKWdQEKLExoaS7C6jL+58GVuSxjGtav+WPhbIzdK/b6BStfgH40qmjgrsr7DO/irboca9s/1KSR6NwIR20kJhiTaRAjZsxcL4JNn3Dey1OtM37hH+kwi3S/nl3z3UfwxqMRApBe9Sg92b0UgemlIeTRC7kMG0zHNKCVSbbupbECyaq3KTdqaOggvvLaGT4AtKCVdtqHbMp8CV1uJCYhGOrBRo5A7HLY5BC4Nf2j0AWpZGiw8tjbded/SFllhafj4oKa8HU3SzxL6z6JWVoiCPhEx6zD/FQSPTKxYnJCFem6X5PZ2bTacwjuK3BRrbSScRs6WCHE+iXxpL7XrCGHEj71imIv26QgqK+Rod35TJOErpP+yI/17xo1t4jNq41fMVdqGIJfvpIYLniqbrrX5HPD2H4n53myUVZ0dfYfaELPYG/5/+/nmHTluKFzx/Lcv3TNIzSc8kPZPE3/8m7ymkp5CeQnoKiXUnVN+ddtsz1nrg6GlQ38JB2A99CuK1l3FQ8KlOIfIaMhhiMMPWUcPx+bQoUXzcz4TKaGUExBxSNhyuqEhlWEXU9Fr+59aX+jWGADLyEVq7P459br9hESyddrqj0z52dOxSR+0edHR6VUeVzKKWvs77AbAiBdwMjskcjCfvQYH43gDz+E/dqvrEBdaEu5tw/QCwE1QPBdbqHyvV4LHdYTkDDAYUnMJ3hudcwVoSkM1gu11gok/RVpUsfy0pbiCbzRU8YjRN0c3mldqP3CQB2iTZ0IeCOfPy0OnCi/vdfO6vgYz8nvP+C21OB3yPaEsRg0ptd7tvG4OvHsTAUeLiE/ioMc5zCtySbR86bxXi5OP0ARQsdjPBon5VQMQnGS7ik/ihwNeh1TRTr23BoluV9SsOmpPl9x2smk7X
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Add new batch period"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/BatchPeriod/AddBatchPeriod"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Add new batch period

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"fromHour":{"type":"integer","format":"int32"},"fromMinute":{"type":"integer","format":"int32"},"toHour":{"type":"integer","format":"int32"},"toMinute":{"type":"integer","format":"int32"},"periodHour":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"AddBatchPeriodRequest"}},"text/json":{"schema":{"type":"object","properties":{"fromHour":{"type":"integer","format":"int32"},"fromMinute":{"type":"integer","format":"int32"},"toHour":{"type":"integer","format":"int32"},"toMinute":{"type":"integer","format":"int32"},"periodHour":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"AddBatchPeriodRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"fromHour":{"type":"integer","format":"int32"},"fromMinute":{"type":"integer","format":"int32"},"toHour":{"type":"integer","format":"int32"},"toMinute":{"type":"integer","format":"int32"},"periodHour":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"AddBatchPeriodRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      