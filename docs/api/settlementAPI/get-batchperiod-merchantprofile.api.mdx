---
id: get-batchperiod-merchantprofile
title: "Get BatchPeriod MerchantProfile settlementProfileId"
description: "Get BatchPeriod MerchantProfile settlementProfileId"
sidebar_label: "Get BatchPeriod MerchantProfile settlementProfileId"
hide_title: true
hide_table_of_contents: true
api: eJztWFFP2zAQ/ivVPW2SWRl7y1uZEPDAhihoD6gP1/jamjm2sS/Vqij/fXLSpqGEURiThpSntPXdd+fP5y/VV4B15JGVNecSEpgTHyOnC0deWXlBPl2gYeftTGkCAYzzAMktVEGXVRBMBDj0mBGTj4sFGMwIEgjErCkjw5c1wLkEAcpAAg55AQI83efKk4SEfU4CQrqgDCEpgFeugmCvzBwEzKzPkCGBPFcSylI0VU5PLsajm+uzDfSCUJJ/AXhZTmJwcNYECnH96PAwPiSF1CsXyYEExnmaUgggILWGyXCFRL946DTGysXjCnZ6RymDAOcjz6xq/LCG2gZOrdWEBkoBGYWAc+oiweRa41RTvaNSgETep1zHOTxPceykPv7jPChDIXyrCN+jrU3idzO16KUy8+6KjxIxZztuuu3mZ7odvTaD6D2u4gwwZeF5Tlooe7LRyvhq5Z7nQzPM9RMbmXmbXas9KWW7d2h9d89s7lvhyjDNq2vRbEwZ/nIUG0k9IZMc8R95kMh0wLGJeMJWqpl6Xc7xar8hasJPMlR6r5xHk/4XE/I/Tn8MklJFRUJ92Wp3hjqQAFasK00kHu9ScbVWuHgUO6VfANsS/rcBfNTnD8WLzir/AHTzPEMjNfm6hnNapdUrcXgXbC/svbD3wt4Ley/s713Yq//qvaL3it4req/ovaK/e0Uvy+ratj2aU+JBK3ewMbDW0INuRyojXti1+xVnK3pTCQzRqWELbLgDNiw60EqIk+2XGycs9zpaUswuJMPhNuFA0vLTnLLgcBWszmP/n1KbQfSilJnZaqQalqq4wZaqwejyHATEMvXGl5/j0TobOMPq/bYxx15FyANSm9FueV2lqHdWrMm6BXQKxANbUMBOMRCQdNWbCFjYwBGlKKYY6Mbrsow/3+fkV5DcTgQs0at6am8LkCrEz7KZp52GG4MOPlytTcCPg+hedm1kIzkmCs4SdR6/gYCftHrCwywnpdjYjG/eTl22ZWo2LUXPs14dpSk5bq21ISatgT49uYay/A11dsJH
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get BatchPeriod MerchantProfile settlementProfileId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/BatchPeriod/MerchantProfile/{settlementProfileId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get BatchPeriod MerchantProfile settlementProfileId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementProfileId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"batchPeriods":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponse"}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"batchPeriods":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponse"}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"batchPeriods":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponse"}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      