---
id: delete-settlementaccount-merchant
title: "Delete SettlementAccount settlementAccountId merchant merchantId"
description: "Delete SettlementAccount settlementAccountId merchant merchantId"
sidebar_label: "Delete SettlementAccount settlementAccountId merchant merchantId"
hide_title: true
hide_table_of_contents: true
api: eJy9k09v2zAMxb+K8U4boNXdjroFaLAFWIFhaU9BDqrNxMJsSZXoYIGh7z4w/9t4hwLdTrYl+pH8kW+ADxQNW+9mNTRqaolpTswtdeTYVJXvHd9TrBrjGAps1gl6gXPMZB+DpUIw0XTEFCVkgDMdQSO9Dp3VULAOGsFwA4VIz72NVENz7EkhVQ11BnoAb8NOgqN1ayisfOwMQ6PvbY2c1SlLd6jxn4h/nd7PJ48P347SDZma4hvEc15KcAreJUpy/+X2Vh41pSraIBOAxryvKkpJcr+6udtNprjCXozQLY4sihdQOuLGn6cMtSekUZpgyyvlchiRzuVRshzO4hkKieLmOPg+tgKJOSRdlmeZTzVtbtbUpWC2ybe99HZT+Q5Cx7qV34Gz3O6Y7+MuWi4mP2ZQkDR7KJvPyArBJ+6Mk38P43oHWC/on6bJ9JvL0BrrJPGuzeFAcQETLNSIMRT0uAW6s630RfKlQuMTi+QwPJlEj7HNWY6fe4pb6MVSYWOiNU8CajGgtknea+iVaRNdVV95x+Rksz/8PCzsx0LMPNbV4dC4rbA2bS9fUPhF27+YWYzyH0u4QJWXWR3N+O4g9tkurH+qRNb15Ka76ffpwxQ5/wGKiNZe
sidebar_class_name: "delete api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete SettlementAccount settlementAccountId merchant merchantId"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/SettlementAccount/{settlementAccountId}/merchant/{merchantId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete SettlementAccount settlementAccountId merchant merchantId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      