---
id: delete-settlementaccount-merchant
title: "Delete SettlementAccount settlementAccountId merchant merchantId"
description: "Delete SettlementAccount settlementAccountId merchant merchantId"
sidebar_label: "Delete SettlementAccount settlementAccountId merchant merchantId"
hide_title: true
hide_table_of_contents: true
api: eJy9k09rGzEQxb/K8k4tiGzao26GmDbQQKmTk/Fhshp7RXe1ijQyNYu+e5H/J3YPhbSn1UqjN6PfzBsxeA4kdnD3BhqGOxaesUjHPTuhphmSkwcOTUtOoCC0itBznGImuxgsFDwF6lk4lJARjnqGRnwbem+gYB00PEkLhcAvyQY20BISK8Sm5Z6gR8jGbyUkWLeCwnIIPQk0UrIGOatjln5f4z8R/zJ9mE2eHr8epFsmw+EvxHNelODoBxc5lvPPt7flYzg2wfrSAWjMUtNwjCX3m5O7bWeqC+zVFbrVgUX1CkrP0g6nLkPtCGnU5G19oVyPV6RzfZCsx5N4hkLksD40PoWuQBLxUdf1ivuY/CqQ4ZuybrohmRvyHgWKdcthy8tKt0XNffS0OXtpNfl+D4WivmOx/oSs4IcoPblyd9+ld2D0CvqxicK/pPYdWVcSb1837uHNQd5CXfGDgr4++f3JTfos+UKhHaIUyXF8pshPocu5bL8kDhvo+UJhTcHScwE1H2FsLGsDvaQu8kX1zeCEXRnoDz/2c/qxKh6+9qr9JrlNYU1dKn9Q+MmbP3i4+OM/lnCGKi+yOnjw3UHssp05/lhJGdejie6m36aPU+T8GwVU0pM=
sidebar_class_name: "delete api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete SettlementAccount settlementAccountId merchant merchantId"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/SettlementAccount/{settlementAccountId}/merchant/{merchantId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete SettlementAccount settlementAccountId merchant merchantId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      