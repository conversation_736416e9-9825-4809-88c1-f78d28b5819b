---
id: update-gemspaybankaccount
title: "Update gemspay bank account id"
description: "Update gemspay bank account id"
sidebar_label: "Update gemspay bank account id"
hide_title: true
hide_table_of_contents: true
api: eJztW9tu2zgQ/RVjnvYi123SuLt+c4IgDdBL4CRPQR7G4thmQ5EqSbk1DP37gpQsyZe0chAs0pp5sS3ODCnynEOOolmCSkmj5UpeMhhAljK0dEGJSXExRvmAcawyaSECi1MDgzsoG09RPgzLxvsIUtSYkCXtbJYgMSEYAGcQAZcwgBTtDCLQ9DXjmhgMrM4oAhPPKEEYLMEuUudhrOZyChFMlE7QuiFlnEGeR1XQi/OP18Pbm/er0DNCRnqP4Hl+XxiTsaeKLZxFrKQlad1XTFPBYz8nvS9GSXdtK5Qaf6HYTUuq3QxaTsb7FjPyyY90+6ZkJgSOBRUjzKPKPkvGpFt5uEU5U6xdeGfceiwPi/gDzUm0izyXrewSlA5RI5qQJhnTHndaGCyBZJY44MVKCIrdskAEhqwVlJCHZoVhh8TNuJZb4WGzhdubRUoG8gjSTKfKtJslTQnqh1amKS7cAM9mKCWJ1ou27tZ6+dbdrkU2fYKbE4FWTtpK0rutN5jrWMotR6t063upPM4T5G0A6ajEGHcYQHHVoOQEhaEaBEPGbpsS10DDqBAEJzVg6bsN3A/cD9w/PO43t/+//g4iEEQgiMCBiUDul9mkSpqC1UevX7sPRibWPPVAG8B1FsdkDETN5MGfHFKBfC/VMGWo2nCslCCUbvYSMgan7eaNoW3RHW+3bEHLnl/Lfs6sg5Yiryp7dOPt97ibH6jWDjhbPqfdnBRo7EfF+IQTO120Crfu0lbXIog1oSU2tD/krMNl1/KEvGKU3ezjE89QTumDmjZVCLXGhXvOYikxz6YrqabLZF3RglAFofqFhOqQpKHtyeqC7PaZ6rMUi1F5kvLUV8YG7gfuB+4fHvf/hywwKv+B9UnZtmpQJHO/3oRvzvbZ6vzWnPInJsk7l7OO+yxhVp/vUTJBeuvR274P3kIKHXansDuFFDqk0EGoglC9fKE6JGkIKXTgfuB+4H5IoV/UhP+OKfST3lwLuXPYlsK2FHLnkDsHoQpC9fKF6pCkIeTOgfuB+4H7IXd+URP+++XO/q3v9Te8izfFO9MiSseJa6cU8I4vbU3IzhQryltjV9/qy1wH0MOU90q3rnPrlm69JWe5r17Q81WtbKaFq2K1NjWDXq+ua+gymr8qgxglMjeoV7FKwJWvuhR/VJewnn/HJBUedGs7Ur3cGxtP3VDvL+vXNgPUu0XDzm0K9c/HtL+2KEG4VslRqXltthLt+soubX6sdXPku5T2sVZfBd1oqzI2OJ7gPyeT/tvuybs377pvT/pH3fHxJO4exf/2jyf9Pk6wD1syU8faVJNVixeaifIUXadX57rCQmd4dQkROMwU0Jy/WZ1nEvQCUhZH/xSyawivVKFRtuBUTHu1K8B8B5hyiGAXnCGCAWeu6GamjHW2y+UYDd1qkefu8teM9AIGd/cRzFHzQgzulsC4cd9ZRdqNYVUVFfDHqKzq/rNT42d9uKtsUbpccY4ic78cYmlR1KDn93m0KhN/9t6LXhpF6dUIXM160XpWBOy6gqOGxdb7LrXHMI4ptQ3bZqf3DeW5Gt6cuV7HZSF7UlBD4zfHI/xWTIXy9+YPtv7aEgTKaeZPwlB07f7+AwycgS0=
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update gemspay bank account id"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/gemspay-bank-account/{id}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update gemspay bank account id

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"id","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"enum":["collection","settlement","operation"],"type":"string","title":"GemspayBankAccountTypes"},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"paymentChannelId":{"type":"string","nullable":true},"partnerId":{"type":"string","format":"uuid"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddUpdateGemspayBankAccountRequest"}},"text/json":{"schema":{"type":"object","properties":{"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"enum":["collection","settlement","operation"],"type":"string","title":"GemspayBankAccountTypes"},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"paymentChannelId":{"type":"string","nullable":true},"partnerId":{"type":"string","format":"uuid"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddUpdateGemspayBankAccountRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"enum":["collection","settlement","operation"],"type":"string","title":"GemspayBankAccountTypes"},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"paymentChannelId":{"type":"string","nullable":true},"partnerId":{"type":"string","format":"uuid"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddUpdateGemspayBankAccountRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"partnerName":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"partnerId":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"changeLogs":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"preImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"postImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true},"updateNote":{"type":"string","nullable":true},"action":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GemspayBankAccountChangeLogResponse"},"nullable":true}},"additionalProperties":false,"title":"GetGemspayBankAccountResponse"}},"additionalProperties":false,"title":"GetGemspayBankAccountResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"partnerName":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"partnerId":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"changeLogs":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"preImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"postImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true},"updateNote":{"type":"string","nullable":true},"action":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GemspayBankAccountChangeLogResponse"},"nullable":true}},"additionalProperties":false,"title":"GetGemspayBankAccountResponse"}},"additionalProperties":false,"title":"GetGemspayBankAccountResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"partnerName":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"partnerId":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"changeLogs":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"preImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"postImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true},"updateNote":{"type":"string","nullable":true},"action":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GemspayBankAccountChangeLogResponse"},"nullable":true}},"additionalProperties":false,"title":"GetGemspayBankAccountResponse"}},"additionalProperties":false,"title":"GetGemspayBankAccountResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      