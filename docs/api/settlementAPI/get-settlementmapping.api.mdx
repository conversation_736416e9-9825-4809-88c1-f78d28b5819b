---
id: get-settlementmapping
title: "Get SettlementMapping settlementMappingId"
description: "Get SettlementMapping settlementMappingId"
sidebar_label: "Get SettlementMapping settlementMappingId"
hide_title: true
hide_table_of_contents: true
api: eJytUz1v2zAQ/SvGm1qAjdKO3DIErocARZ1MhoeLdLaJSiRDnowKAv97cZbtpLE7BOgkivfx7j2+GxEiJxIX/KKBxZZlySItd+yloxid38JAaJthV3iNPRxja4NIiToWTpoywlPHsMjvUxcNDJyHRSTZwSDxS+8SN7CSejbI9Y47gh0hQzy0kDTBb0LqSGDR965BKeaMMr9/WN49PX4/td4xNZw+0LyUtSbnGHzmrPFvt7f6aTjXyUVVBhbLvq45Z8V+F5mzzC5kmV1n37HswlFmmEkHi4qiqy5aVOOVHgUGmdP+pHWfWiUtErOtqteCLw3vb7bc5UhDDm2vs97UoYOydX4TDkI4aQ8aTnlvWMzufixgoDATyf1XFIMYsnTktfYk/wfI/yXb+RmEf0sVW3JeEQ58xqMwK1B0MFdMZ2CvYawNdiGLVo7jM2V+Sm0pev3ScxpgV2uDPSVHz0p8NaJxWc8N7IbazBdD1sELe3Xep59HQ32e6T5cG/54SX5Q7ajt9Q8Gv3j4xz6UdTEny/73cSbYNwtyHklNcLbi/P4RpfwBIUJlow==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementMapping settlementMappingId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementMapping/{settlementMappingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementMapping settlementMappingId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementMappingId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      