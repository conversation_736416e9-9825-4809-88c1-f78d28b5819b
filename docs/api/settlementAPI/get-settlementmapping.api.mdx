---
id: get-settlementmapping
title: "Get SettlementMapping settlementMappingId"
description: "Get SettlementMapping settlementMappingId"
sidebar_label: "Get SettlementMapping settlementMappingId"
hide_title: true
hide_table_of_contents: true
api: eJytU7Fu2zAQ/RXjTQ1ARElHbhkC10OAok4mQ8NFOttEJYohT0YFgf9enC27aawOATqJ4h3fu/fwbkQXOJK4zq9qWOxY1izScMteWgrB+R0MhHYJdoM/taepVhoEitSycNSWEZ5ahkX62LqqYeA8LALJHgaR33oXuYaV2LNBqvbcEuwIGcIRQuKJftvFlgQWfe9q5GwuLMvHp/XDy/O3M/Seqeb4CfCcS21OofOJk9a/3t3pp+ZURRfUGVis+6rilJT7Q2XJsriyZTGvvmXZd5PNMCcfLAoKrriCKMYZjAyDxPFw9rqPjYoWCckWxY7b1IddpJpv9Vw1XV/fUghQkc5vu6N+J83ROm5ToOHd8IuH7ysYKPpJ2+Ee2SB0SVry+vbs+ic0/+XWxX3hX1KEhpxXhqOMcfJjAwoOZiZrBnaOozTYd0n05Ti+UuKX2OSs1289xwF2UxocKDp6VeGbEbVLeq5ht9Qkvhqy6ryw18B9+THl6GahazA3/HRJflDvqOn1DwY/efjHGuQym3NS//s4J9p3e3EZSUNwSeDy8Rk5/wYT6mHY
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementMapping settlementMappingId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementMapping/{settlementMappingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementMapping settlementMappingId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementMappingId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      