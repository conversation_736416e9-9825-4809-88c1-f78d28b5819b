---
id: create-settlements-retry
title: "Create settlements settlementId retry"
description: "Create settlements settlementId retry"
sidebar_label: "Create settlements settlementId retry"
hide_title: true
hide_table_of_contents: true
api: eJztVcFu2zAM/ZXgnTZAqLsdfSuGYe2hWNG0pyAHxmITdbKlSnKwwNC/D7Qdx+2KYsN27EmyKD6ST/RjB+c5UDKuudIoUQWmxEtOyXLNTYq3nMIBCom2EeUKJxPWCp4C1Zw4iK1DQzWjRJzuXGkomAYlPKUdFAI/tSawRplCywqx2nFNKDukg+99UzDNFgoPLtSUUKJtjUbOaoL/9vV6eXF/d3mE3jFpDn8BnvNaLkfvmshR7J/Pz2XRHKtgvLCBEsu2qjhGKFSuSVKxIPHPVHhLErn7PYLbPHKVoOCDEJvMgB9HqNPFjXOWqUFWqDlG2vJrJDSttbSxPFSUFTSlV8OR1kbSJnszC/xANrJCMkkgxre75lDtqEm3IwHC7b/4H9dLarTlMMB5b03Vt1XxGN07V29w1XfUO0lvkZQF8fnP+aUXqsVJauJiLjuLMOpWzWnnRNm8iz2NokMlCvKmmDkX3dw7F0f3yGF/VLc2WFGblHwsi2LLdWz9NpDmM9lX1rX6jLyHqItpHlxP61jpN66jp8PiJJ+Li5srKAj6UNH+k7ydpFlT3wuj3P1ppc/4mR50pldZDSV0IwkrkDd9jRMyFMoX6j3ArxV2QmC5QtdtKPJ9sDnL8VPL4YBytVbYUzBDc606aBNlr6dHf5HfpKn4cDvq9seFDJrX8h4PqZFC92Rb+YLCDz68nDd5ndVxJPz3PIZ4swE05SLzabBeVBX7NLPNIdazjrz5vrxDzr8A/EukHg==
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create settlements settlementId retry"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/settlements/{settlementId}/retry"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create settlements settlementId retry

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","additionalProperties":false,"title":"SettleMerchantResponse"}},"additionalProperties":false,"title":"SettleMerchantResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","additionalProperties":false,"title":"SettleMerchantResponse"}},"additionalProperties":false,"title":"SettleMerchantResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","additionalProperties":false,"title":"SettleMerchantResponse"}},"additionalProperties":false,"title":"SettleMerchantResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      