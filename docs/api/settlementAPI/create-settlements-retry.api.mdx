---
id: create-settlements-retry
title: "Create settlements settlementId retry"
description: "Create settlements settlementId retry"
sidebar_label: "Create settlements settlementId retry"
hide_title: true
hide_table_of_contents: true
api: eJztVcFu2zAM/ZXgnTZAq7sdfSuGoe2hWJC0pyAHxmYTdbKlSnKwwNC/D7Qdx+2CYsN27Mm2KD6Sj/RjC+vYU9S2vi2Ro/BMkZcco+GK6xgWHP0BCpG2AfkKJxPWCo48VRzZi61FTRUjRxjv3JZQ0DVyOIo7KHh+brTnEnn0DSuEYscVIW8RD67zjV7XWyg8Wl9RRI6m0SVSUiP89be75dXD/c0ResdUsv8L8JTWcjk4WwcOYv9yeSmPkkPhtRM2kGPZFAWHAIXC1lEqFiT+GTNnSCK3v0ewmycuIhScF2Kj7vHDAHW6uLHWMNVIChWHQFs+R0LdGEMbw31FSaGkeDYclaWWtMnMJ4EfyQRWiDoKxNC7O/bFjuq4GAgQbv/F//i8obo07Hs454wuurHKnoJ95+oNrrqJeifpLZKSIL78Ob92QjU7SU2YTWVn5gfdqjjurCibs6GjUXQoR0ZOZxPnrJ16p+zoHtjvj+rWeCNqE6MLeTZx/lTy/mLLVXB0CNY0kuFFYSuIzOj60Xb8DiVf9/dmJx2dXc1voSBh+tL2n6WJkm9F3VAMuvenJb8gauzsRLiS6mtpBzZWIKe7YkdkKOSvZLyHXyvshMl8hbbdUOAHb1KS4+eG/QH5aq2wJ6/7KVu1KHWQ93Ls/qv8RnHFh8Ug4B9nsnHO5T0cUi2F7sk08gWFH3x4vXjSOqnjbvjvefTxJptozEUWVW+9Kgp2cWKbQqwnozn/vrxHSr8AfKGn6Q==
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create settlements settlementId retry"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/settlements/{settlementId}/retry"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create settlements settlementId retry

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","additionalProperties":false,"title":"SettleMerchantResponse"}},"additionalProperties":false,"title":"SettleMerchantResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","additionalProperties":false,"title":"SettleMerchantResponse"}},"additionalProperties":false,"title":"SettleMerchantResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","additionalProperties":false,"title":"SettleMerchantResponse"}},"additionalProperties":false,"title":"SettleMerchantResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      