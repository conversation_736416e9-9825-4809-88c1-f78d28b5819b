---
id: get-settled
title: "Get settled settledId"
description: "Get settled settledId"
sidebar_label: "Get settled settledId"
hide_title: true
hide_table_of_contents: true
api: eJytUk1vnDAQ/SvonVrJDWmPvuUQbfdQqeompxUHB2YXq2A79rAqQv7v1bBAk34cIvUEzLyZee/xJvhA0bD1bt9A40x8IOaOGiiwOSfoI9ZKpRBMND0xRWlMcKYnaKQrYC9D1kEjGG6hEOl5sJEaaI4DKaS6pd5AT+AxzIMcrTtD4eRjbxgaw2Ab5Ky23bv7L4e7x4fP6+qWTEPxDctzrgScgneJkvQ/3d7Ko6FURxtEOzQOQ11TSnL7t86OuFgUFi+V9sStX0yDumrWKE2w5QIrpw2foZAoXlbnhtiJGOaQdLnge3L8oaHLzZn6FMyYfDcIh5va9xAV1p38LNByN3tzxRWHbby4+7qHgpy5kr98RFYIPnFvnMyutv5D1Cvpm5VMP7gMnbFOts3cp0XwESbYWdwaG/1rX6XQ+sSCmqYnk+gxdjlL+XmgOEIfK4WLidY8iaDjhMYmeW+gT6ZL9Aeh2jsmJ0l5920JwPtCsvo3okvRuFE8Md0gX1D4TuOr1OYqqzVY/53E9diLGG9E5JduIdrdPyDnn3XRNLM=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settled settledId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settled/{settledId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settled settledId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settledId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      