---
id: get-settled-merchant
title: "Get settled merchant merchantId"
description: "Get settled merchant merchantId"
sidebar_label: "Get settled merchant merchantId"
hide_title: true
hide_table_of_contents: true
api: eJytVU1v2kAU/CvonVppE9L05htqUsohFQrkFHF42A97Ve+us/uMSq3979X6AzClwYo4Ie/OzL4ZPQ0VmIIssjR6lkAEKfGCmHNKnsjGGWoGAYypg+gV2htYCSjQoiImGy4q0KgIIphjSj9LtSYLAqSGCN5KsjsQ4OKMFEJUAe+KAJWaKa1xG2MVcnP09R68Fz29hfxD11H7lqHWlF8Uc2ylTnvUiTJlHcX7TN1530+RmHKdU09raVE7jEPkz7QhSzq+bPDMTHOy0iQfYX63Rg3lHXtBphuW6sSOuZrUA+6uptWt7+xDCc3cTM+tSS05d5G/NiYn1D0B9c/zBXIGAiy9ldJSAhHbkgYZLEuZ9MSnj0+LycvyRyedESb12g0V934VwK4w2pEL9/d3d+EnIRdbWYTdhAgWZRyHALwXJzdT4pFr2mDUWR31PCvizLSNAqJxH8EYCzluieMOP64OTB8CJrvtiqW0eTDIXLho3DIVab5JaHubknIF7pzJyzDXbWwUBGdSb0xtWnJe59XgRos9fTSZz0BAeKYxtP0CXkBhHCvUgdtFfdFoL5h90Ey/eVzkKHXQrV1UbQivgIWsbTZtKvbLAgKiI+2VgMw4DoyqWqOjF5t7H46bVQz5JNLhOqhEG8wdCfhFu9Mi3mJehpkgrNA7hLZph8APVToEvW/PIeD/1OMgE10jDgG3JThoJDMQ2BTYEGSvnoYQThrpQFmFDysD5/xGnCxobDSTDr3y6bmti8+j8Cd/bnHbQ9Q9X91MR7vqV150PXT1KZrXjlqv537fNNPHJXj/FzXm8NQ=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settled merchant merchantId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settled/merchant/{merchantId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settled merchant merchantId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"Channel","in":"query","schema":{"type":"string"}},{"name":"Amount","in":"query","schema":{"type":"number","format":"double"}},{"name":"TransactionReference","in":"query","schema":{"type":"string"}},{"name":"Period","in":"query","schema":{"type":"string"}},{"name":"From","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"To","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"Day","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"MerchantId","in":"query","schema":{"type":"string"}},{"name":"IsInProgress","in":"query","schema":{"type":"boolean"}},{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      