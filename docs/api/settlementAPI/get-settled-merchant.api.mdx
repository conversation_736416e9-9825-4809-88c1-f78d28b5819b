---
id: get-settled-merchant
title: "Get settled merchant merchantId"
description: "Get settled merchant merchantId"
sidebar_label: "Get settled merchant merchantId"
hide_title: true
hide_table_of_contents: true
api: eJytVctu2zAQ/BVjTy3ARml60y1o09SHFEacnAIf1tJaIio+Qq6CugL/vaAetuWmsRD4ZJmcGe0MFqMGjCWHLI2e55BCQbwk5oryO3JZiZpBAGPhIX2C/gZWAiw6VMTk4kUDGhVBCgss6Get1uRAgNSQwnNNbgsCfFaSQkgb4K2NUKmZiha3MU4hd0dfriAEMdJbyj90HrWvJWpN1Ukxz07qYkS9VqZuo3ibqQfvuylyU68rGmk9ONQesxj5PW3Ikc5OG3xlpgU5afL3ML87o6byDr0g0yeW6siOOZvUN9yeTWtY3/m7Epr7uV44Uzjy/iR/bUxFqEcC6p/XW+QSBDh6rqWjHFJ2NU0yWNcyH4nf3twtrx8ffgzSJWHert1U8RBWEeyt0Z58vL+6vIw/OfnMSRt3E1JY1lkWAwhBHN3cEs981wazweps5FkRl6ZvFBCd+xQStDLpicmAT5o9M8SAyb0MxVK7Khpktj5NkoKUr23hMKeL+JxVps4v0FqIhqTemNar5KqNiZS3uJ11taVI8+x6MQcBUb3z8fIZggBrPCvUkTskfNLfKI9dvky/ObEVSh112+Gb3vsToJWtu65ExW5HQEB6oL0SUBrPkdE0a/T06KoQ4nG3gTGWXHpcR5V0g5UnAb9oe9y/L1jVcSaIm/MGoS/YKfB9g05B70pzCvg/rTjJxFCEU8B9900ayUwEdr01BTlqpSmEoyLaU1bxj5OR8/pGHC1oZjSTjnXy4b5viY+z+G1/bXH7Q9QjX8NMB7saVkEM9XP2Kbq3HZTdyP2uYG5vHiCEv4Lg7Qk=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settled merchant merchantId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settled/merchant/{merchantId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settled merchant merchantId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"Channel","in":"query","schema":{"type":"string"}},{"name":"Amount","in":"query","schema":{"type":"number","format":"double"}},{"name":"TransactionReference","in":"query","schema":{"type":"string"}},{"name":"Period","in":"query","schema":{"type":"string"}},{"name":"From","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"To","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"Day","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"MerchantId","in":"query","schema":{"type":"string"}},{"name":"IsInProgress","in":"query","schema":{"type":"boolean"}},{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      