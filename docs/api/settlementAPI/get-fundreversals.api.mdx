---
id: get-fundreversals
title: "Get fund reversals"
description: "Get fund reversals"
sidebar_label: "Get fund reversals"
hide_title: true
hide_table_of_contents: true
api: eJztWc+v2jgQ/lfQnHaltLTdG7e3Vfe1UrdC0J6eOAz2AG4d27XHqCzK/76ahEB4j7aB3UMPOQH2fOP55flCZg8+UEQ23r3TMIE18V/Z6UhbigltggIY1wkmDyDrs8M6LAoIGLEkpii7e3BYEkxgimv6kMslRSjAOJjA10xxBwUktaESYbIH3gURNY5pXcutfCyRm6U/XkFVFWf65uYf+n+0hegVpUT6p+qW3ltCBwVoWmG2DJMV2kRdbfdv/p7fffr4tlW2IdS1BZG+ZhNJw4RjpgvaE0fj1lBVCxFOwbtESfZfvXghH5qSiiZIWmAC86zEaihAecfkuNZE33gcLMrJ+6cn+OVnUgyFuBwosmn0p4Oqp45WBZSUEq7pqZ0FuGwtLi01HlUFaOTucRgjShwNU5l+bobRlw45Ji5no8UgjugSqrY6eyBKimqDjnuKY+mz4yme2ePa8j2Ka5/F93OLZrSiSE71i1eiuDWKptFvjab4oa6g63Gvvb4JN7d53Qsn0XNke8nGqyKgcmJfUvwT3ZfebnRBvWPWgu6UkvTejGvqoF8kOO5eC6hXRypgVTdTSRLa6wrp1MIu3uHkc1R0VYxPkP5VWUOuje85qn90VSRk0nf8wzutkekZm7K+p6XXZmWuwUg30NrIzUY77XSruu0XwIZt3fQbhmyZcHbo3nLo01ydyLBfWYSW7XpWkYmJp99p2admF82lqFq8Hcue0Qo49bS0BsxI+aj7Qhx9u9m+EGlrfE634f9jKbw3TWB1u/AWnbYUmxoLwRpVP3CNPyc/kPdA3gN5D+Q9kPdA3gN5/+rkXf/nHlh7YO2BtQfWHlh7YO2BtX951q5E9/n79HvikXSDUXfMURJv/GEEIuSJvIEJjDGYscg+68pKE2nnHjlaefXPHNJkPF5TmXJYR9T0XL4r67N+jiGAvOo3buXr2By9KVPA3WhOzJZKcjy6m76DAkR7Y+v2ZV2kPnGJ9WNHO3u45MOZm8cUdCYFVdHYuz/49wAYJBuPPFwUsPGJZX+/X2KiT9FWlSw38xLxW5sk+dPHFH2h3eP5zxZtFgtAhiY/ABwGPH3EuxOck/xCfkTTFNTDoiraWcxFSx+F6ThUgd9mh8HN7yMZfF0KX2NFZ/JzZnSze6cUBe7sdVUsOqV2/+YjVNW/VZKWAw==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get fund reversals"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/fund-reversals"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get fund reversals

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"processed","in":"query","schema":{"type":"boolean","default":false}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"merchantId":{"type":"string","format":"uuid"},"amountPaid":{"type":"number","format":"double"},"transactionReference":{"type":"string","nullable":true},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"serviceProviderSlug":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"customerBankCode":{"type":"string","nullable":true},"customerBankName":{"type":"string","nullable":true},"customerAccountName":{"type":"string","nullable":true},"customerAccountNumber":{"type":"string","nullable":true},"retryCount":{"type":"integer","format":"int32"},"fundReservalReference":{"type":"string","nullable":true},"processed":{"type":"boolean"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetFundReversalResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetFundReversalResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"merchantId":{"type":"string","format":"uuid"},"amountPaid":{"type":"number","format":"double"},"transactionReference":{"type":"string","nullable":true},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"serviceProviderSlug":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"customerBankCode":{"type":"string","nullable":true},"customerBankName":{"type":"string","nullable":true},"customerAccountName":{"type":"string","nullable":true},"customerAccountNumber":{"type":"string","nullable":true},"retryCount":{"type":"integer","format":"int32"},"fundReservalReference":{"type":"string","nullable":true},"processed":{"type":"boolean"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetFundReversalResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetFundReversalResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"merchantId":{"type":"string","format":"uuid"},"amountPaid":{"type":"number","format":"double"},"transactionReference":{"type":"string","nullable":true},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"serviceProviderSlug":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"customerBankCode":{"type":"string","nullable":true},"customerBankName":{"type":"string","nullable":true},"customerAccountName":{"type":"string","nullable":true},"customerAccountNumber":{"type":"string","nullable":true},"retryCount":{"type":"integer","format":"int32"},"fundReservalReference":{"type":"string","nullable":true},"processed":{"type":"boolean"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetFundReversalResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetFundReversalResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      