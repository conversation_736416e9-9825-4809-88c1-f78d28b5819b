---
id: get-fundreversals
title: "Get fund reversals"
description: "Get fund reversals"
sidebar_label: "Get fund reversals"
hide_title: true
hide_table_of_contents: true
api: eJztWc+v2jgQ/lfQnHalvNJtb7mxVftaaVshaE9PHAZ7ALeOndpjVBblf19NQiC8R9tA99BDTgh7vvH88nxJZg++pIBsvHunIYc18ZvkdKAthYg2QgaM6wj5A8j67LAOiwxKDFgQU5DdPTgsCHKY4po+pGJJATIwDnL4mijsIIOoNlQg5HvgXSmixjGta7mVDwVys/TyBVRVdqZvbv6l/0dbGbyiGEn/VN3Se0voIANNK0yWIV+hjdTVdv/6/Xzy6ePbVtmGUNcWBPqaTCANOYdEF7RHDsatoaoWIhxL7yJF2X/x/Ln8aIoqmFLSAjnMkxKrIQPlHZPjWhN943FpUU7ePz3BLz+TYsjE5ZICm0Z/PKh66miVQUEx4pqe2pmBS9bi0lLjUZWBRu4ehyGgxNEwFfHnZhh96ZBj4lIyWgzigC6iaquzB6KgoDbouKc4Fj45nuKZPa4t36O49kl8P7doRisK5FS/eEUKW6NoGvzWaAof6gq6HvfK65twc5vWvXASPUe2l2y4KgIqRfYFhb/RfentRhfUO2YtaKKUpPdmXFMH/SLBYfdKQL06UgaruplKktBeV0inFnbxDkefgqKrYnyC9K/KGnJtfM9R/aOrAiGTnvAP77RGpjs2RX1PC6/NylyDkW6gtZGbjXba6VZ128+ADdu66TcM2TLh7NC95dCnuTqRYb+yKFu261lFJkSefqdln5pdMJeiavF2LHtGK+DY09IaMCPlg+4LcfTtZvvKQFvjU7wN/4ul8I9pAqvbhbfotKXQ1FhZWqPqB67x5+gH8h7IeyDvgbwH8h7IeyDv352863fugbUH1h5Ye2DtgbUH1h5Y+7dn7Up0n39PvyceSTcYdcccBfHGH0YgQp7IG8hhjKUZi+xdV1aaSDv3SMHKp3/mMubjcSRmSwU5vtO0fbamIpa4i94mOfuZ8gXIN3/jVr4O0tGtWm40P8JHk+k7yECOaYze/lVXq49cYP380Q4hLjlz5u8xF52RQZU1hu8Pjj4AlpKWR64uMtj4yLK/3y8x0qdgq0qWm8GJBECbKInUx1x9od3jQdAWbRILQKYnPwAcJj19xLujnJP8Qv4E01TWw6LK2qHMRUsfhek4XYE/ZocJzp8jmYBdCl9jRWcEdGZ0sztRikru7HVVLDo1d//6I1TVf13xmc4=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get fund reversals"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/fund-reversals"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get fund reversals

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"processed","in":"query","schema":{"type":"boolean","default":false}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"merchantId":{"type":"string","format":"uuid"},"amountPaid":{"type":"number","format":"double"},"transactionReference":{"type":"string","nullable":true},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"serviceProviderSlug":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"customerBankCode":{"type":"string","nullable":true},"customerBankName":{"type":"string","nullable":true},"customerAccountName":{"type":"string","nullable":true},"customerAccountNumber":{"type":"string","nullable":true},"retryCount":{"type":"integer","format":"int32"},"fundReservalReference":{"type":"string","nullable":true},"processed":{"type":"boolean"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetFundReversalResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetFundReversalResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"merchantId":{"type":"string","format":"uuid"},"amountPaid":{"type":"number","format":"double"},"transactionReference":{"type":"string","nullable":true},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"serviceProviderSlug":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"customerBankCode":{"type":"string","nullable":true},"customerBankName":{"type":"string","nullable":true},"customerAccountName":{"type":"string","nullable":true},"customerAccountNumber":{"type":"string","nullable":true},"retryCount":{"type":"integer","format":"int32"},"fundReservalReference":{"type":"string","nullable":true},"processed":{"type":"boolean"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetFundReversalResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetFundReversalResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"merchantId":{"type":"string","format":"uuid"},"amountPaid":{"type":"number","format":"double"},"transactionReference":{"type":"string","nullable":true},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"serviceProviderSlug":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"customerBankCode":{"type":"string","nullable":true},"customerBankName":{"type":"string","nullable":true},"customerAccountName":{"type":"string","nullable":true},"customerAccountNumber":{"type":"string","nullable":true},"retryCount":{"type":"integer","format":"int32"},"fundReservalReference":{"type":"string","nullable":true},"processed":{"type":"boolean"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetFundReversalResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetFundReversalResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      