---
id: update-settlementsplitconfigaccount
title: "Update SettlementSplitConfigAccount settlementSplitAccountId"
description: "Update SettlementSplitConfigAccount settlementSplitAccountId"
sidebar_label: "Update SettlementSplitConfigAccount settlementSplitAccountId"
hide_title: true
hide_table_of_contents: true
api: eJzVVU1v2zAM/SsBT/tQm2xH37Ki6HIYUDTtLkUOjMUk6mRJlei0geH/PlB2nbRdAwzbpbnEkJ8eyUc+ugEfKCIb72YaCqiDRqY5MVuqyHEK1nDp3cqssSx97RgUMK4TFLewh80FdpZh0x62UBAwYkVMUdANOKwICkjPb/X4mQYFxkEBAXkDCiLd1yaShoJjTQpSuaEKoWiAdyHzcDRuDQpWPlbIknxtNLStGkJdnP+YT2+uvz9Rbwg1xb8gb9tFB6bE37zeCaL0jsmxPGII1pRZvfFd8k7OXlH55R2VIluIojUbShknxf9EW9MB1tXVMic41KR9vbQkVQFqbSQU2ssDphXaRArYsBWGmxcNPNT4qiskkzE98nvL+VDuT5/fVfJCFykF71KXzdfJRP40pTKaIFGggHldlpRSDv78TRdldMxxoyPGqog3XnfmKsVd2WQFjDGY8THScfMWawsKEsXtk7vraMVhzCEV4/H+1omm7emaqhRwl7ytpaDT0lcg1pIeXu3tdf6IVbD0sluTVvy78rlvvewXHeOBIqPp5QwUSEKdZtsv0CoIPnGFeVL6rfCPWj5rzDBI2U/BonESNIvR9CrfAgYD6vi6VFC8GXOhYOMTC1PTLDHRTbRtK8f3NcUdFLcLBVuMBmVypRnaJHnWw7S+SHrYYfDhql+FH0ey2f9UTH+Ibifydi0BUPCLdsfWebto1dPG/e85dbEP9vuQl6z/7u1ZR3hyLQR7xKulLYM4GORyen0mfMt+21dey6WID/LZwIeudJ+zzkbOZw1YdOsa14LtSOX3Gz/YnKY=
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update SettlementSplitConfigAccount settlementSplitAccountId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/SettlementSplitConfigAccount/{settlementSplitAccountId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update SettlementSplitConfigAccount settlementSplitAccountId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementSplitAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"}},"additionalProperties":false,"title":"UpdateSettlementSplitAccountRequest"}},"text/json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"}},"additionalProperties":false,"title":"UpdateSettlementSplitAccountRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"}},"additionalProperties":false,"title":"UpdateSettlementSplitAccountRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      