---
id: get-batchperiod-get
title: "Get Batch<PERSON>eriod Get batchPeriodId"
description: "Get Batch<PERSON>eriod Get batchPeriodId"
sidebar_label: "Get Batch<PERSON>eriod Get batchPeriodId"
hide_title: true
hide_table_of_contents: true
api: eJztV0tv2kAQ/itoTq20idP05hupIpJDWxSSE+IweAfYdL272R2jIsv/vVobjCGocdIeORkz79cnfyVYRx5ZWXMvIYUl8Q1ytnLklZUjYhDAuAyQTqEWjGsBzAQ49JgTk4/CEgzmBCnM90r3EgQoAyk45BUI8PRSKE8SUvYFCQjZinKEtATeuGgc2CuzBAEL63NkSKEolISqEq3/0e33yfDp8W7nekUoyb/DeVXNonJw1gQKUX59dRUfkkLmlYutgBQmRZZRCCAgs4bJcO2JfnPiNMbI5esIdv5MWeyY87GrrBr/Yetqrzi3VhMaqATkFAIu6VQTTKE1zjU1FVUCJHKPcIcTeLu5omvxzcqeudACC82ni1p4mz+qvJ8ntr1Vm628s4XvqCvDtKxXoC1MGf56HRPJPCGTHPJf+yCR6YJjEnEgVqqF+pjNzaZXGXv12xyV7mUTiFlTTobH3i6Upu4+ofe4iRfBlIe3NyQnn63Q8E0RlKEQfmDP/u8Mf5q5RS+VWZ7esFeGWLCdtBWcWpqoJKWK14d63El3gTqQAFas6/snnhy34mF7zXEUR6Hf4bYDb3uH/2a/e96hkZp84845rbIacZPnYM9IckaSM5KckeQDSFJ/jZwh5AwhZwg5Q8hHIKSq6tvp0p4R8aBjO4jvx5QuJ17ZLVmM44zkLoUEnUo6psmIOCkPbCuIq+PXO8pYeB0ZHLMLaZLsl+pC0vpySXlwuAlWFzG3y8zmEKmbMgtbz6wtttYb7AcxGI7vQUAM0xS1/hKH4WzgHGuw3HHJHsUetKfdlA4RrERTR7ltxBTQKRAHbFlAw6TTQ+8zASsbONqU5RwDPXldVfHvl4L8BtLpTMAavWp2aFqCVCH+lu3Ij9JruSp8etjy4c+DSOFPpb27VxOvdY26iG8g4BdtXhH5alaJHdf+74k0ATvMvk0mEv9GOswyctyRdV3MOks5un2EqvoDKDLm5g==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get BatchPeriod Get batchPeriodId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/BatchPeriod/Get/{batchPeriodId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get BatchPeriod Get batchPeriodId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"batchPeriodId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      