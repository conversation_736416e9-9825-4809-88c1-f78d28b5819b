---
id: get-batchperiod-get
title: "Get Batch<PERSON>eriod Get batchPeriodId"
description: "Get Batch<PERSON>eriod Get batchPeriodId"
sidebar_label: "Get Batch<PERSON>eriod Get batchPeriodId"
hide_title: true
hide_table_of_contents: true
api: eJztVz1v2zAQ/SvGTS3AVmm6aXOKwMnQ1oiTKfBwFs8yU4pkyJNRQ9B/LyjZsuwYjZJ29CTJvHu8zwe/Cqwjj6ysuZWQQk58hZytHHll5YQYBDDmAdJHaA6mzQHMBTj0WBCTj4cVGCwIUljsjW4lCFAGUnDIKxDg6blUniSk7EsSELIVFQhpBbxx0TmwVyYHAUvrC2RIoSyVhLoWHf7k+vts/HB/s4NeEUrybwCv63k0Ds6aQCGeX15cxIekkHnlYikghVmZZRQCCMisYTLcINFvTpzGeHP18ga7eKIsVsz5WFVWLX7YQu0NF9ZqQgO1gIJCwJxOFcGUWuNCU5tRLUAiD7jusAOvF1f0Pb5ZOTAWWmKp+XRSS2+Le1UMQ2I72LSdyhtb+p65Mkx5MwJdYsrw18sYSOYJmeSY/1oHiUyfOAYRG2KlWqr3+VxtBqWxN78uUOlBPoGYNRVkeOrtUmnqzxN6j5u4EUxFeH1CCvLZCg1flUEZCuEHDqz/zvGnWVj0Upn89IS9cMSS7azL4NTQRCMpVdw+1NNeuEvUgQSwYt3sP/HsuBR3222OrTi6+g2wPXrbA/6b/+55g0Zq8i2cc1plDeMmT8GemeTMJGcmOTPJO5ik+TdyppAzhZwp5Ewh76GQum52py97JsSjnu8ofh9LuoJ4ZbdiMbYzirsUEnQq6bkmE+KkOvCtIY6OX+8kY+l1VHDMLqRJklMRSpd7lPQ5vmfalvIzOgdRsSmztE2ruhyL4HAz2td/NJ7egoCI3uay/hJ74GzgAhuO3EnIATkeVKUbkJ7+q0UbfrXN/xHQKRAHIllAK6DTQ/S5gJUNHH2qaoGBHryu6/jzc0l+A+njXMAavWpH57ECqUJ8l12nj8LrJCp8uNvK4I+jqNxPhb1bUxOXdI26jF8g4BdtXuj3el6LncT+74G0F/YEfRdM1Pvt6TjLyHHvrA8x783i5Poe6voPXSTjGw==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get BatchPeriod Get batchPeriodId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/BatchPeriod/Get/{batchPeriodId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get BatchPeriod Get batchPeriodId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"batchPeriodId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      