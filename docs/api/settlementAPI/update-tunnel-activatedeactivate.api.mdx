---
id: update-tunnel-activatedeactivate
title: "Update tunnel settlementTunnelId activate deactivate"
description: "Update tunnel settlementTunnelId activate deactivate"
sidebar_label: "Update tunnel settlementTunnelId activate deactivate"
hide_title: true
hide_table_of_contents: true
api: eJztWE2P2zgM/SsBT/uhabp79C2dFu0cFhjMx2mQA2MxibqypEp02sDwf19QthPPJLtIigG2QJOLHYd6JJ+oF5MN+EAR2Xh3o6GAOmhkeqidIzsr2WyQSRP2d6CAcZWgeIJ7YrZUkePOGOYKAkasiCmKRQMOK4IC0gvLGw0KjIMCAvIaFET6UptIGgqONSlI5ZoqhKIB3oaMwNG4FShY+lghS5i10dC2aufk44e/7mePD58G6DWhpngGeNvOO2NK/M7rrViU3jE5llsMwZoy8zT9nLyTZwdQfvGZSgYFIQqrbCjltcIejewW3ltCJwkAam0EFe3taNESbSIFbNjKgmEn3u924iX9d13kGZLpG//wQY4J/e33HztaAY2Ugnepc//n27dy0ZTKaIL4ggLu67KklECN6ybvRbBozkov9VBH8lNQUUq4omPnw9XW4kLyk2JvFWjkE9wZfcJhU/1RO8XrmJcT7JOtVycZ/vu2KygjiVbN+D9zEXW7YlNRZtJrszTnrSnXKMUx3huMEbciPExVejW2e0cn8RIwsqN47fVpG2TSe1pibfk4lSbN/m+iTz3GH4mvO6L2x/euP6q5Zp+nfgbsoRwMqK8AMlw/odOW4oEinquHF8G4CMZFMH4ywfiu97yLUlyU4qIUP5VS5P7lea/ymNv8CeeVk8MOfTI0RZNnzX9FvPa669xLad1zB1/AFIOZdmDT5hCtnQ4YV8/gEsXNMCyoo5W2nTmkYjpdUZXqsIqo6Y3cl9bX+g2GANKmi+bd7Vv1D9+wCpbGfeFQjG7pc1nsaKtSwO1kz91kdnsDCiSMjpnNH7K3wSeuMEtMP2D4TsaO69WoK2xVl3rTc/kEGIzMWbqpioLi6PzkGKFzBWufWDCaZoGJHqNtW3n8paa4heJprmCD0XRV+9SANknu9a6+XoS7a2Thl7t+jvLrRII7lsYgWU4Ea4O2lm+g4G/aHp8CtfNWDYOaV4+m8zoaC+0ikqlR9+t1B3j1IAB7i4MX8f2KWVlS4JHt2Ol8dEBuZw/X4nXRj5KqrJ8Q8avMpPBrR43PuWXpzs8asOhWdf4Lhs61fP4BCUTnMQ==
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update tunnel settlementTunnelId activate deactivate"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/tunnel/{settlementTunnelId}/activate-deactivate"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update tunnel settlementTunnelId activate deactivate

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementTunnelId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"active":{"type":"boolean"}},"additionalProperties":false,"title":"ActivateDeactivateSettlementTunnelRequest"}},"text/json":{"schema":{"type":"object","properties":{"active":{"type":"boolean"}},"additionalProperties":false,"title":"ActivateDeactivateSettlementTunnelRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"active":{"type":"boolean"}},"additionalProperties":false,"title":"ActivateDeactivateSettlementTunnelRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      