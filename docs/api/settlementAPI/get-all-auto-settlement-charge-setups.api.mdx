---
id: get-all-auto-settlement-charge-setups
title: "Get all auto settlement charge setups"
description: "Get all auto settlement charge setups"
sidebar_label: "Get all auto settlement charge setups"
hide_title: true
hide_table_of_contents: true
api: eJztWMlu2zAQ/RVjTi2g1F1uurlFkAZoUyNuT4EPY3Ess6VIhhwGTQ39ezGSFzlREifIUSfb1LzZ+Wy/NThPAVk7e64gh5J4YswksZsRs6GKLH9ZYShpRpx8hAwYywj5FTxoBPMMPAasiCmI6RosVgQ5TLGki1QtKEAG2kIO14nCLWQQixVVCPka+NaLqbZMZWO3dKFCbo8+fYS6zg78zfQ/eh1vZ6ffZ5NfP79uva0IVYMJdJ10IAU5h0Q97iMHbUuo67kYR+9spCjPP75/Ly+KYhG0ly5DDrNUFBSllYWzTJYbT/SXx96gRF7fj+AWv6lgyMAHGRjr1n/cuNobLpwzhBbqDCqKEUu6n2cGNhmDC0NtRXUGCrkbDkNAaaRmquLTaWjVF2TX6pS0koSKZkc6pna7CztT5ZKk1RpbS+ao7ONuDafBLbWhp1OuKEgE/pyithTjRbMDR8TaAn/YhcOgtC3Pe6u/B8SD+9I3MjFSSsuaoJl20l2iiZQBazbNohLP7lZ8uVm7pnWBkElN+NGpKGQ6YV01kMopvdTPwTwj2T6m6OZ7r1V+zxPH3OEW0BDBceZLHSJPH7gb+7UNum+OBl+OZcdoBByPzLQBXFLhgjoWYunvi/PzgW60S/Fl+FdaiW+6bbDaHnxFqwyFdue8N7povrDGv6Mb2HJgy4EtB7Yc2LKXLZtflQNNDjQ50ORAkwNN9tNkLTEO/6KfEY/QmJFcr9GeCEYt2chJq4VUxCu30U2EDZBXkMMYvR4L9GQPPWmhJztopHCzlUdSMKI3MPuYj8clVTH5MqCid/K+MC6pd+g9iL6g7dI1XdzVXUWPt6N94aPJ9BwyEO9tNTcfmnV2kStsvgm2gseRVR70Zje/jmJRZ20J600HrgC9jPLxHswzWLnIYr5eLzDSr2DqWo5bHUc6o3SUXVC7cf+h27tC0g2aJAmBaDmPADZK0d58Lh+Cbpftal5nW8mnN/KdLuy0G3hzudGH3o5EHuvrTptER2A6yLl9OikK8tx51nUx7+za2elPqOv/t0HfRQ==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get all auto settlement charge setups"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/auto-settlement-charge-setups"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get all auto settlement charge setups

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      