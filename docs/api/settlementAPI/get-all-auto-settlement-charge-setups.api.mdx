---
id: get-all-auto-settlement-charge-setups
title: "Get all auto settlement charge setups"
description: "Get all auto settlement charge setups"
sidebar_label: "Get all auto settlement charge setups"
hide_title: true
hide_table_of_contents: true
api: eJztWE1P20AQ/SvRnFrJEEpvvqUVAqSWRqScUA4T78RZut5ddsdRaeT/Xo2dDwcMBMTRpyjreTOzb2efk7cC5ykga2cvFaSQE4+MGZXsJsRsqCDL3xcYcpoQlz5CAox5hPQWng2CaQIeAxbEFCR0BRYLghTGmNNVWcwoQALaQgr3JYUHSCBmCyoQ0hXwg5dQbZnyOm7uQoHcLH09hapK9vJN9D/6mGznZz8no5vfF5tsC0JVYwLdlzqQgpRDSR3pIwdtc6iqqQRH72ykKM9PT07kQ1HMgvbCMqQwKbOMolCZOctkuc5Ef3noDUrl1dMKbnZHGUMCPsiBsW7yx3WqXeDMOUNooUqgoBgxp6d9JmBLY3BmqNlRlYBCbpfDEFCI1ExFfL0NrbqKbKkuS62koayekVao3czCNlS5Utpqgq0lc1D3cTuG4+Dm2tDrLRcUpAJ/K6O2FONVPQMH1NoAf9mZw6C0zS87d/8EiHv3pevIJEgpLWOCZtxqd44mUgKs2dSDSjx5vOPr9djV1AVCJjXiF09FIdMR66KGFE7puX4L5g3NdilFu98nVPmdThxyhxtALQSHhc91iDx+5m7sxjbornM0+H4sO0Yj4HhgpzXgmjIX1KEQS3/f3Z8PtNSujO/Df9BI/NANwWqzcIFWGQrNzHlvdFa/sIZ30fVq2atlr5a9WvZq2amW9a/KXiZ7mexlspfJXia7ZbKSGvt/0c+JB2jMQK7XYCcEg0ZsZKXxQgrihVv7JqIGyAtIYYheDwV6tIMeNdCjLTRSWG7skTIY8RuYfUyHwxZI0fI4pyJ6fIjOlNLdceYKEKNB27mr6dwSUMcNdgwMRuNLSEDKNNtafqnn2kUusH4lbJyPA7e7R9L2IFvWRZU0e1mtqbgF9HKmL5MxTWDhIkv4ajXDSDfBVJUsN4aOUKR0lKFQ23P/Qw+PHaUlmlIaAjF1XgCsLaNd+FS+BN1M3e20SjbeT2flRyxsTRz4dL02ij4PxCfrYqdpouU07fXcPB1lGXluPWunmLaG7vzsN1TVfzhR4xA=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get all auto settlement charge setups"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/auto-settlement-charge-setups"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get all auto settlement charge setups

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      