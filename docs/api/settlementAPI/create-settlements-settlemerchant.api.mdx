---
id: create-settlements-settlemerchant
title: "Create settlements settle merchant merchantId"
description: "Create settlements settle merchant merchantId"
sidebar_label: "Create settlements settle merchant merchantId"
hide_title: true
hide_table_of_contents: true
api: eJztWUtz4jgQ/itUn/ZhB0ISMuNbHrOzOWSXCswelsqhsdtBM7blkeRMKMr/fUuSXxgzZVJ7NBcUqZ+S+vtIawc8JYGK8eQhAA98QahoQUpFFFOiZDkU/gYTBQ4ofJHgraCWgWcHUhQYkyKh13aQYEzgQan2EIADLAEPUlQbcEDQ94wJCsBTIiMHpL+hGMHbgdqmWlMqwZIXcCDkIkYFHmQZCyDPncr450+Pi5svyz9L0xvCgMQJxvP82QqTVLc82GoJnydKp+TtANM0Yr7ZmvFXyRM9d2CKr7+Sr7clFXojFSOpV6vE73vk5FTif5nMDhWSLIpwHZHNKHcAY54lao4saIgnWbw2G1DZD3imtSqFJben1lNJCUwk+uXl6JFIQ+OJQhKU+P0Saijek/QFS/XwVNW7DYqXvtnRW0q+omD5Tv0Dv/9glL1b+Q7TnqpM/hGhOgh1zXlEmLSM36PqPIDaLipyFYuNaUnilfk0F/yVBSR6X8eW3h0P+unpS59Q1EtWnHSfJM+ET7eYfOsdTK3SP2+jcuP7urjeqWVPut8ul4j7WORESRZrLH7ENLVKizRiyqTswD2FmEUanBbZunAHDtyi8jcas9sOFTPQ0EB242fP81zwkEVUu+kVuJY+DQ/WOsjTVJi0mXWWBGaKNxirQ0YLBQHTRYPRvIHmIUaS2rvzWCD2k6UPo67oTQ1MMTDFwBQDUwxMMTDFcaZo/mvx2+8DZQyUMVDGQBkDZQyUcYQycnNHZcoTaTlgOpnor6AJZ7DIfJ+kNO72V+5MW29Ub7UsxqOSHUZ7vbqY1IbrfmDKpSEg3bbzYIwpGzeMFGO31B3vais52NItW4KZiHSTTqlUeuOGETeg17MXimWKW8mjTEd85vMYdHdOU+NT3aH79IZxahmmSYJwEeKHq3B26V5dn1+7l1ezqbu+CH136n+cXYSzGYY4gzYT1qfdJLzJIZ1NDsiqr8NuxmoUxRFi6pQoQXnyU3aZHOeOriXDDJM27tu+6QHSw3QyvXQn1+704/L8yrs696YfzibX5//CEXSv8+gE8Xq5wup6SnRtWBt5u1YOXB/i6LHFAi6bYe+jYgMMfwZbDQMtdKpX2iBUr1RYY4+hjS4lIiUhN1BTQMdnW0KjWnR0M38AB3QF2lv1eq7RSpd0jObnT9FJPxUdgs4fUab9kUbIDCaact8VwLECTNnensnqL7fxtuA13Dw7sNHg461gt1ujpC8iynM9/T0jsQVv9ezAKwpmcXq1g4BJPQ4qVG3FWfX24Zen4ong1xE43fEXk5hs9RbaAgJw4Btt9x828ufcKd8e/vcorLfGS0cViX4Isat31qC71AZqiYPnC42nFa7P/14s9SUsnj1ie20F/tCVhz9srtwEbfjGzO0gwuQlQw0RYG3qz3+TcTRV
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create settlements settle merchant merchantId"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/settlements/settle-merchant/{merchantId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create settlements settle merchant merchantId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"merchantID":{"type":"string","format":"uuid"},"merchantName":{"type":"string","nullable":true},"amountPaid":{"type":"number","format":"double"},"amountToSettle":{"type":"number","format":"double"},"transactionId":{"type":"string","format":"uuid"},"transactionReference":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionCharge":{"type":"number","format":"double"},"expectedTransactionCharge":{"type":"number","format":"double"},"transactionChargeValue":{"type":"number","format":"double"},"transactionChargeCap":{"type":"number","format":"double"},"isFlatCharge":{"type":"boolean"},"transactionDate":{"type":"string","format":"date-time"},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"settlementMode":{"enum":["Mapping","SplitCode","Default","SubAccount","Batch"],"type":"string","title":"SettlementMode"},"settlementProfileSplitCode":{"type":"string","nullable":true},"splitReference":{"type":"string","nullable":true},"batchReference":{"type":"string","nullable":true},"isBatch":{"type":"boolean"},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"SettleMerchantRequest"}},"text/json":{"schema":{"type":"object","properties":{"merchantID":{"type":"string","format":"uuid"},"merchantName":{"type":"string","nullable":true},"amountPaid":{"type":"number","format":"double"},"amountToSettle":{"type":"number","format":"double"},"transactionId":{"type":"string","format":"uuid"},"transactionReference":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionCharge":{"type":"number","format":"double"},"expectedTransactionCharge":{"type":"number","format":"double"},"transactionChargeValue":{"type":"number","format":"double"},"transactionChargeCap":{"type":"number","format":"double"},"isFlatCharge":{"type":"boolean"},"transactionDate":{"type":"string","format":"date-time"},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"settlementMode":{"enum":["Mapping","SplitCode","Default","SubAccount","Batch"],"type":"string","title":"SettlementMode"},"settlementProfileSplitCode":{"type":"string","nullable":true},"splitReference":{"type":"string","nullable":true},"batchReference":{"type":"string","nullable":true},"isBatch":{"type":"boolean"},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"SettleMerchantRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"merchantID":{"type":"string","format":"uuid"},"merchantName":{"type":"string","nullable":true},"amountPaid":{"type":"number","format":"double"},"amountToSettle":{"type":"number","format":"double"},"transactionId":{"type":"string","format":"uuid"},"transactionReference":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionCharge":{"type":"number","format":"double"},"expectedTransactionCharge":{"type":"number","format":"double"},"transactionChargeValue":{"type":"number","format":"double"},"transactionChargeCap":{"type":"number","format":"double"},"isFlatCharge":{"type":"boolean"},"transactionDate":{"type":"string","format":"date-time"},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"settlementMode":{"enum":["Mapping","SplitCode","Default","SubAccount","Batch"],"type":"string","title":"SettlementMode"},"settlementProfileSplitCode":{"type":"string","nullable":true},"splitReference":{"type":"string","nullable":true},"batchReference":{"type":"string","nullable":true},"isBatch":{"type":"boolean"},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"SettleMerchantRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      