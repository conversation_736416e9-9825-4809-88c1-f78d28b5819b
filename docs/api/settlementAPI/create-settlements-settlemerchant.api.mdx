---
id: create-settlements-settlemerchant
title: "Create settlements settle merchant merchantId"
description: "Create settlements settle merchant merchantId"
sidebar_label: "Create settlements settle merchant merchantId"
hide_title: true
hide_table_of_contents: true
api: eJztWUtz4jgQ/itUn/ZhAiEJmfEtIbOzOWSXCsweNsWhsdugGdvSSHImFOX/viX5iTFTJrVHc0GR+utW6/F9pLUHLkiiZjx+9MEFTxJqWpDWIUUUa1U0pbfFWIMDGjcK3BeobGDlgECJEWmSZmwPMUYELhSwRx8cYDG4IFBvwQFJ3xMmyQdXy4QcUN6WIgR3D3onDFJpyeINOBBwGaEGF5KE+ZCmTun886enxd2X5Z+F6y2hT/IM52m6yoxJ6Xvu74yFx2NtUnL3gEKEzLNLM/qqeGz6jlzx9VfyzLIIaRZSM1JmtEz8oUNOTmn+l83sGBAnYYjrkLKMUgcw4kms58j8mnmcRGu7AKV/nycGVQKWPNu1jiAtMVboFYejQyI1xDMFJCn2uiVUAz6Q8iQTpnkudLZFuemaHb0J8jT5y3fij+L+g2HybvAMRUcoU3+EqI+muuY8JIwbzh9Qt25A5Rc1DTWLrGtF8pV5NJf8lfkkOx/HBm7G/W44c+hjCjvZyrPOk+KJ9Oge42+dJ1NBuudtIXeeZy7XO1HZTndb5YJxn/KcKE4iw8VPKEQGWoiQaZuyAw8UYBIaclok6zwcOHCP2tsazm4G1MxSQ43ZbZyDyHPJAxZSFabTxI31eXywNpM8D8JUllnrlcBE85pitdgYI99n5tJgOK+xeYChoubqPOWM/ZzJh4VretO9UvRK0StFrxS9UvRKcVop6v9a/PZ7Lxm9ZPSS0UtGLxm9ZJyQjNSeUSV4rDINmIzH5suv0xksEs8jpWy4w5GZLesNqqVWeXtQqMPgoFYXkd5yUw8UXFkBMmU7F0Yo2KjmJG8PC+xoX3lJIbu6RUkwkaEp0mktlDsabShSidhI9OnCtL2QJ/4FCgGmKGcU8bkqzH16w0hkwlLXPrgK8MNNML0e3txe3g6vb6aT4foq8IYT7+P0KphOMcApNAWw2uS6zo2PVWx8pFFdA7YLVe0unNCjVouCi8c/FZXxacloG7KCMG7SfVYuPSJ4mIwn18Px7XDycXl5495cupMPF+Pby3/hBKlXebRydzVcUnTVJdsWrEm4bSNHoY/p89RgzpL1aR+SYY0Df8ZWNQcNUqpGmtxTjZQUk21Dk1QKIooDbhkmZ4zPFCmBu0FlOribP4ID5uJlp+r10pCUuckR2l89eQH9XFLwW3872aqHCJFZKrS3fJ/zxQugYAdrpsq/hrUnBbcWZuXA1nCO+wL7/RoVfZFhmpru7wnJHbgvKwdeUbKMnl/24DNl2n5Jpo15liV9+OU5fxn4dQBO+/zzTox3ZgmzCwTgwDfaHb5npKvUKZ4c/vdZZNFqDxzlTMz7RzY6yxwOl8ZBZXH0amH4tKTz+d+LpTmE+WtHlB1biT/MzcMfWa7cTtrKjO3bQ4jxJkFDEZD5NJ//AKefMIo=
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create settlements settle merchant merchantId"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/settlements/settle-merchant/{merchantId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create settlements settle merchant merchantId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"merchantID":{"type":"string","format":"uuid"},"merchantName":{"type":"string","nullable":true},"amountPaid":{"type":"number","format":"double"},"amountToSettle":{"type":"number","format":"double"},"transactionId":{"type":"string","format":"uuid"},"transactionReference":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionCharge":{"type":"number","format":"double"},"expectedTransactionCharge":{"type":"number","format":"double"},"transactionChargeValue":{"type":"number","format":"double"},"transactionChargeCap":{"type":"number","format":"double"},"isFlatCharge":{"type":"boolean"},"transactionDate":{"type":"string","format":"date-time"},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"settlementMode":{"enum":["Mapping","SplitCode","Default","SubAccount","Batch"],"type":"string","title":"SettlementMode"},"settlementProfileSplitCode":{"type":"string","nullable":true},"splitReference":{"type":"string","nullable":true},"batchReference":{"type":"string","nullable":true},"isBatch":{"type":"boolean"},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"SettleMerchantRequest"}},"text/json":{"schema":{"type":"object","properties":{"merchantID":{"type":"string","format":"uuid"},"merchantName":{"type":"string","nullable":true},"amountPaid":{"type":"number","format":"double"},"amountToSettle":{"type":"number","format":"double"},"transactionId":{"type":"string","format":"uuid"},"transactionReference":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionCharge":{"type":"number","format":"double"},"expectedTransactionCharge":{"type":"number","format":"double"},"transactionChargeValue":{"type":"number","format":"double"},"transactionChargeCap":{"type":"number","format":"double"},"isFlatCharge":{"type":"boolean"},"transactionDate":{"type":"string","format":"date-time"},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"settlementMode":{"enum":["Mapping","SplitCode","Default","SubAccount","Batch"],"type":"string","title":"SettlementMode"},"settlementProfileSplitCode":{"type":"string","nullable":true},"splitReference":{"type":"string","nullable":true},"batchReference":{"type":"string","nullable":true},"isBatch":{"type":"boolean"},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"SettleMerchantRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"merchantID":{"type":"string","format":"uuid"},"merchantName":{"type":"string","nullable":true},"amountPaid":{"type":"number","format":"double"},"amountToSettle":{"type":"number","format":"double"},"transactionId":{"type":"string","format":"uuid"},"transactionReference":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionCharge":{"type":"number","format":"double"},"expectedTransactionCharge":{"type":"number","format":"double"},"transactionChargeValue":{"type":"number","format":"double"},"transactionChargeCap":{"type":"number","format":"double"},"isFlatCharge":{"type":"boolean"},"transactionDate":{"type":"string","format":"date-time"},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"settlementMode":{"enum":["Mapping","SplitCode","Default","SubAccount","Batch"],"type":"string","title":"SettlementMode"},"settlementProfileSplitCode":{"type":"string","nullable":true},"splitReference":{"type":"string","nullable":true},"batchReference":{"type":"string","nullable":true},"isBatch":{"type":"boolean"},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"SettleMerchantRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      