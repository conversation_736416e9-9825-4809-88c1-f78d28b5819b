---
id: delete-settlementsplitconfig-splitaccount
title: "Delete settlement split config settlementSplitConfigId SplitAccount splitAccountId"
description: "Delete settlement split config settlementSplitConfigId SplitAccount splitAccountId"
sidebar_label: "Delete settlement split config settlementSplitConfigId SplitAccount splitAccountId"
hide_title: true
hide_table_of_contents: true
api: eJy9lMFu2zAMhl/F+E8boNbdjroFa7AF2IBhaU9BDqrNJMJsSZXoYIGhdx9oJ03SNgMGrDtZlij+5EdSPXygaNh6N6uhUVNDTHNibqglxyk0livvVnY9l6WpKt85hgKbdYJe4Gg7GHzyNWGpEEw0LTFFMerhTEvQSM+NxfGshoJ10AiGN1CI9NjZSDU0x44UUrWh1kD34F0Y3HC0bg2FlY+tYWh0na2Rszoqif/JGO2bCHyefptP7u++HFxvyNQU/8J5zksxTsG7REnOP97cyKemVEUbpCbQmHdVRSmJ9rOT26FWxZFpMSRdjOUqLrAu5idkiheYWuKNP3YC1MhMozTBlkefV8PNq1Gr7C+I5fJUrezP5TIUEsXtoUm62AhI5pB0eapV0/Z6TW0KZpd800n+15VvIQStW/kBruVmqMtoVxz7sph8n0FBZEZw2w/ICsEnbo2Tu/uSvgnQs5o99QDTLy5DY6yTUIbE+z3pBUywA5lXWUNBXx6j01gGy/Nwlgobn1hE+v7BJLqPTc6y/dhR3EEvlgpbE615EJiLHrVNsq6hV6ZJ9CKfyjsmJxPy7se+8d8X8jy8lud+07id1MM0nfxB4Sft/vA4yND9zzDOkeVlVofh/udARsWTp+QpGmntp1m8nX6d3k2R828DZv0g
sidebar_class_name: "delete api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete settlement split config settlementSplitConfigId SplitAccount splitAccountId"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/settlement-split-config/{settlementSplitConfigId}/SplitAccount/{splitAccountId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete settlement split config settlementSplitConfigId SplitAccount splitAccountId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementSplitConfigId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"splitAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      