---
id: get-chargerevenues
title: "Get charge revenues"
description: "Get charge revenues"
sidebar_label: "Get charge revenues"
hide_title: true
hide_table_of_contents: true
api: eJztWMuO2koQ/RVUq0TyhDx23pFRNDNSMhoBWY1YFO7CdGJ3d7qrUbjI/35VfoBhSMJMsvQK4a7HqUcfw9mBdeSRtTV3ClLIia/X6HPytCETKUACjHmA9BGag2lzAIsEHHosicnL8Q4MlgQpPGBO97FckocEtIEUfkTyW0ggZGsqEdId8NaJqTZMeW23sr5Ebh59eA9VlRzFm+n/6N9E0+Gzzf8YamltQWggAUUrjAVDusIiUD/Szacvs8nX+W0XbE2o6uyefkTtSUHKPtKZ6IG9NjlU1UKMg7MmUJDz92/fyoeikHntZCiQwixmGQUZRGYNk+E6Ev3ksStQMu+eZrDLb5QxJOC8zJd1Ez+0oZ4WWiVQUgiY01OcCZhYFLgsqKmoSkAh99Oh9yh91Exl+DMMrc4l2Q8tRq0EEJY2ttU2pqbbqr2pslFgVQkEYi6oJMMzG31GU1qRJ5NdVs+p99yjCZjJBK5PQPx6y/ph5rXxDsjEUu7O3f1sPrmfQwIfJ/PrW7k9p6BYsyCC2VGQcIir7i7rm/NWpkzq/JzbY23yKbHfPqe+zBMyqQn/FodCpivWZT2X0iq90s/xkckrpaX3WDz0Fqe+gYc+3XRU1TLStL1JkvXJgN2BlC4r1XWsc5n5SvvAD7+4PocJeX1u+wp8uS9bxkKcw4VIa4cpZdarS10M/XwxPudpo20ML/P/2134rJvOqu7BLRpVkG+2zLlCZ/W7b/wt2IFJByYdmHRg0oFJn82k9a/RgUIHCh0odKDQgUKfT6GVBD/+239DPMrqMKOeFlMSr20r1AiVIa8hhTE6PW6Mr3rGgfymE2eiL0SjYHYhHY8P3HClaPMmpzI43AZbRMn+JrMliDihzcrWfdpXVtuNDqwwmjzcQQKSpoG9eVcvrA1cYv066NSSs+UclbyfR0/cqJIG+a4t9RHQyWhOi10ksLaBxWC3W2Kgr76oKnncaDzSAqWDTFPtB/adtqd61QaLKBBAhJ7fOLSC1CXmneJ0sF3IF6+b1XpcVEmnHZ1FedKjvQgEr6at0PR6BMn53jUIekrVEeDmdJJl5Lh31g+x6K3czac5VNX/9Cr0GA==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get charge revenues"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/charge-revenues"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get charge revenues

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"isLog","in":"query","schema":{"type":"boolean","default":false}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"amount":{"type":"number","format":"double"},"settlementSourceReference":{"type":"string","nullable":true},"settlementSourceTransactionCount":{"type":"integer","format":"int32"},"settlementType":{"enum":["INSTANT","BATCH"],"type":"string","title":"SettlementTypes"},"settledId":{"type":"string","format":"uuid"},"processed":{"type":"boolean"},"processingRetryCount":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChargeRevenueResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetChargeRevenueResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"amount":{"type":"number","format":"double"},"settlementSourceReference":{"type":"string","nullable":true},"settlementSourceTransactionCount":{"type":"integer","format":"int32"},"settlementType":{"enum":["INSTANT","BATCH"],"type":"string","title":"SettlementTypes"},"settledId":{"type":"string","format":"uuid"},"processed":{"type":"boolean"},"processingRetryCount":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChargeRevenueResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetChargeRevenueResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"amount":{"type":"number","format":"double"},"settlementSourceReference":{"type":"string","nullable":true},"settlementSourceTransactionCount":{"type":"integer","format":"int32"},"settlementType":{"enum":["INSTANT","BATCH"],"type":"string","title":"SettlementTypes"},"settledId":{"type":"string","format":"uuid"},"processed":{"type":"boolean"},"processingRetryCount":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChargeRevenueResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetChargeRevenueResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      