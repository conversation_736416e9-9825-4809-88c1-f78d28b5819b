---
id: get-chargerevenues
title: "Get charge revenues"
description: "Get charge revenues"
sidebar_label: "Get charge revenues"
hide_title: true
hide_table_of_contents: true
api: eJztWE2P2jAQ/StoTq2Uln7ccqOraneldrUCelpxGOIhuE1s1x6jUpT/Xk0+ILC0Zbc95gTE4+c3H37A24F15JG1NbcKUsiJr9boc/K0IRMpQAKMeYD0AZqFabMAiwQceiyJycvyDgyWBCncY053sVyShwS0gRS+R/JbSCBkayoR0h3w1kmoNkx5HbeyvkRuHr1/B1WVHOHN9E/6P2g6fLL5X6GW1haEBhJQtMJYMKQrLAL1ka4/fp5NvsxvOrA1oapP9/Q9ak8KUvaRzqAH9trkUFULCQ7OmkBB1t+9eSMvikLmtZOmQAqzmGUUpBGZNUyGayT6wWNXoJy8e3yCXX6ljCEB56W/rBv80EI9TrRKoKQQMKfHPBMwsShwWVCTUZWAQu4fh96j1FEzleHvNLQ6d8i+aTFqJYSwtLHNtgk13VTtQ5WNQqtKIBBzQSUZntnoM5rSijyZ7LJ8TnfPPZqAmXTg6oTE76esDzOvg3dAJpZyd27vZvPJ3RwS+DCZX93I7TklxZqFEcyOQMIBV91eVjfnrXSZ1Pk+t8va5FNiv31KfpknZFIT/iMPhUyvWJd1X0qr9Eo/ZY90Xikttcfivjc49Q081Om6k6pWkabtTZJTHzXYHUTpslRdpzqXha+0D3z/m+tz6JDX56avwOfvZctYyOZwIdN6w5Qy69WlWwz9eDY/52mjbQzP2/+vs/BJN5VV3YMbNKog30yZc4XO6u++8ddgByUdlHRQ0kFJByV9spLWv0YHCR0kdJDQQUIHCX26hFYCfvy3/5p4lNUwo54XUxKvbWvUiJQhryGFMTo9boJf9YID+U1nzkRfiEfB7EI6HudUhuhyj4pey/ussFG9RudAPAltVrYuzz6hMjjcjg5iMJrc30ICgt6w3byt59QGLrH+FuhMkrNZHGW6b0PP06iShvCuzfAB0ElHTnNcJLC2gSVgt1tioC++qCp53Fg7krnSQZqo9n36RttTm2qDRRQKIP7OHza0PtQl4Z3RdIhdyAevm4l6WFRJZxmdZXlSo733Ay+mrb/0cgTJ+do1DHoG1RHhZnWSZeS4t9aHWPQm7frjHKrqF7xW8E0=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get charge revenues"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/charge-revenues"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get charge revenues

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"isLog","in":"query","schema":{"type":"boolean","default":false}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"amount":{"type":"number","format":"double"},"settlementSourceReference":{"type":"string","nullable":true},"settlementSourceTransactionCount":{"type":"integer","format":"int32"},"settlementType":{"enum":["INSTANT","BATCH"],"type":"string","title":"SettlementTypes"},"settledId":{"type":"string","format":"uuid"},"processed":{"type":"boolean"},"processingRetryCount":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChargeRevenueResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetChargeRevenueResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"amount":{"type":"number","format":"double"},"settlementSourceReference":{"type":"string","nullable":true},"settlementSourceTransactionCount":{"type":"integer","format":"int32"},"settlementType":{"enum":["INSTANT","BATCH"],"type":"string","title":"SettlementTypes"},"settledId":{"type":"string","format":"uuid"},"processed":{"type":"boolean"},"processingRetryCount":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChargeRevenueResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetChargeRevenueResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"amount":{"type":"number","format":"double"},"settlementSourceReference":{"type":"string","nullable":true},"settlementSourceTransactionCount":{"type":"integer","format":"int32"},"settlementType":{"enum":["INSTANT","BATCH"],"type":"string","title":"SettlementTypes"},"settledId":{"type":"string","format":"uuid"},"processed":{"type":"boolean"},"processingRetryCount":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChargeRevenueResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetChargeRevenueResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      