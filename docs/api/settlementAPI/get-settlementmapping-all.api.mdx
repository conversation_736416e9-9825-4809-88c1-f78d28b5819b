---
id: get-settlementmapping-all
title: "Get SettlementMapping all"
description: "Get SettlementMapping all"
sidebar_label: "Get SettlementMapping all"
hide_title: true
hide_table_of_contents: true
api: eJytUz1v2zAQ/SvGTQ3ARmm6afMQuBkSBHUyGRrO0lkiyq+QJ6OuwP9enGQ7sWNkyiSQ9+7uvcenAXygiKy9u2+ghJZ4ScyGLDm2GIJ27dwYUMDYJihX8FZ+mMpQKQgY0RJTFMgADi1BCU/Y0mNv1xRBgXZQwmtPcQcKUt2RRSgH4F0QqHZM7Yjb+GiRp6uft5CzOpm31P/oa6Yt7h6W85fnX4dpHWEz9kR67XWkBkqOPV0YnziK7pwrAafgXaIk9dubG/k0lOqog5gKJSz7uqaUZPdZZUE8+2DnDEe7LXHn9y8CYjB3UEKBQRcfWoqpJVHcHl6gj0YkMYdUFkU6dnxvaHvdkk0Bd8mbXphc196CaNFu40eZms3o0IR7x3E2f7oHBbJmkrD9AVlB8IktOuk9mPuJtBMTjqYy/eUiGNROJo78h73sFWDQoC5ET4GMrBR0PrEAh2GNiV6iyVmup4SIIY1OuDbyqBs0iRT8od15RLdoeqECkpJPGvYZfINXcoha8FCuqqwOYbq4+Ux/7R2Tk4x++71P3tVMfrhLvkwk3kX3hMQxNIu7Z8j5P38ITvQ=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementMapping all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementMapping/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementMapping all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      