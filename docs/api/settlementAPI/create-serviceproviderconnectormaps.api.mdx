---
id: create-serviceproviderconnectormaps
title: "Create service provider connector maps"
description: "Create service provider connector maps"
sidebar_label: "Create service provider connector maps"
hide_title: true
hide_table_of_contents: true
api: eJztV01z2jAQ/SvMnvohl+aLtL7RTKbtISkTkhPDYbHWoNaWVEkmYRj/987aBhzStCTT5gQXe+TdlfbtW7FvCcaSw6CM/iohhsQRBhqSm6uErDNzJcklRmtKgnE5Wg8CAk49xCNozAaN2aWyZyvLC7QwFmDRYU6BHNsvQWNOEMPn84th/+b6CwhQGmKYEUpyIMDRz0I5khAHV5AAn8woR4iXEBaWPX1wSk+hLMe1MfnwycgFWyRGB9KBX9HaTCVVUt3v3mheexDKTL5TEkCAdQxBUOQru/s5MSjbuwtIGYoAMRSFklAK0K3U2YF0kTNCWk08I3bLe4/Fg0BBhYwX2tB5KEsBKKXiBDAbtM6XYuZp49aX8sbKTcEeqcRVjVQVN9Bd2IOyBUqbMG/e7tFpo8ORHXlrtK/TPXz/nh+SfOKU5Q0hhmGRJFSdu9WHFddshuppcDahNoYTYzJCzajl5D1O6XcI6yLLcMJJ8uVRCpAYdthO7Vau5xV5y+uyuv92OPo/Jodo7nXZD388NvMiCiqnCmojVaqe4rMrBz9T+Bv7ar79h5Cr5xfUMiP3oP2f3Px7tu7Z+qJsfd4/+J6me5q+IE2rseH+iHBWodVpqtdZiYvOWl10GnmRU5gZFiPW+IpaGGYQQxet6jbe0co7WntHjTdbrPRG4TKWFyFYH3e7U8p9YacOJb3j9yQzhXyH1gLLCe6oq42kOL/D3Gb0yHwHRyl+OEl7x9HJ6cFpdHzSO4wmR2kSHSYfe0dpr4cp9mCbcg3TSpY9qakIsEY69xYXnSGFkFFOOnT6g68ggFOp4ZsfsCNDkmPV+o2Y2hnWe9VYc681o5WiBmzZID4CtArW/fYo5mMBM65UPILlcoKeblxWlrz8syC3gHg0FjBHp+puHI1LsZJ8XCSpPH+Qa/ptHXQ9UMKrq0Yfvu6A+H0CP2hxX2DOMSvYDEqxbL6e1QGjaw6wsXgwBWw8+klCNrRs25uOW4wdfBteg4BJo0lzI9na4S2LW7zlcwgwVWrVPVmtLSFDPS2qGxjqnfn3C2aOf/U=
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create service provider connector maps"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/service-provider-connector-maps"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create service provider connector maps

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"serviceProviderId":{"type":"string","format":"uuid"},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"}},"additionalProperties":false,"title":"AddUpdateServiceProviderNipConnectorMapRequest"}},"text/json":{"schema":{"type":"object","properties":{"serviceProviderId":{"type":"string","format":"uuid"},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"}},"additionalProperties":false,"title":"AddUpdateServiceProviderNipConnectorMapRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"serviceProviderId":{"type":"string","format":"uuid"},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"}},"additionalProperties":false,"title":"AddUpdateServiceProviderNipConnectorMapRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"serviceProviderId":{"type":"string","format":"uuid"},"serviceProviderName":{"type":"string","nullable":true},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponse"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"serviceProviderId":{"type":"string","format":"uuid"},"serviceProviderName":{"type":"string","nullable":true},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponse"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"serviceProviderId":{"type":"string","format":"uuid"},"serviceProviderName":{"type":"string","nullable":true},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponse"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      