---
id: create-serviceproviderconnectormaps
title: "Create service provider connector maps"
description: "Create service provider connector maps"
sidebar_label: "Create service provider connector maps"
hide_title: true
hide_table_of_contents: true
api: eJztV01z0zAQ/SuZPfEhE2hpCr6FDgMcgE7TnjI5bKx1IrAlIckpmYz/O7Oyk7gphbQDPSUXZ+Td1e7bJ3nfCowlh0EZ/UlCCpkjDDQit1AZWWcWSpLLjNaUBeNKtB4EBJx5SMfQmp23Zl+UPVtbfkYLEwEWHZYUyLH9CjSWBCl8eP95NLy6/AgClIYU5oSSHAhw9KNSjiSkwVUkwGdzKhHSFYSlZU8fnNIzqOtJY0w+vDNyyRaZ0YF04L9obaGyWFT/mzea126FMtNvlAUQYB1DEBT5aHezJgZld3cBOUMRIIWqUhJqAbpTOjuQrkpGSKupZ8Suee+JuBUoqFDwQhc6D3UtAKVUXAAW5538ciw8bd2GUl5ZuW3YHZ24aJCKcQP9DAdQdkDpEubZ8wM6XXQ4siNvjfZNuUcvX/JDks+csrwhpDCqsoxi3p1zGLlmC1T3g7MNtTWcGlMQakatJO9xRr9DWFdFgVMuki+PWoDEsMd2ar92PazJO15f4v23R+r/mByivdflMPwxbeZFElRJEWojVa7u47MvBz9Q+Bv7Gr79h5Dr50fUsiB36/jf+/Af2Hpg66Oy9WFf8ANNDzR9RJrGseHmiHAW0eq13eutxUVvoy56rbwoKcwNixFrfKQWhjmk0Eer+q13svZONt5J680Wa71RuYLlRQjWp/2+pxAKKkmHRNLixYxKb3HpTVFxhi8yUwLrCj5aF1tt8f4nlragOwY9OM7xzUk+eJ2cnL46TV6fDI6S6XGeJUfZ28FxPhhgjgPY5V5LuZr1T24iEzaQx6R6o02uveH5JxDANTU4Ll6xI2NTYrwDWlW1N7432rIhYWdYq0WD3KqFfgxoFWwO3p3gTwTMuWXpGFarKXq6ckVd8/KPitwS0vFEwAKdao7leFKLtfbjbknl+YXc8HAn0c1kCU8uWqH4tAfi9wV8p+VNpbnAomIzqMWqfXvWBEwuOcDW4tY4sPUYZhnZ0LHtbjrpUPf86+gSBExbcVoaydYOr1nl4jXnIcDE0uKFGddWUKCeVfEqhmZn/v0C9h6DwA==
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create service provider connector maps"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/service-provider-connector-maps"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create service provider connector maps

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"serviceProviderId":{"type":"string","format":"uuid"},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"}},"additionalProperties":false,"title":"AddUpdateServiceProviderNipConnectorMapRequest"}},"text/json":{"schema":{"type":"object","properties":{"serviceProviderId":{"type":"string","format":"uuid"},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"}},"additionalProperties":false,"title":"AddUpdateServiceProviderNipConnectorMapRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"serviceProviderId":{"type":"string","format":"uuid"},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"}},"additionalProperties":false,"title":"AddUpdateServiceProviderNipConnectorMapRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"serviceProviderId":{"type":"string","format":"uuid"},"serviceProviderName":{"type":"string","nullable":true},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponse"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"serviceProviderId":{"type":"string","format":"uuid"},"serviceProviderName":{"type":"string","nullable":true},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponse"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"serviceProviderId":{"type":"string","format":"uuid"},"serviceProviderName":{"type":"string","nullable":true},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponse"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      