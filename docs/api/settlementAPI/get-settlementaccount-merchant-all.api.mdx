---
id: get-settlementaccount-merchant-all
title: "Get SettlementAccount merchant merchantId all"
description: "Get SettlementAccount merchant merchantId all"
sidebar_label: "Get SettlementAccount merchant merchantId all"
hide_title: true
hide_table_of_contents: true
api: eJytVE1v2zAM/SsGTxug1V138y2HIsuhQ7G0pyIHxmZsYbakSnSwzNB/H+ivpElQbEBPhq3HR/K9J3dgHXlkbc2qgAxK4jUx19SQYcxz2xp+IJ9XaHhR16CAsQyQvcARthhgsFHg0GNDTF4gHRhsCDJoRoJVAQq0gQwccgUKPL222lMBGfuWFIS8ogYh64APTioDe21KULCzvkGGDNpWFxCjmskfsaQfbbMlP5G/tuQPcIVNG6ayx8102vC3uwu+tf5DH8O2vH9YL56fvk9sFWHR1/zr6jFuBBycNYGCnN/d3sqjoJB77cQ5yGDd5jmFIL3PTpbEyYVXyWRJcvQmwd7fhriyYxRADU5lkKLT6QVNOlWn3ZEnpgNRIL+fgtD6WpZndiFL0zDzfClof1NSExwegq1bmfkmtw3I1trsbC+I5rrXcsCdbJMsHlegQNoMy+6/QlTgbOAGjdRONvynCG8knC1h+s2pq1Eb6dLv1I0CvQA6DerKrVBz/EFB9uYqSK+NgsoGFoau22KgZ1/HKJ+H4Il6hQ64rSUrO6wDKfhFh/Pk77FuZUaQ8L1TMEb7CN/Ii9eCv97sTIvcGiYjaf/0c8zw50T+C9c0Gj+iOZz2nOY5ESNuopoux4dPMXQ7uYpvtp8Dv7x/ghj/Aj0rud4=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementAccount merchant merchantId all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementAccount/merchant/{merchantId}/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementAccount merchant merchantId all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      