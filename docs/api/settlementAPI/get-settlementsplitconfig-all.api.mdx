---
id: get-settlementsplitconfig-all
title: "Get settlement split config all"
description: "Get settlement split config all"
sidebar_label: "Get settlement split config all"
hide_title: true
hide_table_of_contents: true
api: eJytk81u2zAQhF9FmFMDMHGa3nQzisDNoUVQJSdDB1pay0QokiFXRl1B716s5J/YMNpLTwLJmeXux1EPHyhqNt491cjREBfEbKklxylYw5V3a9PMrYUC6yYhX+IkKUTy1deEUiHoqFtiiiLq4XRLyPGsG/rRtSuKUDAOOd47ijsopGpDrUbeg3dBpMYxNaNu7WOredr68oBhUGf1CvOb/k+1xeP3Yv768u1QbUO6Hj2R3jsTqUbOsaMr5RNH4xoMQyniFLxLlOT84f5ePjWlKpogaJGj6KqKUpK7L04WxFk6As1G6NlEPdMj9pZ44/evA8HMG+SY6WBmJ+PtaLydjLPJmChuD6/RRSvjMYeUzz76atreNdSmoHfJ2066uqt8C5nLuLUfRzZsR1qTLjsFIJs/P0FBrpnG2X7GoBB84lY78R5A/3PMMyxHzEy/eBasNk7qjlP0ewRL6GDGMa9CgIIULhU2PrHI+36lE71GOwyyPSVH4NQm6ZWVx15rm0jhjXaX0d1q20lDkPT8xbDP5kleyiIa0SNfloM6hOzqzRcUKu+YnGT30899Im8y+RWv0Zma+BDpsyaOMVo8vmAY/gDCn1px
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settlement split config all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settlement-split-config/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settlement split config all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      