---
id: get-settlementsplitconfig-all
title: "Get settlement split config all"
description: "Get settlement split config all"
sidebar_label: "Get settlement split config all"
hide_title: true
hide_table_of_contents: true
api: eJytU8tu2zAQ/BVhTy3ARGl6080oAieHFkGVnAwd1tJaIkKRDLky6gr892IlP2rDSC89SSJnhjvD0QjOU0DWzj41UEBLXBKzoZ4sR280185udLswBhQwthGKFZwgpUC+uYagUuAxYE9MQUAjWOwJCnjGln4M/ZoCKNAWCngfKOxAQaw76hGKEXjnBaotUzvhNi70yPPS13tISZ3plfo3/R+15cP3cvH68nhQ6wibiRPofdCBGig4DHRFPnLQtoWUKgFH72ykKPv3d3fyaCjWQXuJFgooh7qmGOXsi50lcRaPgWZT6NmceoZT7D1x5/a3AxIzd1BAjl7nJ+LNRLyZiflMjBS2h9sYghF7zD4Wed5SHwffBmzoVt5r44bmFr0HsaPtxk1ONZspJOqjx112uvds8fwECkR9drH9AkmBd5F7tMI95PtPd2dpHNNl+sW5N6it6E7Dj3vnK0CvJ3dXvYMCEa4UdC6ywMdxjZFeg0lJlufCSCaNjrg2cscbNJEUvNHusrFbNIMMBFKaDwj7Sp7glXwELXgoVlVSh25dPfkihdpZJiuV/fRzX8TPmfyB19KZh/iryWdDHNuzfHiBlP4AKuRWpg==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settlement split config all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settlement-split-config/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settlement split config all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      