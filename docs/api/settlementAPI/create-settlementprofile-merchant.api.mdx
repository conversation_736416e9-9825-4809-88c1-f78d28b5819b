---
id: create-settlementprofile-merchant
title: "Create SettlementProfile merchant merchantId"
description: "Create SettlementProfile merchant merchantId"
sidebar_label: "Create SettlementProfile merchant merchantId"
hide_title: true
hide_table_of_contents: true
api: eJztWEuP2zgM/isBT/tQmnamk259SwdFO4dug8nMKciBsehErS2pkjxtEPi/L+hH7Dy6TYIeCjS52JGpTxT5USK5BmPJYVBG30mIIHaEgSYUQkoZ6WCdSVRKH8jFS9QBBARceIim0MqMKxmYCbDoMKNAjkXWoDEjiCCrZ99JEKA0RGAxLEGAoy+5ciQhCi4nAT5eUoYQrSGsLM/0wSm9AAGJcRkGiCDPlYSiEBvwd28/TEaPD+8b6CWhJHcCeFHMKmHy4Y2RK5aIjQ6kA7+itamKSwsNPnmjeWwPysw/UczWsY7tGRR5/tps/E3ulSbv/y1V3t+dztMU5ylVqhYCMA+mNXBnytyYlFBDIUD5kcyUvq80PyzjNxijODb5FtR3dJ6j/nxr5HF6svDRm2LhWo2z5pijZig/dipDt/qRRWraMu2P4BuglIpJgOm4Y68EU08CggqsAoyknOyavHHQKSCPVmLYRN1eqHUhA30LF2JeiPnLEbN7cP7194WhF4b+WgxlUEfeGu0r9109f84PST52yvJaEMEkj2PyHkQ3JyjPXJuiOonSvoY6aNyMvMfFcZ6VGI5Z7ix3ifMjr5n4Uc8NOqn04vCKZ4Tssf5/R+F/PF/5+ueiNc/3qGVKbu/kO/Xcu5DkNyHJWXnbhR2/BTvKu2n7HrotC/PeHkivMVlvq8rOKCwNF/TW+NKvXHBHMECrBnsYg2bqYN2CFMA3vntqavncpVxdh2B9NBi0zOhLenq2oMxbXHmT5qzvs9hkwGU18/u+La3ffsPMpvT9/K718K5Dqxp+N3GrK/tDqVqbjLWgbc61PbaVWh3+ZLofOonSrgadQIHrBP+5SYYv+zevXrzqv7wZXvXn10ncv4pfD6+T4RATHJZUUjqpMrQNa0pzdrzdG43vQAB7o+LD0wsONPZuhuURUrdDTuTJFsk2QdPJbwpReX5dU2gKaBWIA+2fNnpBQNRZZSZgySyMprBez9HTo0uLgoe/5MQ2nM4EPKFTVURP1yCV53e5CacdNTepGPxxX3d5/uxxb+qQ+vUg6hUbENOc/4GAz7Ta7k0Vs0I07aOfrkW1WqdZtdGEe1nV19sKsP/AAK3EXjrRzhjFMdnQke0uOuscA+OPkwdmdN3fyqrAcPiVG2X4tbKIKbdWnvzl2BpS1Iu8vFOgWpl//wH8sTDq
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create SettlementProfile merchant merchantId"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/SettlementProfile/merchant/{merchantId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create SettlementProfile merchant merchantId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"isAdminRequest":{"type":"boolean"},"settlementAccount":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"settlementProfileId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementAccountRequest"}},"additionalProperties":false,"title":"AddUpdateMerchantSettlementProfileRequest"}},"text/json":{"schema":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"isAdminRequest":{"type":"boolean"},"settlementAccount":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"settlementProfileId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementAccountRequest"}},"additionalProperties":false,"title":"AddUpdateMerchantSettlementProfileRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"isAdminRequest":{"type":"boolean"},"settlementAccount":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"settlementProfileId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementAccountRequest"}},"additionalProperties":false,"title":"AddUpdateMerchantSettlementProfileRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      