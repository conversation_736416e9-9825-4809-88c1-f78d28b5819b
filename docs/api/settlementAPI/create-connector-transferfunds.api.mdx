---
id: create-connector-transferfunds
title: "Create connector connector transfer funds"
description: "Create connector connector transfer funds"
sidebar_label: "Create connector connector transfer funds"
hide_title: true
hide_table_of_contents: true
api: eJztmt1v2zgMwP+VgE/34S673Vveuqy3FbfrBUl3wKEIDozMJNpsyZPobEGQ/32gFX+0STu72w7Y4L7UkUiKpsWfZUo7sBk5ZG3NZQwjUI6QaWyNIcXWXTs0fklumZvYQwSMKw+jG6gEYB5Bhg5TYnLStQODKYmlSiQCbWAEGfIaInD0PteOYhixyykCr9aUIox2wNtMFD07bVaw30eVrZcXf83O31y/Kk2tCWNynYzNgzB5fm7jrUgoa5gMyyVmWaJVEYXhW2+NtB2Zsou3pBgiyJzEjDX5Qs7mTtGl8aw5FwvKxnTsQQQmTxJcJBR83UeAqc3D+J8VXZChpVYa3fZcKVG7KiLzONU8XZDrqvwczbt/yOnlIVCPs/Lnv+PXtKGklZ5aozGUjNsG1Dq90gbZuq5BOtZsf3e17heEqDbSKUIxeRY1Sd96BrYOWIomRqYpLcmRUdTBYcnMCyPZt53SsuM9XqELzGmll+E2JcOVl62UWMiFqgRbN43XVrV3rzG7u93XQicJuVbeCS3iWItpTCYN/iwx8RQBaxZZ+CM3ccnsacCdgBSYPnJPtp5sPdl6sv1YZGsu3X75tUdcj7gecT3ifiDEibojn1njA7OePX0q/2LyyuksOAyzXCny8one+KwtVn1ZgrobEw+masGFtQmhAcnq1rOTvMdVO9kYuYVfjthtx3eoqw3TqigFLK1LkUPT78+gEbXWGdVUeEFLbfS3neaevH/UFG+pYTrNaPbvX7R8EP9fXL91iI4WAK1vStg7sZ6ps2vXhexXw8Z1bXnGyLkv2CFkD1GHRwKo1JaH4DZa0cTZjY5bvmJYpzRjTLNT0lWuShDPRPTLnLy6nPxXXr9CEyfkjhaHnZeGPQZ7DPYY7DH4/WPwcdW/nn89/3r+9fz73vlXfEHf/loeF7vcg2qLunHFB5uDcss7JV5b2RrPrC+SQLaxRzDETA8rteGuutwPSxNnpQkJXLk7nrtENrCZMz8aDj0xJyTIOItp82RFqc9w621STMInyqYgO9fC7mm9e33xEdMsoQcKmHWMyzpl3XJfOfJBiUNt6aTMfZWzk8J1hazuvlUqrJvvqQg+JHA08ufre6dkTzn5ULWulrqvKNdIkTu1t1PjN0pRdffxu6Xuu0O8kx11Xezkgzk5Zl3lKtv2cgRjaYt0PqTiyzBrB7NqMg/OJ5cQgUz6YHDzmyhKBqVYEP9wsKNLIt5K4QomjRrXPgrZtTvk6A1gpkNFrDqJMmr+uJOo8wjWkuOjG9jtFujpjUv2e2kuQAqjm3kEG3Q6EO5mB7H2ch1XdLrjY1WLg5+mh5MqPw9k4FO+HxrRbCV0mOTyCyJ4R9tbx2n2831UnoD56k6EwRrnbSpH5DhO6B0Hg2fFC6yWOPrYrjXOlaKMG7LNQecNxk7+nl3LtDsc0UlDdjn8IGd98EMIiC1uLaxBpG0HCZpVXiwzIYwsf58AtsIuyg==
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create connector connector transfer funds"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/connector/{connector}/transfer-funds"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create connector connector transfer funds

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"connector","in":"path","required":true,"schema":{"type":"string"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"sourceInstitutioncode":{"type":"string","nullable":true},"amount":{"type":"string","nullable":true},"beneficiaryAccountName":{"type":"string","nullable":true},"beneficiaryAccountNumber":{"type":"string","nullable":true},"beneficiaryBankVerificationNumber":{"type":"string","nullable":true},"beneficiaryKYCLevel":{"type":"string","nullable":true},"channelCode":{"type":"string","nullable":true},"originatorAccountName":{"type":"string","nullable":true},"originatorAccountNumber":{"type":"string","nullable":true},"originatorBankVerificationNumber":{"type":"string","nullable":true},"originatorKYCLevel":{"type":"string","nullable":true},"destinationInstitutionCode":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"nameEnquiryRef":{"type":"string","nullable":true},"originatorNarration":{"type":"string","nullable":true},"paymentReference":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"transactionLocation":{"type":"string","nullable":true},"beneficiaryNarration":{"type":"string","nullable":true},"billerId":{"type":"string","nullable":true}},"additionalProperties":false,"title":"FundTransferRequest"}},"text/json":{"schema":{"type":"object","properties":{"sourceInstitutioncode":{"type":"string","nullable":true},"amount":{"type":"string","nullable":true},"beneficiaryAccountName":{"type":"string","nullable":true},"beneficiaryAccountNumber":{"type":"string","nullable":true},"beneficiaryBankVerificationNumber":{"type":"string","nullable":true},"beneficiaryKYCLevel":{"type":"string","nullable":true},"channelCode":{"type":"string","nullable":true},"originatorAccountName":{"type":"string","nullable":true},"originatorAccountNumber":{"type":"string","nullable":true},"originatorBankVerificationNumber":{"type":"string","nullable":true},"originatorKYCLevel":{"type":"string","nullable":true},"destinationInstitutionCode":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"nameEnquiryRef":{"type":"string","nullable":true},"originatorNarration":{"type":"string","nullable":true},"paymentReference":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"transactionLocation":{"type":"string","nullable":true},"beneficiaryNarration":{"type":"string","nullable":true},"billerId":{"type":"string","nullable":true}},"additionalProperties":false,"title":"FundTransferRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"sourceInstitutioncode":{"type":"string","nullable":true},"amount":{"type":"string","nullable":true},"beneficiaryAccountName":{"type":"string","nullable":true},"beneficiaryAccountNumber":{"type":"string","nullable":true},"beneficiaryBankVerificationNumber":{"type":"string","nullable":true},"beneficiaryKYCLevel":{"type":"string","nullable":true},"channelCode":{"type":"string","nullable":true},"originatorAccountName":{"type":"string","nullable":true},"originatorAccountNumber":{"type":"string","nullable":true},"originatorBankVerificationNumber":{"type":"string","nullable":true},"originatorKYCLevel":{"type":"string","nullable":true},"destinationInstitutionCode":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"nameEnquiryRef":{"type":"string","nullable":true},"originatorNarration":{"type":"string","nullable":true},"paymentReference":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"transactionLocation":{"type":"string","nullable":true},"beneficiaryNarration":{"type":"string","nullable":true},"billerId":{"type":"string","nullable":true}},"additionalProperties":false,"title":"FundTransferRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"retryCount":{"type":"integer","format":"int32"},"responseCode":{"type":"string","nullable":true},"responseCodeDefinition":{"type":"string","nullable":true},"paymentReference":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"narration":{"type":"string","nullable":true},"tsqData":{"type":"object","properties":{"responseCode":{"type":"string","nullable":true},"responseCodeDefinition":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"sourceInstitutionCode":{"type":"string","nullable":true},"datePosted":{"type":"string","nullable":true},"transactionType":{"type":"string","nullable":true}},"additionalProperties":false,"title":"TransactionStatusRequeryResponse"}},"additionalProperties":false,"title":"FundTransferResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"FundTransferResponseNIP_ResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"retryCount":{"type":"integer","format":"int32"},"responseCode":{"type":"string","nullable":true},"responseCodeDefinition":{"type":"string","nullable":true},"paymentReference":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"narration":{"type":"string","nullable":true},"tsqData":{"type":"object","properties":{"responseCode":{"type":"string","nullable":true},"responseCodeDefinition":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"sourceInstitutionCode":{"type":"string","nullable":true},"datePosted":{"type":"string","nullable":true},"transactionType":{"type":"string","nullable":true}},"additionalProperties":false,"title":"TransactionStatusRequeryResponse"}},"additionalProperties":false,"title":"FundTransferResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"FundTransferResponseNIP_ResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"retryCount":{"type":"integer","format":"int32"},"responseCode":{"type":"string","nullable":true},"responseCodeDefinition":{"type":"string","nullable":true},"paymentReference":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"narration":{"type":"string","nullable":true},"tsqData":{"type":"object","properties":{"responseCode":{"type":"string","nullable":true},"responseCodeDefinition":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"sourceInstitutionCode":{"type":"string","nullable":true},"datePosted":{"type":"string","nullable":true},"transactionType":{"type":"string","nullable":true}},"additionalProperties":false,"title":"TransactionStatusRequeryResponse"}},"additionalProperties":false,"title":"FundTransferResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"FundTransferResponseNIP_ResponseHandler"}}}}}}
>
  
</StatusCodes>


      