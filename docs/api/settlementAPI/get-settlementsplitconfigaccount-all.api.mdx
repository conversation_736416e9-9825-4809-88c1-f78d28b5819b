---
id: get-settlementsplitconfigaccount-all
title: "Get SettlementSplitConfigAccount all"
description: "Get SettlementSplitConfigAccount all"
sidebar_label: "Get SettlementSplitConfigAccount all"
hide_title: true
hide_table_of_contents: true
api: eJytU7Fu2zAQ/RXjphYgojTduBlF4GZoEVTJZGg4S2eJKEUy5NGoK+jfi5PsJDYMI0MnUeR7j3fvHgfwgSKy8e6hAQ0tcUnMlnpynII1XHu3NS3Wtc+Ol9aCAsY2gV7DG7IU5LcJuZyRUCkIGLEnpijoARz2BBoesaWfud9QBAXGgYaXTHEPClLdUY+gB+B9EKhxTO2E2/rYI89bX+9gHNWJXmn+0v9RW93/KJfPT9+Pah1hM3EivWQTqQHNMdMF+cTRuBbGsRJwCt4lSnJ+d3srn4ZSHU0Qq0FDmeuaUpK7z05WxItrzi5wGkJP3PnDyEC85g40FBhMcY1dzOxEcXecS45WGmUOSRdFS33KoY3Y0I2sa+tzc4MhgDRm3NZPPRu2k13Up4D7dwUvlo8PoEDU5352X2BUEHziHp1wj05/rM8Tc17NZvrDRbBonIhPHQwHD9aAwYC6nk4Fol4p6Hxi4QzDBhM9RzuOsj2HSNxpTMKNlblv0SZS8Jv25yneoc1SFUiQrhAOMX2DV/ITjeBBr6tRHfN28eYzK2rvmJzE+NOvQzg/L+R5XrJoLuJduk+KeA3T6v4JxvEfEFJluw==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementSplitConfigAccount all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementSplitConfigAccount/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementSplitConfigAccount all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      