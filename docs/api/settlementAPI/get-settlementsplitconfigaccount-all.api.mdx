---
id: get-settlementsplitconfigaccount-all
title: "Get SettlementSplitConfigAccount all"
description: "Get SettlementSplitConfigAccount all"
sidebar_label: "Get SettlementSplitConfigAccount all"
hide_title: true
hide_table_of_contents: true
api: eJytU0Fu2zAQ/IoxpwZgozS96WYUgZtDiqBKToYPtLSWiFIkQ66MuoL+XqxkO7FhGD30JJCc2d2ZHfXwgaJm491jhRw1cUHMllpynII1XHq3MbUuS985nlsLBdZ1Qr7EO7IQ5LcROZ+QWCkEHXVLTFHQPZxuCTmedU0/unZNEQrGIcdbR3EHhVQ21GrkPXgXBGocUz3iNj62mqerr/cYBnVSrzB/6P9UWzw8FfPXl++Hag3pauREeutMpAo5x44ulE8cjasxDCsBp+BdoiTv93d38qkoldEEsRo5iq4sKSXpffayIJ5dc3amxyW0xI3frwziNTfIkelgsmvsbGInitvDXrpoRShzSHmWpSP5c0Xb25raFPQuedvJfLelbyEKjdv4UbxhO/o24T5MPps/P0JB2kzCtl8wKASfuNVOuAfL/03wiUtH15l+cxasNk6Kj1L6vRlL6GCgrsdUQaqvFBqfWDh9v9aJXqMdBrme0iQ2VSbptZUAbLRNpPCLdudx3mrbyVSQRF0h7PP6Dl/JIRrBI1+uBnUI3sXOZ1aU3jE5yfOnn/uU3szkP71k0TTEh5ifDHFM1eLhBcPwFy+HaYY=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementSplitConfigAccount all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementSplitConfigAccount/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementSplitConfigAccount all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      