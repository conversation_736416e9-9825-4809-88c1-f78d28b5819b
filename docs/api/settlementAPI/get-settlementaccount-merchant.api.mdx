---
id: get-settlementaccount-merchant
title: "Get SettlementAccount settlementAccountId merchant merchantId"
description: "Get SettlementAccount settlementAccountId merchant merchantId"
sidebar_label: "Get SettlementAccount settlementAccountId merchant merchantId"
hide_title: true
hide_table_of_contents: true
api: eJy9k09v2zAMxb+K8U4boNXdjrrlUGQ5FBiW9hTkwNpMLNSWVIkOFhj67oOSOEmb9FDsz8m2RD+SP/INcJ4DiXF2VkNjzTJnkZY7tkJV5Xor9xyqhqxAQWgdoRc4xUz2MVgqeArUsXDIIQMsdQyN+DZ0VkPBWGh4kgYKgV96E7iGltCzQqwa7gh6gGz9TkKCsWsorFzoSKDR96ZGSuqYpTvU+E/Ep3f388njw/dRumGqOXxAPKVlDo7e2cgx33+7vc2PmmMVjM/4oTHvq4pjzLnf3ExZigvmxRW0xQiieEWkY2ncYb5QezYaJXlTXsiWwxXdVI565XBSTlCIHDbjyPvQZjwiPuqyPMl8qXlzs+YuetpG1/a5q5vKdchcjF25HTIj7Y72Pu6s32LyYwaFnGaPY/MVScG7KB3Z/O84qD/E9Ar6cYjCv6T0LRmbs+56HA4IFyBvoK74QUFf3/zu5CZ9lnyp0LgoWXIYnijyY2hTyscvPYct9GKpsKFg6ClTWgyoTczvNfSK2sgX1VfOCtu80J9+Hvb0c5E9fK2rwyHZbQZNbZ+/oPDM23c8nP3xH0s4Q5WWSY0e/Osg9tnOHH+sJO/q0UfTuwek9BvFs9C/
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementAccount settlementAccountId merchant merchantId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementAccount/{settlementAccountId}/merchant/{merchantId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementAccount settlementAccountId merchant merchantId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      