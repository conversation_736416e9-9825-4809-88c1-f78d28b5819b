---
id: get-settlementaccount-merchant
title: "Get SettlementAccount settlementAccountId merchant merchantId"
description: "Get SettlementAccount settlementAccountId merchant merchantId"
sidebar_label: "Get SettlementAccount settlementAccountId merchant merchantId"
hide_title: true
hide_table_of_contents: true
api: eJy9kz9v2zAQxb+K8KYWIKK0IzcPgeshQFEnk+HhIp0tohLFkEejhsDvXtC2bCdWh6J/JlHk8d3xd/cG9I49ientoobGlmXJIi13bIWqqo9WHtlXDVmBgtA2QK9wiZkdY7BWcOSpY2GfQwZY6hga4X3oooaCsdBwJA0UPL9G47mGFh9ZIVQNdwQ9QPbuICHe2C0UNr3vSKARo6mRkjpn6U41/hPx+cPjcvb89GWUbphq9r8hntI6BwfX28Ahn3++v8+fmkPljcv4obGMVcUh5NzvTuYsxQ3zYgJtMYIo3hDpWJr+1F+oIxuNkpwpb2TLYUI3laNeOVyUExQC+93Y8ujbjEfEBV2WW+5CdFtPNd/lddX2sb4j55BxGLvpD6SMtAfI3AVH+6tnFrOvCyhk9SOF3SckBdcH6cjmu2N//pDOG9bn3gn/kNK1ZGzOenjacCK3AjkDNWEDBT098N3FRPoq+Vqh6YNkyWF4ocDPvk0pb79G9nvo1VphR97QS6a0GlCbkNc19IbawDfVV70VtnmOP3w7jefHIlt36lWnTbL7DJramP+g8J33v7ButsV/LOEKVVonNVrvr4M4Zrsy+rmSPKtn+8wfnpDST3eizPQ=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementAccount settlementAccountId merchant merchantId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementAccount/{settlementAccountId}/merchant/{merchantId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementAccount settlementAccountId merchant merchantId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      