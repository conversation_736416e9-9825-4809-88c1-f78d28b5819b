---
id: update-batchperiod-update
title: "Update BatchPeriod Update batchPeriodId"
description: "Update BatchPeriod Update batchPeriodId"
sidebar_label: "Update BatchPeriod Update batchPeriodId"
hide_title: true
hide_table_of_contents: true
api: eJztWEtz4zYM/isenPpg1u72ppuTySQ5bOvJ45TxARZhm1uK1JKQdz0a/fcOKVuP2NOV0/awM8rFDgmAIPABNL4SbE4OWVnzICGBIpfIdI2cbnNyysqXuAACGDcekleIe4u4B0sBOTrMiMmFzRIMZgQJrFqhBwkClIEEcuQtCHD0pVCOJCTsChLg0y1lCEkJvM+DsmenzAYErK3LkINXhZJQVaKxf3f76Wn+8nx/NL0llOQuMF5Vy1qYPF9buQ8SqTVMhsNXzHOt0hiW6WdvTVg7MWVXnyllEJC7EERW5MPu2tns3hauI6kM0yb611xJGf79I1Qiin9SpmAaqMD2AutsL7Jd5/wC+5mVaq1IXu/PJdAUWuNKU52NnvhthkoP0KkEoJQqZAL1ohPoNWpPAlhxkIWXFrg17h7r5AbYANM3HvP44+exW5a//Dom9AdPaLDhyOfW+DpDH2ez8CHJp07lwTQk8FSkKXkPotugY0XnGtVFCPAHU63gylpNaGLEyHvc0KBwSeQBx/Vfwe8/cKKrcWPlQF9ojYXm85cKIH5W2TBLbAeLXozH1BEyyTn/YxwCVK44ONHB5Ht0/jfYC/DErCkjwwtn10pTF0/oHO7DrxKmzH8fIRm5dIuGrwuvDHn/Bw6M/1HxT7Oy6KQym/MIO1HEgu1Tc4NzoBla2HfET29D8Xio5pCKd/aLO+Jeszga/Hf6x897NFKTO3lNLn1Lxk4ydpKxk4ydxL1/vhhbyNhCxhYythAXJ6FYO92xp56bJh31yWHpLbmWEW+trAm2NDBskWhLYIq5mnb0p7X+tOwZqCCgyO2ODF7hdCDUmHOfTKctvq4k7T5sKPM57r3VRXDzQ2ozCExa6H2PLZt2+w2zXFN/6J71R+pZOzDPuuPwrF/Ps34ZtWA6qZaG2guc4NpGNDVpiG5PWohM5osHEBBuXYd791scs63nDGMbPzCNw9PQS1+D5M6gWok6uOUhQa+AuQLRY1QFNIRr0j9gKWBrPQe1slyhpxenqyosfynI7SF5XQrYoVM1zF9LkMqH77JB5RsPm3Eafno80KY/TwLTe87zY0sxoaHsUBfhPxDwF+1P+N5qWYkjJfufO1If2CGAG2cCP1zv3tQGr56DgVbi5Ad/qzFPU8q5I9s9dNmpscX8+SacujqQxll8GcHh18A+49c6KjbeLXbVuFaCRrMp4osO9dHh72/9nzKr
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update BatchPeriod Update batchPeriodId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/BatchPeriod/Update/{batchPeriodId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update BatchPeriod Update batchPeriodId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"batchPeriodId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"fromHour":{"type":"integer","format":"int32"},"fromMinute":{"type":"integer","format":"int32"},"toHour":{"type":"integer","format":"int32"},"toMinute":{"type":"integer","format":"int32"},"periodHour":{"type":"integer","format":"int32"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateBatchPeriodRequest"}},"text/json":{"schema":{"type":"object","properties":{"fromHour":{"type":"integer","format":"int32"},"fromMinute":{"type":"integer","format":"int32"},"toHour":{"type":"integer","format":"int32"},"toMinute":{"type":"integer","format":"int32"},"periodHour":{"type":"integer","format":"int32"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateBatchPeriodRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"fromHour":{"type":"integer","format":"int32"},"fromMinute":{"type":"integer","format":"int32"},"toHour":{"type":"integer","format":"int32"},"toMinute":{"type":"integer","format":"int32"},"periodHour":{"type":"integer","format":"int32"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateBatchPeriodRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      