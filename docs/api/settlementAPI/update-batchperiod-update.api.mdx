---
id: update-batchperiod-update
title: "Update BatchPeriod Update batchPeriodId"
description: "Update BatchPeriod Update batchPeriodId"
sidebar_label: "Update BatchPeriod Update batchPeriodId"
hide_title: true
hide_table_of_contents: true
api: eJztWE2P2zgM/SsBT92t2mTbm2+ZwWBmDt0N5uM0yIGxmEStLKkSPW0Q+L8XkhPHngRbZ3Z7KOBc7EgkJZGPlPm2YB15ZGXNrYQMSieR6QI5XzvyysrHNAACGFcBsidIc7M0B3MBDj0WxOTj5BYMFgQZLA5CtxIEKAMZOOQ1CPD0tVSeJGTsSxIQ8jUVCNkWeOOicmCvzAoELK0vkOOuSiWhqkRj//rq0/308eFmb3pNKMmfYbyq5rUwBb6wchMlcmuYDMdXdE6rPLll/DlYE8eOTNnFZ8oZBDgfnciKQpxdelvc2NK3JJVhWqX9NUdShj9+gEok8U/KlEw9FdieYZ3tWbbrmJ9hv7BSLRXJi82pAJpSa1xoqqPREb8qUOkeOpUAlFLFSKCetRy9RB1IACuOsvB4AG6Nu7s6uBE2wPSdhzj+/nFsp+Wfb4eA/uYBjTY8BWdNqCP0YTKJD0kh98pF05DBfZnnFAKIdoFOGe00qrMQEHamDoILazWhSR6jEHBFvdwlkXss170Ff37BibbGpZU990JLLDWfPlQE8YMq+lli21v0bDzmnpBJTvlf/RCh8o7jJlqYfI3OL4O9gEDMmgoyPPN2qTS18YTe4yZ+lTAV4ecIKcjnazR8UQZlKIS/saf/94r/mIVFL5VZnUbYkSKWbO+bE5wCTd/Evia+f+mKu102x1C8sl5cE3eKxd7gf9PfP2/QSE3+6DY59y4ZKslQSYZKMlQS//r+YighQwkZSshQQnzqhFLutNueum8atdRHu6GX5FpBvLayJtjyyLAloi2DMTo1bumPa/3xtmOggogi/7xn8EqvI6HG7EI2Hq+oCKVbeZT0Pr7n2pbyPToHkUCLJe/uQKJdfcfCaer22pNuJz059MmTdhc86abxpJs9BwwdJUnD6EUqcGkTiBrvF8HhZnRAxmg6uwUB8bC1l5//St21DVxgqt47grG/9ztRawDc6k8rUft0u4vLE6BTIDpEqoCGZ826C8wFrG3gqLbdLjDQo9dVFYe/luQ3kD3NBTyjVzW6n7YgVYjvsgHjix02XTS8uduxpX+MIsF7auf7SmJiHXlGXcZ/IOALbY5o3mpeiT0T+79vpF6wxfs2m4m0cD17WRt89xANHCSOvvMPGtM8J8ct2fai81ZqzaYPl3HVxY4rLtKFCB6/RdIZv9VeselsqZimsS1oNKsyXeRQLx1/PwDEKi7g
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update BatchPeriod Update batchPeriodId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/BatchPeriod/Update/{batchPeriodId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update BatchPeriod Update batchPeriodId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"batchPeriodId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"fromHour":{"type":"integer","format":"int32"},"fromMinute":{"type":"integer","format":"int32"},"toHour":{"type":"integer","format":"int32"},"toMinute":{"type":"integer","format":"int32"},"periodHour":{"type":"integer","format":"int32"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateBatchPeriodRequest"}},"text/json":{"schema":{"type":"object","properties":{"fromHour":{"type":"integer","format":"int32"},"fromMinute":{"type":"integer","format":"int32"},"toHour":{"type":"integer","format":"int32"},"toMinute":{"type":"integer","format":"int32"},"periodHour":{"type":"integer","format":"int32"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateBatchPeriodRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"fromHour":{"type":"integer","format":"int32"},"fromMinute":{"type":"integer","format":"int32"},"toHour":{"type":"integer","format":"int32"},"toMinute":{"type":"integer","format":"int32"},"periodHour":{"type":"integer","format":"int32"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateBatchPeriodRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      