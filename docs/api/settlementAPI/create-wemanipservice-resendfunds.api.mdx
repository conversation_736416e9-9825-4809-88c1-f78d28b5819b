---
id: create-wemanipservice-resendfunds
title: "Create WemaNIPService ResendFunds"
description: "Create WemaNIPService ResendFunds"
sidebar_label: "Create WemaNIPService ResendFunds"
hide_title: true
hide_table_of_contents: true
api: eJztWN9v2kgQ/lfQPN0PU9qkoXd+41oujZSkETg6nRBCw3oM29rr7e44LUL+309j02AgvcOnvFSCF6zd+WZnZmc/e7815JYcss7NVQwhKEfI9BdlaLT15B60ohF5MnFSmNhDAIwLD+EExOb26m5c28A0AIsOM2JyMr8GgxlBCJfDm/HgPnoPAWgDISwJY3IQgKPPhXYUQ8iuoAC8WlKGEK6BV1aQnp02CyjLaW1Mnv/I45VYqNwwGZZHtDbVqsqg99HnRsYOXOXzj6QYArBO8mVNXmYlp8ih8Qm5yKEiqcH++gEkucuQIYSi0DGUZQAYx1pWxPSu4TDB1FMArDkVfF23P6Vuozr8Csv0lX+MSJul/eXXHyBkQTvyNje+juHs5Uv5i8krp604hRDGhVLkpZcbbVTtik1Rt8rRb1xtDed5nhIaKAPIyHtc0FNJmyJNcS6JSO+XAcTIzeXQOVzJiWHK/H+HgUrlheH6xB2xmlNuOIuubob/uiExMnVZZyTJpGp52RLykLybvRtELRDWc1sIGXZ/t8Sww9uWkAdMi2H7ZYx+sucPNsSiY62KFJ0/yl5cO8rQfTrOPnZidjCsnh6eY4pGNVvJFNm8Iu1tpnkhkDr2aBaNBrez8eh6dnt/08Bpw7TYBWrD52eC08azy0y0B/l+EovrVhtwLJPIi+ySeFCfoTEjU0aGRxsekVD3Qnkmz9faPz6/RxOn5A5oty3pngjpREgnQjoR0rMS0v/6Yj0x0YmJTkx0YqJnZKLqfrd7l3tb6RWdXTGi07gcgpANL3MRN2zuK4JAXkIIPbS6twvs7QJF//imZhQuFfGC2fqw11tQ5gu7cBjTC3lWaV7EL9BaELFCqHK0FSyGXzGzKX33TgznCf52kfRfdy/evHrTfX3RP+vOzxPVPVO/98+Tfh8T7NdtkeTVFm+KeEmZt7jqjIk5rerXGdxdQQASdV2fh1dVH+aeM6zoe6PKHFO3nUo/NlbjolwGdVnWm5JOAK2GYF8aCpqXdRGKlrIP4QTW6zl6undpWcrw54LcCsLJVDjF6bqrJtMy+KYZyT7E2stE/NhRe1E+Xunhp9FGYPq5I7LVU9F/otWuQiVcJmZQBuvN7NvaYTcSB1uLg4/0LWKgFFlu2DYXnTb68e7DOIIA5htRK8tjsXb4RdQx/CJxBJBXqVVcVo2tIUWzKKq3J9Qry+8f7hvUXQ==
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create WemaNIPService ResendFunds"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/WemaNIPService/ResendFunds"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create WemaNIPService ResendFunds

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"fundTransferTraceId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"ResendFundsRequest"}},"text/json":{"schema":{"type":"object","properties":{"fundTransferTraceId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"ResendFundsRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"fundTransferTraceId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"ResendFundsRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"accountname":{"type":"string","nullable":true},"rcrE_TIME":{"type":"string","format":"date-time"},"lchG_TIME":{"type":"string","format":"date-time"},"vfD_DATE":{"type":"string","format":"date-time"},"pstD_DATE":{"type":"string","format":"date-time"},"entrY_DATE":{"type":"string","format":"date-time"},"traN_DATE":{"type":"string","format":"date-time"},"valuE_DATE":{"type":"string","format":"date-time"},"tranid":{"type":"string","nullable":true},"particulars":{"type":"string","nullable":true},"tranremarks":{"type":"string","nullable":true},"dr":{"nullable":true},"cr":{"nullable":true},"balance":{"type":"number","format":"double"},"parT_TRAN_SRL_NUM":{"type":"integer","format":"int32"},"instrmnT_NUM":{"type":"string","nullable":true},"gL_DATE":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementResponse"},"nullable":true}},"additionalProperties":false,"title":"WemaGetAccountStatementResponseListResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"accountname":{"type":"string","nullable":true},"rcrE_TIME":{"type":"string","format":"date-time"},"lchG_TIME":{"type":"string","format":"date-time"},"vfD_DATE":{"type":"string","format":"date-time"},"pstD_DATE":{"type":"string","format":"date-time"},"entrY_DATE":{"type":"string","format":"date-time"},"traN_DATE":{"type":"string","format":"date-time"},"valuE_DATE":{"type":"string","format":"date-time"},"tranid":{"type":"string","nullable":true},"particulars":{"type":"string","nullable":true},"tranremarks":{"type":"string","nullable":true},"dr":{"nullable":true},"cr":{"nullable":true},"balance":{"type":"number","format":"double"},"parT_TRAN_SRL_NUM":{"type":"integer","format":"int32"},"instrmnT_NUM":{"type":"string","nullable":true},"gL_DATE":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementResponse"},"nullable":true}},"additionalProperties":false,"title":"WemaGetAccountStatementResponseListResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"accountname":{"type":"string","nullable":true},"rcrE_TIME":{"type":"string","format":"date-time"},"lchG_TIME":{"type":"string","format":"date-time"},"vfD_DATE":{"type":"string","format":"date-time"},"pstD_DATE":{"type":"string","format":"date-time"},"entrY_DATE":{"type":"string","format":"date-time"},"traN_DATE":{"type":"string","format":"date-time"},"valuE_DATE":{"type":"string","format":"date-time"},"tranid":{"type":"string","nullable":true},"particulars":{"type":"string","nullable":true},"tranremarks":{"type":"string","nullable":true},"dr":{"nullable":true},"cr":{"nullable":true},"balance":{"type":"number","format":"double"},"parT_TRAN_SRL_NUM":{"type":"integer","format":"int32"},"instrmnT_NUM":{"type":"string","nullable":true},"gL_DATE":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementResponse"},"nullable":true}},"additionalProperties":false,"title":"WemaGetAccountStatementResponseListResponseHandler"}}}}}}
>
  
</StatusCodes>


      