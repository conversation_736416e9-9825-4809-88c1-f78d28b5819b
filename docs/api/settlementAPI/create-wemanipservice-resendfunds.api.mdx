---
id: create-wemanipservice-resendfunds
title: "Create WemaNIPService ResendFunds"
description: "Create WemaNIPService ResendFunds"
sidebar_label: "Create WemaNIPService ResendFunds"
hide_title: true
hide_table_of_contents: true
api: eJztWG1v2kgQ/itoPt2LKW3S0Dt/41oujZSkETg6nRBCw3oM29q77u6YFiH/99PYFAykd3DKl0rwBWs9z7zt+LH3WYHNySFra25iCEE5Qqa/KEOjc09uoRUNyJOJk8LEHgJgnHkIRyA29zcPw9oGxgHk6DAjJif3V2AwIwjhun837D1G7yEAbSCEOWFMDgJw9LnQjmII2RUUgFdzyhDCFfAyF6Rnp80MynJcG5PnP2y8FAtlDZNhucQ8T7WqKuh89NbI2oErO/1IiiGA3Em9rMnLXakpcmh8Qi5yqEh6sB8/gMS6DBlCKAodQ1kGgHGsJSKmDw2HCaaeAmDNqeDrvv0pfRvU6VdYpq/8Y2TabO0vv/4AKQvakc+t8XUOFy9fyl9MXjmdi1MIYVgoRV5muTFG1a7kKeqTavRrV1vDqbUpoYEygIy8xxk9VbQp0hSnUojMfhlAjNwMh87hUp4Ypsz/dxqolC0M10/cEdGccv1JdHPX/9cNiZGpzTojKSZV8+sTIYvk3eRdLzoBkXs+FUKG3d8nYtjh/YmQBaZF//QwRj858wcbkqNjrYoUnT/KXlw7ytB9Os4+dmJ2sKyeXp5iikY1R8kU2bQi7W2lthBInXs0iQa9+8lwcDu5f7xr4LRhmu0CteHLC8Fp49llJtqDfL+I2e1JG3Ask8iL7Jq4Vz9DQ0amjAwP1jwiqe6l8kyeb7XfXL9HE6fkDmj3VNI9E9KZkM6EdCakZyWk//XFemaiMxOdmejMRM/IRNX5bvcs97bSK1q7YkSrcTgEIRueWxE3cusrgkCeQwgdzHVnF9jZBYr+8U3NKFwq4gVz7sNOxxNzWmXcjmnxYkaZz3HpbVpIXi+UzUBUC+HMwVa56H/FLE/pu4djuEzwt6uk+7p99ebVm/brq+5Fe3qZqPaF+r17mXS7mGC3no/EVnu97uZ1Hb813KTV6j3cQACSft2oxatqIK3nDCseX8szxzRwp+WbCWucmMug7s9q3dsRYK4h2NeIguapXRSjuWxIOILVaoqeHl1alrL8uSC3hHA0FnJxuh6v0bgMvolHsiGx9nIj3ozWXpabsz38NFgrTT+3RL96KvtPtNyVqoTUxAzKYLW++7Z22I7Ewdbi4Gt9i+gpRTk3bJtBx43BfPgwjCCA6Vrdymws1g6/iEyGXySPAGxVWkVq1doKUjSzonqNQh1Zfv8AdIPYKA==
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create WemaNIPService ResendFunds"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/WemaNIPService/ResendFunds"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create WemaNIPService ResendFunds

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"fundTransferTraceId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"ResendFundsRequest"}},"text/json":{"schema":{"type":"object","properties":{"fundTransferTraceId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"ResendFundsRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"fundTransferTraceId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"ResendFundsRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"accountname":{"type":"string","nullable":true},"rcrE_TIME":{"type":"string","format":"date-time"},"lchG_TIME":{"type":"string","format":"date-time"},"vfD_DATE":{"type":"string","format":"date-time"},"pstD_DATE":{"type":"string","format":"date-time"},"entrY_DATE":{"type":"string","format":"date-time"},"traN_DATE":{"type":"string","format":"date-time"},"valuE_DATE":{"type":"string","format":"date-time"},"tranid":{"type":"string","nullable":true},"particulars":{"type":"string","nullable":true},"tranremarks":{"type":"string","nullable":true},"dr":{"nullable":true},"cr":{"nullable":true},"balance":{"type":"number","format":"double"},"parT_TRAN_SRL_NUM":{"type":"integer","format":"int32"},"instrmnT_NUM":{"type":"string","nullable":true},"gL_DATE":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementResponse"},"nullable":true}},"additionalProperties":false,"title":"WemaGetAccountStatementResponseListResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"accountname":{"type":"string","nullable":true},"rcrE_TIME":{"type":"string","format":"date-time"},"lchG_TIME":{"type":"string","format":"date-time"},"vfD_DATE":{"type":"string","format":"date-time"},"pstD_DATE":{"type":"string","format":"date-time"},"entrY_DATE":{"type":"string","format":"date-time"},"traN_DATE":{"type":"string","format":"date-time"},"valuE_DATE":{"type":"string","format":"date-time"},"tranid":{"type":"string","nullable":true},"particulars":{"type":"string","nullable":true},"tranremarks":{"type":"string","nullable":true},"dr":{"nullable":true},"cr":{"nullable":true},"balance":{"type":"number","format":"double"},"parT_TRAN_SRL_NUM":{"type":"integer","format":"int32"},"instrmnT_NUM":{"type":"string","nullable":true},"gL_DATE":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementResponse"},"nullable":true}},"additionalProperties":false,"title":"WemaGetAccountStatementResponseListResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"accountname":{"type":"string","nullable":true},"rcrE_TIME":{"type":"string","format":"date-time"},"lchG_TIME":{"type":"string","format":"date-time"},"vfD_DATE":{"type":"string","format":"date-time"},"pstD_DATE":{"type":"string","format":"date-time"},"entrY_DATE":{"type":"string","format":"date-time"},"traN_DATE":{"type":"string","format":"date-time"},"valuE_DATE":{"type":"string","format":"date-time"},"tranid":{"type":"string","nullable":true},"particulars":{"type":"string","nullable":true},"tranremarks":{"type":"string","nullable":true},"dr":{"nullable":true},"cr":{"nullable":true},"balance":{"type":"number","format":"double"},"parT_TRAN_SRL_NUM":{"type":"integer","format":"int32"},"instrmnT_NUM":{"type":"string","nullable":true},"gL_DATE":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementResponse"},"nullable":true}},"additionalProperties":false,"title":"WemaGetAccountStatementResponseListResponseHandler"}}}}}}
>
  
</StatusCodes>


      