---
id: create-connector-balanceinquiry
title: "Create connector connector balance inquiry"
description: "Create connector connector balance inquiry"
sidebar_label: "Create connector connector balance inquiry"
hide_title: true
hide_table_of_contents: true
api: eJztWMtu2zAQ/BVjT30wcdujbokRJDm0NeL0ZBjFmtrYTCiSISm3hqB/L1ZUZKXOQe4DyMG+mKKWo+XsiN5xBdaRx6isuc4hA+kJI02sMSSj9eeo0UhS5rFUfgsCIq4CZHPoImAhwKHHgiJ5vlWBwYIYqgsRoAxk4DCuQYAnBqMcsuhLEhDkmgqErIK4dbwwRK/MCupadFiXF59nZ99ur56g1oQ5+YPAFimYQjy3+ZYjpDWRTOQhOqeVbGgY3wdreG4Pyi7vSUYQ4DyTFhWFZq2UtjTxS1ksye8/WYAptcalppRjLboVzd4GxC83ZlgcmoeJzYeAchZ5rnjDqKe9/dyhDiQgqsix0ArgOgngJhHIpYFIP+ORq2Fc9eX17v2RtAGkMYCn4KwJiYVPHz7wV05BeuUYFzKYlVJSCCD6L3OjTKdRHcRyaKF2gUtrNaGBmtEHbVpAQSHgalhsjnFIXhRCezoPwIweTUAZh684VCt/oMYNqmamrfV/VE/SC1cskN8oSVNvNyofmGhUBc0iFu6l6DvrC4yQcdnohEPhL9P8cj39/jS+QpNr8nunxaFnxVHFRxW/DhX/UX9wlO9Rvq9Bvk378bzVmDTOaNS5mt5omZBHO5tUUFxb9lPOhkZNbH0yGKNT427duOqG9bjFONlhMHlPlqr0ml1PjC5k43GgGDUVZOJJTpvTFRXB4TZYXXKup9IWwHaHX76bneW5+ImF0/RCR7nj95kOd9NNf9i77NrAzl6xL7uzTclaui9TVqNZl+zobHoNAnhTidLNR17IFBXYHBOt2zuI6mdV6hTT6wFrkeir2irMAZ1KHWPnT7P+xe+lWAhYcxmzOVTVEgN987quefqxJL+FbL4QsEGvko7nFeQq8DjvNPhbkl2zCm9uWgP7dsTu+qXk20k0vNsN6pKvQMADbZ+57HpRiydj/M+TSA/r2fAuEXbp6e4kAZ7cMsAuYq+d2a04k5Jc7MX2H7rovUbTr7Nbrkzr3IskPo8/+C8A/JEIsc3WmvO6matAo1mVzS8BpCfz5xdZ+/lH
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create connector connector balance inquiry"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/connector/{connector}/balance-inquiry"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create connector connector balance inquiry

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"connector","in":"path","required":true,"schema":{"type":"string"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"accountName":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"BalanceInquiryRequest"}},"text/json":{"schema":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"accountName":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"BalanceInquiryRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"accountName":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"BalanceInquiryRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"availableBalance":{"type":"string","nullable":true}},"additionalProperties":false,"title":"BalanceInquiryResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"BalanceInquiryResponseNIP_ResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"availableBalance":{"type":"string","nullable":true}},"additionalProperties":false,"title":"BalanceInquiryResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"BalanceInquiryResponseNIP_ResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"availableBalance":{"type":"string","nullable":true}},"additionalProperties":false,"title":"BalanceInquiryResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"BalanceInquiryResponseNIP_ResponseHandler"}}}}}}
>
  
</StatusCodes>


      