---
id: add-settlementaccount-add
title: "Add SettlementAccount add"
description: "Add SettlementAccount add"
sidebar_label: "Add SettlementAccount add"
hide_title: true
hide_table_of_contents: true
api: eJztVslu2zAQ/RVjTl3oOKvT6uYGQZpDWyNOToYPY3FkM6FEhqScGIL+PRhK8VIbRdBTDtFFAmfh6L3HGVZgLDkMyhTXEhJAKUcUgqacioBpasoiDKQEAQFnHpIxrM2DxgwTARYd5hTIsUsFBeYECVxd/hoN7m5/ggBVQAJzQkkOBDh6LJUjCUlwJQnw6ZxyhKSCsLQc6YNTxQzqetI4kw8/jFyyR2qKQEXgT7RWqzQW37v3puC1nVRmek9pAAHW8a8GRZ6tUyweLoyk3U0FFKXWONXUlFeL6Pw7/tMbnVto/ivGvClC+aFTObrlhvfUGE1YQC3Ar1gaOpMpTczubtrMuBwDJFCWSkJdCxaAYkBRDzfwylB7EhBU4BJgsKmStu6bhqaYJNBz+GDkPTGyeVS+fP2g5t1Qw2kceWsK32B9fHjIL0k+dcpydkhgVKYpeR833bYMpOzspO9gbNk5hbnhrm6Nj6RimEMCPbSqtxPTa2I8ucVrGy+d5q4dgvVJrzej3Jd25lDSAX+n2pTyAK0F7tIsqZt1p758xtxq2hbPGsq1RrbXtqSw32Q2DRvEtpNkH5VwkuG3s6x/2j07Pzrvnp71j7vTkyztHqff+ydZv48Z9lkIqsgaQbXUXVHuLS43AO4MhtcggCFq8F8ccSADnGM8U+3s+xcvWwyudBabptWooiYj9lVL2RjQKhB7Zm/UIE/gOVOcjKGqpujpzum65uXHkhic8UTAAp1qTsV4UovXYcw8S+XZIFfS/avA1ciFTzft5P7c4SvBvsIfaLk9+heoS3aDWlSt9aJJ2L3lBGuPnXnOwlqJePhndMtaaO8BeSMph098ocAn3kGAiUXHgxTXKtBYzEqcsW+Tk58XzN0eug==
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Add SettlementAccount add"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/SettlementAccount/add"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Add SettlementAccount add

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"settlementProfileId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementAccountRequest"}},"text/json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"settlementProfileId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementAccountRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"settlementProfileId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementAccountRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      