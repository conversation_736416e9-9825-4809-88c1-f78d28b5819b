---
id: add-settlementaccount-add
title: "Add SettlementAccount add"
description: "Add SettlementAccount add"
sidebar_label: "Add SettlementAccount add"
hide_title: true
hide_table_of_contents: true
api: eJztVslu2zAQ/RVjTl3oOKvT6uYGQZpDWyNOToYPY3FkM6FEhqScGIL+PRhK8VIbRdBTDvFFBmfh8L3HGVZgLDkMyhTXEhJAKUcUgqacioBpasoiDKQEAQFnHpIxrM2DxgwTARYd5hTIsUsFBeYECVxd/hoN7m5/ggBVQAJzQkkOBDh6LJUjCUlwJQnw6ZxyhKSCsLQc6YNTxQzqetI4kw8/jFyyR2qKQEXgv2itVmksvnfvTcFrO6nM9J7SAAKs46MGRZ6tUyweLoyk3U0FFKXWONXUlFeL6Pw7numNzi00/xVj3hSh/NCpHN1yw3tqjCYsoBbgVywNncmUJmZ3N21mXI4BEihLJaGuBQtAMaCohxt4Zag9CQgqcAkw2FRJW/dNQ1NMEug5fDDynhjZvCpfvn5Q826o4TSOvDWFb7A+PjzkjySfOmU5OyQwKtOUvI+bblsGUnZ20ncwtuycwtxwV7fGR1IxzCGBHlrV24npNTGe3OK1jZdOc9cOwfqk11uj1JW0OJhR7i0uvdEll3KQmhy4XbO2btYt+/IZc6tpW0VrTNdi2V7b0sR+k9k0bDDcjpR9nMJJht/Osv5p9+z86Lx7etY/7k5PsrR7nH7vn2T9PmbYZ0WoImuU1XJ41Rx2A+nOYHgNAhirhojFEQcy0jnGy9UOwX8RtEXlSnCxe1qNKoozklC13I0BrQKxZwhHMfIonjPXyRiqaoqe7pyua15+LInBGU8ELNCp5nqMJ7V4ncpMuFSeDXKl4b8KXM1e+HTTjvDPHX4b7Cv8gZbbb4AF6pLdoBZVa71oEnZvOcHaY2ews7BWah7+Gd2yFtoHQd5IyuETvyzwiXcQYGLR8UbFtQo0FrMSZ+zb5OTfCxEUIoU=
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Add SettlementAccount add"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/SettlementAccount/add"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Add SettlementAccount add

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"settlementProfileId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementAccountRequest"}},"text/json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"settlementProfileId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementAccountRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"settlementProfileId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementAccountRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      