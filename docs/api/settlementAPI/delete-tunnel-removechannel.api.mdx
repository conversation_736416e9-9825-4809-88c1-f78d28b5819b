---
id: delete-tunnel-removechannel
title: "Delete tunnel settlementTunnelId remove channel channelSettlementId"
description: "Delete tunnel settlementTunnelId remove channel channelSettlementId"
sidebar_label: "Delete tunnel settlementTunnelId remove channel channelSettlementId"
hide_title: true
hide_table_of_contents: true
api: eJztVstu3DAM/BWDpxZQ6rRH3wJkkQRogSCP02IPXIvZVSpLikQvujD07wVt7yOJDymQ9JSTH6JnyBHFcQc+UEQ23l1pqECTJaa71jmyN9T4DdVrlAdQwLhKUM3hlpgtNeR4iIOFgoARG2KKEtGBw4aggvQi8kqDAuOggoC8BgWRnloTSUPFsSUFqV5Tg1B1wNvQI3A0bgUKHnxskKGCtjUaclZ7kjHBQ1YfwnIx+3V7dn93uYNeE2qK/wCe80KCU/AuUZL1H6enctGU6miCbAFUcNvWNaUECmrvmBz3SPSHy2BRmLvXDH75SDWDghBlN9kM+GmEOgQuvbeEDrKChlLCFU2J4FprcWlpqCgr0MhvoDP6TYoCam2kWLTXR98/oE2kgA0LMZz3ffiy025G+d4LZ3e9RKctxQE2BGvq/kSUj8l/Kv7hivfd/Sn1x0udBfn5uBkgCu6/LF7P6yL2JlCMQ7aYHrYN8dof7EPUk8lbQYnBlAN42b1Gz+UAfzLClt0EfgYFieJm5y1ttDJ/mUOqyvIAeqJp821FTQq4Td62UuG32jcgg9e4B9/v4CjdxRBXHIiKs+srUCA0gzSb79JKwSdusG/N0QneR7Jn27DvrKNBn9VQaTdqOQcMRly4He24mnTX54JK2BT/QsHaJxbUrltiovtoc5bXTy3FLVTzhYINRjMclXkH2iS51/smfFHA3q3gy83oiF8LSXeqsPEluq0ojraVJ1Dwm7bTfw1ixP8xgynJ8iKrneu/uyAD7dE/xj4lqXxYPatrCny0dgyxODqE57Ofs7sZ5PwXlU57IA==
sidebar_class_name: "delete api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete tunnel settlementTunnelId remove channel channelSettlementId"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/tunnel/{settlementTunnelId}/remove-channel/{channelSettlementId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete tunnel settlementTunnelId remove channel channelSettlementId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementTunnelId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"channelSettlementId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponse"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponse"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponse"}},"additionalProperties":false,"title":"DeleteSettlementTunnelResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      