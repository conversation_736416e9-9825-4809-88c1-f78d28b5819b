import type { SidebarsConfig } from "@docusaurus/plugin-content-docs";

const sidebar: SidebarsConfig = {
  apisidebar: [
    {
      type: "category",
      label: "AutoSettlementChargeSetup",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/get-all-auto-settlement-charge-setups",
          label: "Get all auto settlement charge setups",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-auto-settlement-charge-setups-by-merchant",
          label: "Get auto settlement charge setups by merchant settlement profile",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/add-auto-settlement-charge-setup",
          label: "Add new auto settlement charge setup",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-auto-settlement-charge-setup-by-id-and-merchant",
          label: "Get auto settlement charge setup by ID and merchant",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/update-auto-settlement-charge-setup",
          label: "Update auto settlement charge setup",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/settlementAPI/delete-auto-settlement-charge-setup",
          label: "Delete auto settlement charge setup",
          className: "api-method delete",
        },
      ],
    },
    {
      type: "category",
      label: "BackgroundJob",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/trigger-background-settlement",
          label: "Trigger background settlement job",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "BankReconciliation",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/import-bank-statement",
          label: "Import bank statement for reconciliation",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-all-bank-statements",
          label: "Get all bank statements",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-bank-statement-by-id",
          label: "Get bank statement by ID",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-bank-statement-lines",
          label: "Get bank statement transaction lines",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-bank-statement-by-reference",
          label: "Get bank statement by reference",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "BatchPeriod",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/add-batch-period",
          label: "Add new batch period",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-all-batch-periods",
          label: "Get all batch periods",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-batchperiod-get",
          label: "Get BatchPeriod Get batchPeriodId",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-batchperiod-get-code",
          label: "Get BatchPeriod Get batchPeriodCode Code",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/update-batchperiod-update",
          label: "Update BatchPeriod Update batchPeriodId",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/settlementAPI/delete-batchperiod-remove",
          label: "Delete BatchPeriod Remove batchPeriodId",
          className: "api-method delete",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-batchperiod-getdefault",
          label: "Get BatchPeriod GetDefault",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/update-batchperiod-switchdefault",
          label: "Update BatchPeriod SwitchDefault",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/settlementAPI/create-batchperiod-subscribe-merchantprofile",
          label: "Create BatchPeriod Subscribe batchPeriodId MerchantProfile settlementProfileId",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/update-batchperiod-update-merchantprofile",
          label: "Update BatchPeriod Update MerchantProfile settlementProfileId",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-batchperiod-merchantprofile",
          label: "Get BatchPeriod MerchantProfile settlementProfileId",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "ChargeRevenue",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/get-chargerevenues",
          label: "Get charge revenues",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-chargerevenues",
          label: "Get charge revenues recordId",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/create-chargerevenues-logsettledrevenue",
          label: "Create charge revenues log settled revenue",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "Connector",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/create-connector-balanceinquiry",
          label: "Create connector connector balance inquiry",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/create-connector-nameinquiry",
          label: "Create connector connector name inquiry",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/create-connector-transferfunds",
          label: "Create connector connector transfer funds",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/create-connector-transactionstatusrequery",
          label: "Create connector connector transaction status requery",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "FundReversal",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/get-fundreversals",
          label: "Get fund reversals",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-fundreversals-recordid",
          label: "Get fund reversals recordId",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/create-fundreversals-reverse",
          label: "Create fund reversals transactionId reverse",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/create-fundreversals-retry",
          label: "Create fund reversals unprocessedReversalId retry",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "FundTransferTrace",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/get-fundtransfertrace-all",
          label: "Get FundTransferTrace all",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "GemspayBankAccount",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/add-gemspaybankaccount-add",
          label: "Add gemspay bank account add",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-gemspaybankaccount-all",
          label: "Get gemspay bank account all",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-gemspaybankaccount",
          label: "Get gemspay bank account id",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/update-gemspaybankaccount",
          label: "Update gemspay bank account id",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/settlementAPI/delete-gemspaybankaccount",
          label: "Delete gemspay bank account id",
          className: "api-method delete",
        },
        {
          type: "doc",
          id: "api/settlementAPI/update-gemspaybankaccount-activedeactivate",
          label: "Update gemspay bank account id active deactivate",
          className: "api-method patch",
        },
      ],
    },
    {
      type: "category",
      label: "HealthCheck",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/get-healthcheck",
          label: "Get health check",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-healthcheck-bugsnag",
          label: "Get health check bugsnag",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "ServiceProviderNipConnectorMap",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/get-serviceproviderconnectormaps",
          label: "Get service provider connector maps",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/create-serviceproviderconnectormaps",
          label: "Create service provider connector maps",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-serviceproviderconnectormaps",
          label: "Get service provider connector maps id",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/update-serviceproviderconnectormaps",
          label: "Update service provider connector maps id",
          className: "api-method patch",
        },
      ],
    },
    {
      type: "category",
      label: "ServiceSetup",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/get-servicesetups",
          label: "Get service setups id",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/update-servicesetups",
          label: "Update service setups id",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-servicesetups",
          label: "Get service setups slug",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/create-servicesetups",
          label: "Create service setups",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-servicesetups",
          label: "Get service setups",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "Settled",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/get-settled",
          label: "Get settled settledId",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settled-all",
          label: "Get settled all",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settled-merchant-aggregated",
          label: "Get settled merchant merchantId aggregated",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settled-merchant",
          label: "Get settled merchant merchantId",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settled-merchant-list",
          label: "Get settled merchant merchantId list",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settled-merchant",
          label: "Get settled settledId merchant merchantId",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "Settlement",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/get-settlements-all",
          label: "Get settlements all",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settlements",
          label: "Get settlements settlementId",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settlements-merchant",
          label: "Get settlements merchant merchantId",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settlements-merchant",
          label: "Get settlements settlementId merchant merchantId",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/create-settlements-settlemerchant",
          label: "Create settlements settle merchant merchantId",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/create-settlements-retry",
          label: "Create settlements settlementId retry",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/create-settlements-marksettlementlogassettled",
          label: "Create settlements mark settlement log as settled",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "SettlementAccount",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/add-settlementaccount-add",
          label: "Add SettlementAccount add",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settlementaccount",
          label: "Get SettlementAccount settlementAccountId",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/update-settlementaccount",
          label: "Update SettlementAccount settlementAccountId",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/settlementAPI/delete-settlementaccount",
          label: "Delete SettlementAccount settlementAccountId",
          className: "api-method delete",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settlementaccount-merchant",
          label: "Get SettlementAccount settlementAccountId merchant merchantId",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/update-settlementaccount-merchant",
          label: "Update SettlementAccount settlementAccountId merchant merchantId",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/settlementAPI/delete-settlementaccount-merchant",
          label: "Delete SettlementAccount settlementAccountId merchant merchantId",
          className: "api-method delete",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settlementaccount-all",
          label: "Get SettlementAccount all",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settlementaccount-merchant-all",
          label: "Get SettlementAccount merchant merchantId all",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "SettlementMapping",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/add-settlementmapping-add",
          label: "Add SettlementMapping add",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settlementmapping",
          label: "Get SettlementMapping settlementMappingId",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/update-settlementmapping",
          label: "Update SettlementMapping settlementMappingId",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/settlementAPI/delete-settlementmapping",
          label: "Delete SettlementMapping settlementMappingId",
          className: "api-method delete",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settlementmapping-all",
          label: "Get SettlementMapping all",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settlementmapping-merchant-all",
          label: "Get SettlementMapping merchant merchantId all",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "SettlementMode",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/get-settlementmodes",
          label: "Get settlement modes",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/create-settlementmodes-code-merchant",
          label: "Create settlement modes settlementMode code settlementModeCode merchant merchantId",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "SettlementProfile",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/get-settlementprofile-all",
          label: "Get SettlementProfile all",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/create-settlementprofile-merchant",
          label: "Create SettlementProfile merchant merchantId",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settlementprofile-merchant-profile",
          label: "Get SettlementProfile merchant merchantId profile",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/update-settlementprofile-merchant-profile",
          label: "Update SettlementProfile merchant merchantId profile profileId",
          className: "api-method patch",
        },
      ],
    },
    {
      type: "category",
      label: "SettlementSplitCode",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/add-settlementsplitconfig-add",
          label: "Add settlement split config add",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settlementsplitconfig",
          label: "Get settlement split config settlementSplitConfigId",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/update-settlementsplitconfig",
          label: "Update settlement split config settlementSplitConfigId",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/settlementAPI/delete-settlementsplitconfig",
          label: "Delete settlement split config settlementSplitConfigId",
          className: "api-method delete",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settlementsplitconfig-all",
          label: "Get settlement split config all",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settlementsplitconfig-merchant-all",
          label: "Get settlement split config merchant merchantId all",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/add-settlementsplitconfig-splitaccount-add",
          label: "Add settlement split config settlementSplitConfigId SplitAccount add",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/update-settlementsplitconfig-splitaccount",
          label: "Update settlement split config settlementSplitConfigId SplitAccount splitAccountId",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/settlementAPI/delete-settlementsplitconfig-splitaccount",
          label: "Delete settlement split config settlementSplitConfigId SplitAccount splitAccountId",
          className: "api-method delete",
        },
      ],
    },
    {
      type: "category",
      label: "SettlementSplitConfigAccount",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/add-settlementsplitconfigaccount-add",
          label: "Add SettlementSplitConfigAccount add",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settlementsplitconfigaccount",
          label: "Get SettlementSplitConfigAccount settlementSplitAccountId",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/update-settlementsplitconfigaccount",
          label: "Update SettlementSplitConfigAccount settlementSplitAccountId",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/settlementAPI/delete-settlementsplitconfigaccount",
          label: "Delete SettlementSplitConfigAccount settlementSplitAccountId",
          className: "api-method delete",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-settlementsplitconfigaccount-all",
          label: "Get SettlementSplitConfigAccount all",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "SettlementTunnel",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/add-tunnel-add",
          label: "Add tunnel add",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/update-tunnel-activatedeactivate",
          label: "Update tunnel settlementTunnelId activate deactivate",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-tunnel",
          label: "Get tunnel settlementTunnelId",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/update-tunnel",
          label: "Update tunnel settlementTunnelId",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/settlementAPI/delete-tunnel",
          label: "Delete tunnel settlementTunnelId",
          className: "api-method delete",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-tunnel-all",
          label: "Get tunnel all",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/settlementAPI/add-tunnel-addchannel",
          label: "Add tunnel settlementTunnelId add channel",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/update-tunnel-updatechannel",
          label: "Update tunnel settlementTunnelId update channel channelSettlementId",
          className: "api-method patch",
        },
        {
          type: "doc",
          id: "api/settlementAPI/delete-tunnel-removechannel",
          label: "Delete tunnel settlementTunnelId remove channel channelSettlementId",
          className: "api-method delete",
        },
      ],
    },
    {
      type: "category",
      label: "WemaNIPService",
      items: [
        {
          type: "doc",
          id: "api/settlementAPI/create-wemanipservice-getbalance",
          label: "Create WemaNIPService GetBalance",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/create-wemanipservice-resendfunds",
          label: "Create WemaNIPService ResendFunds",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/create-wemanipservice-getstatement",
          label: "Create WemaNIPService GetStatement",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/create-wemanipservice-nameinquiry",
          label: "Create WemaNIPService NameInquiry",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/settlementAPI/get-wemanipservice-nipbanks",
          label: "Get WemaNIPService NipBanks",
          className: "api-method get",
        },
      ],
    },
  ],
};

export default sidebar.apisidebar;
