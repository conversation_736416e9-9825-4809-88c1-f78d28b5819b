---
id: delete-settlementsplitconfig
title: "Delete settlement split config settlementSplitConfigId"
description: "Delete settlement split config settlementSplitConfigId"
sidebar_label: "Delete settlement split config settlementSplitConfigId"
hide_title: true
hide_table_of_contents: true
api: eJytk01v2zAMhv+K8Z42QG26HXUr1mALsAHD0p6CHFSLiYXZkirRwQJD/32gnY+uawZs2MmySL0kH5IDQqRk2AW/sNCw1BLTkphb6shzjq3jOviN20KBzTZDr3C2L8X+IVjCWiGaZDpiSuI0wJuOoJFfOovYwkLBeWhEww0UEj31LpGF5tSTQq4b6gz0AN7HUYaT85LEJqTOMDT63lmUok6RPs6/LG8f7j8dpRsyltJfiJeyFuccg8+Uxf7+5kY+lnKdXBRM0Fj2dU05S+wXlrsRX3UuuRoBVhPB6jKKjrgJ5wZATVw0Zia62fnd1ah3NenNhguCBQqZ0u7YiT61goM5Zj17rmZpd72lLkezz6HtpYrrOnQQDs5vwojIcTvSnfyqc/Or268LKEiYqfzdOxSFGDJ3xsvbQ2P+GcsvdE/dYvrBs9ga5yXcWNxw4LWCiW6s/lViUNCXoq0VmpBZNIbh0WR6SG0pcv3UU9pDr9YKO5OceRQeqwHWZTlb6I1pM/2Wbh08k5dRffPtMIFvK1mj18o4XBq/F6Sm7eUPCt9p/4clKuuijnP+31OaQj/bqlNaMh+nkb2bf57fz1HKT8KggEM=
sidebar_class_name: "delete api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete settlement split config settlementSplitConfigId"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/settlement-split-config/{settlementSplitConfigId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete settlement split config settlementSplitConfigId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementSplitConfigId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      