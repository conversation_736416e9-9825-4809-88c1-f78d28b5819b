---
id: delete-settlementsplitconfig
title: "Delete settlement split config settlementSplitConfigId"
description: "Delete settlement split config settlementSplitConfigId"
sidebar_label: "Delete settlement split config settlementSplitConfigId"
hide_title: true
hide_table_of_contents: true
api: eJytk01v2zAMhv+K8Z42QK27HXUr1mALsAHD0p6CHFSLSYTZkirRwQJD/32gnY+tbQZs2MmySL0kH5IDQqRk2AU/t9Cw1BLTgphb6shzjq3jJvi120CBzSZDL3G2L8T+IVjCSiGaZDpiSuI0wJuOoJGfO4vY3ELBeWhEw1soJHrqXSILzaknhdxsqTPQA3gfRxlOzksS65A6w9Doe2dRijpF+jj7srh9uP90lN6SsZT+QryUlTjnGHymLPb3NzfysZSb5KJggsaibxrKWWI/s9yN+KpzydUIsJoIVpdRdMTbcG4A1MRFozbR1ed3V6Pe1aRXDxcECxQypd2xE31qBQdzzLquN9TlPm6SsXQt56YNvb02MULKd34dRjKO2xEqdTmafXXueXX7dQ4FUZ+q3r1DUYghc2e8vD30459p/Ab11CSmH1zH1jgv4caahgOmJUx0Y9GvgoKCvhRtpbANmUVjGB5NpofUliLXTz2lPfRypbAzyZlH4bEcYF2Ws4VemzbTi3Sb4Jm8TOibb4fBe1vJ9rxWxuHS+L0gNW0vf1D4Tvs/7E5ZFXUc7/+e0hT6l2U6pSXzcZrUu9nn2f0MpfwEuY18eA==
sidebar_class_name: "delete api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete settlement split config settlementSplitConfigId"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/settlement-split-config/{settlementSplitConfigId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete settlement split config settlementSplitConfigId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementSplitConfigId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      