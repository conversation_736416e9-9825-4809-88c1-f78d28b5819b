---
id: update-tunnel
title: "Update tunnel settlementTunnelId"
description: "Update tunnel settlementTunnelId"
sidebar_label: "Update tunnel settlementTunnelId"
hide_title: true
hide_table_of_contents: true
api: eJztWEtv2zAM/isBT3uoy7ajb1k3dD0MKPo4FTkwFpNokyVNorMFhv/7QCsPtwkGdyiwQ5OLHZkiqY/UZ+trwAeKyMa7Sw0F1EEj023tHFlQwLhIUNzDDTFbqsjx5tFUQcCIFTFFsWjAYUVQQHpkealBgXFQQEBegoJIP2sTSUPBsSYFqVxShVA0wOvQeeBo3AIUzH2skCWp2mhoW7ULcvHl283k7vbr1vWSUFN8gvO2nWZjSvzJ67VYlN4xOZZbDMGaskNl/D15J2MHrvzsO5UMCkIUDNlQkqc5xcPVuNpanFnKqbUKNKUymiBBBtknWy8GGLYKUGsjbtFe9VKbo02kgA2LLdx1lX5c2OuMiaANTL/5BS+/3wRv3r5UHMRRpBS8S3lhH9+/l8uDtOGmLktKCVR/F3X9EyyaJwGXNq72hjPvLaGDVkFFKeFiILDIA8IZPYB61H8vpwIs2azoOCxlJGTSE/7rWqTQZ2wq6pD02szN0+aUS5Tm6NcGY8S10DBTlZ4N7U2gQbgEjOwonns9rEAmfaY51paPQ2nS5H8DPXTrXhCfZ6D22/d6s1W7nv03RrggPqSDrddncLK9fkWnLcUDrn0q054I40QYJ8J4YYTxT9+mJ6Y4McWJKV4UU3Tnl4dnlXziGXE3c3RUr6iIl15nzaIU0aLTLgoYYzDjPHHcHM5sQUGiuNpKInW0Ik4wh1SMxwuqUh0WETW9k/vS+lq/wxBAxAjhsuu9IPHlN1bB0v4kua/rw9XshvNu3wkcoozMfdcXO9yqFHA92oM3mlxdggLJNztbfZCJwSeu0PViD4DsODn1joCtyng0GzDvAYMRiWmrNRVH/E4VLH1isW6aGSa6i7ZtZfhnTXENxf1UwQqjyc1434A2Se71rm0eJbY7n8Kr641Y9HokaRxLeMtETnhohbaWf6DgB62PS13ttFVbNerZs8lRe9rXLiORxvLT8+zw7FYc7C0Ovq/3MyZlSYF7tv2g095euJrcnkvU2UYvqzpahIi/RHjDXxka362tY+RurAGLblF3b1bIoeX3B3HkK8E=
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update tunnel settlementTunnelId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/tunnel/{settlementTunnelId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update tunnel settlementTunnelId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementTunnelId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateSettlementTunnelRequest"}},"text/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateSettlementTunnelRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateSettlementTunnelRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      