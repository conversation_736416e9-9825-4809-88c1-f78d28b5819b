---
id: update-tunnel
title: "Update tunnel settlementTunnelId"
description: "Update tunnel settlementTunnelId"
sidebar_label: "Update tunnel settlementTunnelId"
hide_title: true
hide_table_of_contents: true
api: eJztWE1v2zgQ/SvGnLpdpm73qJs3W6Q5LBDk4xT4MBbHNrsUyZIjt4ag/16MaFtKbCyUIkAPsS+SqeHM8M3wSXwN+EAR2Xh3raGAOmhkuq+dIwsKGFcJike4I2ZLFTnePZorCBixIqYoFg04rAgKSM8srzUoMA4KCMhrUBDpW20iaSg41qQglWuqEIoGeBs6DxyNW4GCpY8VsiRVGw1tqw5Brj7/ezd7uP+yd70m1BRf4Lxt59mYEv/t9VYsSu+YHMsthmBN2aEy/Zq8k7EjV37xlUoGBSEKhmwoydOc4vFqXG0tLizl1FoFmlIZTZAgo+yTrVcjDFsFqLURt2hvBqkt0SZSwIbFFh66Sj8v7G3GRNAGph/8hpc/bIL3f75VHMRRpBS8S3lhf338KJcnacNdXZaUEqjhLur6J1g0LwIu7Vz1hgvvLaGDVkFFKeFqJLDII8IZPYJ61G8vpwIs2WzoNCxlJGTSM/7ftUihL9hU1CHptVmal80p1yjNMawNxohboWGmKr0a2rtAo3AJGNlRvPR6XIFM+oeWWFs+DaVJs98N9Nite0V8mYHqt+/tbqt2PftrjHBFfEwHe6+v4GR//YJOW4pHXPtSpj0TxpkwzoTxxgjjl75Nz0xxZoozU7wppujOL0/PKvnEM+Fu5uSkXlERr73OmkUpokWnXRQwxWCmeeK0OZ7ZgoJEcbOXROpoRZxgDqmYTnv7C02bDyuqUsBt8raWxD6UvgJRJYTUbntl4vMPrIKl/kjZF/jpsg7DedsflA6RSJa+a5ADgF3kSY/iZHZzDQok8exs80kmBp+4QjeIPQK70yw1OAu2KgPT7FB9BAxGtKa96FSc8DtXsPaJxbppFpjoIdq2leFvNcUtFI9zBRuMJnflYwPaJLnXh/55ltjhoArvbneq0R8TSeNUwntKckJIG7S1/AMF/9H2tObVzlu1l6VePZscdSCCHTISjSw/vcwOL+7FQW9x9KHdz5iVJQUe2A6Dzgeb4mZ2fylRFzvhrOr4ESJ+FwUOv2dofLe2jpq7sQYsulXdvWIhh5bfT0HYL4w=
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update tunnel settlementTunnelId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/tunnel/{settlementTunnelId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update tunnel settlementTunnelId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementTunnelId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateSettlementTunnelRequest"}},"text/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateSettlementTunnelRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateSettlementTunnelRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      