---
id: update-settlementaccount-merchant
title: "Update SettlementAccount settlementAccountId merchant merchantId"
description: "Update SettlementAccount settlementAccountId merchant merchantId"
sidebar_label: "Update SettlementAccount settlementAccountId merchant merchantId"
hide_title: true
hide_table_of_contents: true
api: eJztVk1v2zAM/SsBT/tQ625H37Ki6HroUPTjFOTAWGyiVpZUSU4bGP7vAy0ndhoPWIFup/piR6IeyUfqMTVYRx6jsuZCQg6VkxjphmLUVJKJWBS2MvGSfLFCE0FAxGWAfAa9zTTZwFyAQ48lRfJsUoPBkiCH8Nr0QoIAZSAHh3EFAjw9VcqThDz6igSEYkUlQl5D3LgWInplliDg3voSI0daKQlNI3Zeyi7GfwJ+fnZ5M727/bmFXhFK8m8Ab5p5MqYQf1i5YYvCmkgm8ic6p1XR1iF7CNbw2gGUXTxQwTVwnqsWFQXeXaB5PLWSxjIylda40JTCa0Rr3FXhV5vaG8/YvzjRCEApFSeD+moQ6z3qQAKiimwLd6+arfNxnVhi/iHSS/wgZEDIsFG+fP1gZssMI3kKzpqQUv1+csIvSaHwyrEDyOGmKgoKoWVyfyc5mBx4mIyI12QrNZM9zSkprqxMulOw8LT6k0OGTmUHwFk9gtxkW8Ss7rEbEBDIr7eyWnnNEhSjC3mW9TBHktbHSyqDw02wuuLUjgtbAmsPt8p1rz9nL1g6TftN0ZfpoPbjW3YgcKyM96nmXdHOUygDUifTqwsQwJkk2tff+KCzIZbYdnKnt+9Qjr367hqxFRSnURl23DJZd4WaAToFYmSyCcjHZ1jZz8V84HwuYGVDZMi6XmCgO6+bhpefKvIbyGdzAWv0Kt2EWQ1SBf6Wu8Z/Ff1uVMCn627ifJ7wNB7LqltEs2GuUVf8CwQ80uYP05gn3X8MYUBVM2/Edpq+OxHJ22B27yLhhNPuaQI8umWA3uJgIPMd2t3wq+ntKeMtuklepuvj8Zn/EuBzSta2Ubdq1K7VoNEsK1yybQLl5zeXRla9
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update SettlementAccount settlementAccountId merchant merchantId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/SettlementAccount/{settlementAccountId}/merchant/{merchantId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update SettlementAccount settlementAccountId merchant merchantId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateSettlementAccountRequest"}},"text/json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateSettlementAccountRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateSettlementAccountRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      