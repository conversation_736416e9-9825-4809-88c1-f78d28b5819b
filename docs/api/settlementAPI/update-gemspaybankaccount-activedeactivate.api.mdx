---
id: update-gemspaybankaccount-activedeactivate
title: "Update gemspay bank account id active deactivate"
description: "Update gemspay bank account id active deactivate"
sidebar_label: "Update gemspay bank account id active deactivate"
hide_title: true
hide_table_of_contents: true
api: eJztW0tv2zgQ/ivGnPZB19096uYEQRqg7QZ5nAIfxuLYZi2RKjlyaxj67wuSsiPH7kIOgkXaMBc59sxw+HHmI0fgbMBUZJGV0VcSMqgriUyXVLoK11PUS8xzU2se56xWJAn9E5lAAOPcQfYArfAZ6uU4CsNEQIUWS2KyXmYDGkuCDJQEAUpDBhXyAgRY+lorSxIytjUJcPmCSoRsA7yuvIZjq/QcBMyMLZG9i7WS0DRiZ/Ty4tPt+P7uw9b0glCSPcF400yiMDk+M3LtJXKjmTT7j1hVhcoDRqMvzmj/3YEpM/1COYOAynpEWZELugG3jtzUmIJQQ+OdVayQjf0c5nE4ZV0XBU4Liv53NS5KVEUPlUYASqm861hcdzybYeFIACv2snDfXfbOSsZlv2Vkuon4eOSB6TsnKAIU3eD448+ESdN4q5ZcZbSLk/z7/Xv/kORyqyo/GGRwW+c5OQeim2khrqoC1UkgutbUURRLcg7n/fCTyD2GU7IHPQloibP34m3l63JKtpeGp+dzI/uZ98K9fVmu84+0oj4hJGC60r3kStQyBMqMLOmcTphpFOghWNW2Mq6frKUS7bKfWVyXpPl8gVpT0RvzfbXe6O+r3Rb1vKeaZU392aKVP2E2Qf7qaPgfCecfM1uBjj8ZqWaK5Nm6l7l9lb78JiC3hExyzP+Zsz4uh6xKCozRDnOKTr5APaePZt5lIbQW1/5QwlS6F+OVytJVuc9oiagSUf1ERPWWqKHvCeuS+PB49Y8u1jftSSqkvnGccj/lfsr9t5f7/0M1KNq3P58N92WDWMz9fIA/Rft8e37rQv7MYvnocj7afREz2+cH1LIge/Am4tT3EKmETrtT2p1SCZ1K6ERUiaheP1G9JWpIJXTK/ZT7KfdTCf2qAP8VS+hn3WtItXPaltK2lGrnVDsnokpE9fqJ6i1RQ6qdU+6n3E+5n2rnVwX4r1c7h1vf+ze846XxwTxaGXhyHbQEPlByEFNxsNdSUhIvjIzdIblvDwldIhmMsFKj1tDQGxq2hkYbJZtRNDXcM+XIrrbNJ7UtfFsIc+Wy0cgRc0GePYaSVu9as84UtXf8XW5K8P0g/jXAzWNPyMV3LKsiBOaWRDg0lTzJjMcQeZoA219CbsxMiKr9iBjc7lwbjK+vQICfQkRz9dd2Cy4xxHzb/PIMlPeWaRfanbv3PhVtSNmI/wNgpUDAsRUAAVno6jlchImAhXHs9TebKTq6t0XT+K+/1mTXkD1MBKzQqhjlDxuQyvnPcheNT1zdtQrAbzdtb8/vAxDHp7Atg7QvglZY1P4/ELCkdexEaiaN2DYLvfjocZROa9LOA9+5FH89jwaHd97Ao8TBRY5HjXGeU8Ud2e6gk04CXY/vzv2o07adqQz7MVj85vui8FuEwoS5hRNb+G4DBep5HY54EIf2f/8C1+4LtA==
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update gemspay bank account id active deactivate"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/gemspay-bank-account/{id}/active-deactivate"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update gemspay bank account id active deactivate

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"id","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"active":{"type":"boolean"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateGemspayBankAccountActiveStateRequest"}},"text/json":{"schema":{"type":"object","properties":{"active":{"type":"boolean"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateGemspayBankAccountActiveStateRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"active":{"type":"boolean"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateGemspayBankAccountActiveStateRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"partnerName":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"partnerId":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"changeLogs":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"preImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"postImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true},"updateNote":{"type":"string","nullable":true},"action":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GemspayBankAccountChangeLogResponse"},"nullable":true}},"additionalProperties":false,"title":"GetGemspayBankAccountResponse"}},"additionalProperties":false,"title":"GetGemspayBankAccountResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"partnerName":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"partnerId":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"changeLogs":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"preImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"postImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true},"updateNote":{"type":"string","nullable":true},"action":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GemspayBankAccountChangeLogResponse"},"nullable":true}},"additionalProperties":false,"title":"GetGemspayBankAccountResponse"}},"additionalProperties":false,"title":"GetGemspayBankAccountResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"partnerName":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"partnerId":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"changeLogs":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"preImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"postImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true},"updateNote":{"type":"string","nullable":true},"action":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GemspayBankAccountChangeLogResponse"},"nullable":true}},"additionalProperties":false,"title":"GetGemspayBankAccountResponse"}},"additionalProperties":false,"title":"GetGemspayBankAccountResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      