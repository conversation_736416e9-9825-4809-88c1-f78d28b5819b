---
id: update-gemspaybankaccount-activedeactivate
title: "Update gemspay bank account id active deactivate"
description: "Update gemspay bank account id active deactivate"
sidebar_label: "Update gemspay bank account id active deactivate"
hide_title: true
hide_table_of_contents: true
api: eJztW0tv2zgQ/ivGnPbB1N096uYEQRqg7QZ5nAIfxuLEZiORLDlyaxj67wuSciLH7kIOgkXa0BfJ0syQ/DjzkSNw1mAsOWRl9LmEAhorkemMam9xNUN9j2VpGs2TktWSJGG4IhMIYJx7KG6hEz5GfT9JwjAVYNFhTUwuyKxBY01QgJIgQGkowCIvQICjr41yJKFg15AAXy6oRijWwCsbNDw7pecg4M64Gjl0sVES2lY8GD07/XQ1ubn+sDG9IJTkDjDettMkTJ6PjVwFidJoJs3hFq2tVBkxGn/xRodnO6bM7AuVDAKsC4iyIh91I249uZkxFaGGNnRWsUI27nMcx+6QdVNVOKso9b+vcVqjqgaotAJQShW6jtVFr2d3WHkSwIqDLNz0p703k2narxiZLhM+AXlg+s4ZighF3zn++DNj0rbBqiNvjfZpkH+/fx8uknzplA2NQQFXTVmS9yD6kRb9ylaoDgLRd6b2oliT9zgfhp9EHtCckgPoSUBHnIMnbyPf1DNygzQCPZ8YOcx8EB7cl/tV+ZGWNMSFBMyWepBcjVpGR7kjR7qkA0aaBAYI2sZZ44fJOqrR3Q8zi6uaNJ8sUGuqBmO+rTYY/W21q6qZD1RzrGk4W3TyB4wmyp/vdf897vxjZqvQ8ycj1Z0iebwaZG5bZSi/CSgdIZOc8H/GbPDLI1Y1RcbomjlEp1ygntNHM++zEDqHq7ApYar9i/GKdXRebzNaJqpMVD8RUb0lahi6wzoj3t1e/aOr1WW3k4qhbzzn2M+xn2P/7cX+/5ANiu7rz2fDQ9kgJXM/H+BP0T7Z7N/6kD8zWd47nY92X8TM5voBtazI7XyJOPQ7RE6h8+qUV6ecQucUOhNVJqrXT1RviRpyCp1jP8d+jv2cQr8qwH/FFPpZ5xpy7pyXpbws5dw5586ZqDJRvX6iekvUkHPnHPs59nPs59z5VQH+6+XO8dT39gnvdGh8NE9WRoFcRx2Bj5QcpVAcbZWU1MQLI1N1SBnKQ2KVSAFjtGrcGToKho46Q+O1ku04mTraMuXJLTfFJ42rQlkIs/XFONpp7NyhpHfhvqxMI9+htRDKQEL2f/lYCnL6HWtbRX/ccAfHWpInAfHoGU/9fvMmhsSdic607QijK2KuKPDZaHJxDgJCzxOIy782K2+N0dW7mpdngLs1Ow8e3TtyHyLQxUhNsN8CWgUC9gEPAopYzLOL/VTAwngO+uv1DD3duKptw+OvDbkVFLdTAUt0Kjn37Rqk8uFePjjhk64+VAjAb5ddSc/vIxD7h7DJfnTIfZZYNeEfCLinVSpAaqet2NQIvXjrqZVeRdJDD0LBUnp7kgweXQcDjxI75zceNSZlSZZ7sv1Gp724uZhcn4RWZ10VUx2XYXD4LZRD4bcEhYljixu1+GwNFep5E3d2kJoOv38BMjAH6Q==
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update gemspay bank account id active deactivate"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/gemspay-bank-account/{id}/active-deactivate"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update gemspay bank account id active deactivate

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"id","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"active":{"type":"boolean"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateGemspayBankAccountActiveStateRequest"}},"text/json":{"schema":{"type":"object","properties":{"active":{"type":"boolean"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateGemspayBankAccountActiveStateRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"active":{"type":"boolean"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateGemspayBankAccountActiveStateRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"partnerName":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"partnerId":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"changeLogs":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"preImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"postImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true},"updateNote":{"type":"string","nullable":true},"action":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GemspayBankAccountChangeLogResponse"},"nullable":true}},"additionalProperties":false,"title":"GetGemspayBankAccountResponse"}},"additionalProperties":false,"title":"GetGemspayBankAccountResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"partnerName":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"partnerId":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"changeLogs":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"preImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"postImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true},"updateNote":{"type":"string","nullable":true},"action":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GemspayBankAccountChangeLogResponse"},"nullable":true}},"additionalProperties":false,"title":"GetGemspayBankAccountResponse"}},"additionalProperties":false,"title":"GetGemspayBankAccountResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"partnerName":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"partnerId":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"changeLogs":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"preImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"postImage":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"accountName":{"type":"string","nullable":true},"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true},"bvn":{"type":"string","nullable":true},"mandateReferenceNumber":{"type":"string","nullable":true},"type":{"type":"string","nullable":true},"purpose":{"type":"string","nullable":true},"remark":{"type":"string","nullable":true},"paymentChannelCode":{"type":"string","nullable":true},"paymentChannelName":{"type":"string","nullable":true},"paymentChannelSlug":{"type":"string","nullable":true},"active":{"type":"boolean"},"lastModifiedBy":{"type":"string","nullable":true},"lastModifiedByEmail":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetGemspayBankAccountOnlyResponse"},"initiatorName":{"type":"string","nullable":true},"initiatorEmail":{"type":"string","nullable":true},"updateNote":{"type":"string","nullable":true},"action":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GemspayBankAccountChangeLogResponse"},"nullable":true}},"additionalProperties":false,"title":"GetGemspayBankAccountResponse"}},"additionalProperties":false,"title":"GetGemspayBankAccountResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      