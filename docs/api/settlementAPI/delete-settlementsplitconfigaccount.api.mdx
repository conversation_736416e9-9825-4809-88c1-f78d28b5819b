---
id: delete-settlementsplitconfigaccount
title: "Delete SettlementSplitConfigAccount settlementSplitAccountId"
description: "Delete SettlementSplitConfigAccount settlementSplitAccountId"
sidebar_label: "Delete SettlementSplitConfigAccount settlementSplitAccountId"
hide_title: true
hide_table_of_contents: true
api: eJytk02L2zAQhv9KeE8tqOttj7qFbtgGWijN7inkoLUniagtaaVxqDH672UcJ9mPrqG0J8vSO1/PzPTwgaJh692ygkZFNTGtiLmmhhynUFsuvdvanSlL3zqGAptdgl7jIluJ7PMgm4+yjUIw0TTEFEXdw5mGoJGeW436ZQUF66ARDO+hEOmxtZEqaI4tKaRyT42B7sFdGPxwtG4Hha2PjWFotK2tkLM6h7pdfFvN7+++nFzvyVQU/8J5zhsRp+BdoiTvn66v5VNRKqMNwg0aq7YsKSWJ/eLlZuA5myI1mwDSEO/9pS9QRzoahQm2mPJa9G+5zVBIFA+ntrSxFjTMIemiuFh9qOhwtaMmBdMlX7dS0VXpGwgT67Z+wGW5HkgfdU8Knc2/L6EgYY4oDh+RFYJP3BgntmOT/hHRM97n/jH94iLUxjoJOpTYj+zWMMFCTU+vgn4z5kZh7xOLp75/MInuY52zXD+2FDvo9UbhYKI1D8Jm3aOySc4V9NbUiV4lXXrH5GSE3/0YJ/P9TBbtT8WMl8Z1gtfUrfxB4Sd1U9uVN1mdFuC/53SM/WTdznnJsJyn+GbxdXG3QM6/AXwckiI=
sidebar_class_name: "delete api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete SettlementSplitConfigAccount settlementSplitAccountId"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/SettlementSplitConfigAccount/{settlementSplitAccountId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete SettlementSplitConfigAccount settlementSplitAccountId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementSplitAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      