---
id: create-fundreversals-retry
title: "Create fund reversals unprocessedReversalId retry"
description: "Create fund reversals unprocessedReversalId retry"
sidebar_label: "Create fund reversals unprocessedReversalId retry"
hide_title: true
hide_table_of_contents: true
api: eJzdVbFu2zAQ/RXhTS3ARGlHbUHRJhmKBnYyGR5o8WwzpUiGpIwaAv+9OElW7cQZWnTKZFl3fHfv8e6pg/MUZNLO3ilUqAPJRN9aqwLtKERp4oxS2EMgyU1EtQAHZ2MQSwEvg2woUeBoBysbQoXW+uBqipGm5DsFAW1Rwcu0hUCg51YHUqhSaEkg1ltqJKoOae8ZJKag7QYCaxcamRi21Qo5i6nOzdfv8+vHh9sD9JakovAX4DkvOTl6ZyNFjn++uuIfRbEO2rM0qDBva2YDgdrZRDb1SPQrld5Irty9ruBWT1QnCPjAKic94McR6k/iyjlD0iILNBSj3NA5EWxrjFwZGhhlASXTWbVeJGYBqZRmItLcH7WyliaSQNKJczHvz89GKW6lVYYC+uPeG133U1I+RffO2fa3+t5pZkY4HfEv/e4X69aqYtr+4uwiF2E0hYbS1rFxeBd7UXizK5TS65KBLiagsjuLlMsDVKSwO5hIGwzvcko+VmUZKSVDDdl0oWh3uaEmermPzrTc+WXtGvASa7t2vXIj9Zshr5hPx4vr+zsIcJmB8u4TXw/33sj+ukdX+RcpTsSc7u/IIrIYeHWjSgtIr9ncTnSCQPWWeQ6llgJbVrtaoOtWMtJjMDnz6+eWwh7VYimwk0EP47LooHTkZzVNx4teJ0vDh9lomx8LtvxzHMaX0jLpnTQt/4PAT9q/6ft5mcXBmv97Q0Phow/B1BR/J4bodV2TT0exY4jl0Rzf/5g/IOffCaZ8Sg==
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create fund reversals unprocessedReversalId retry"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/fund-reversals/{unprocessedReversalId}/retry"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create fund reversals unprocessedReversalId retry

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"unprocessedReversalId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}}}}}}
>
  
</StatusCodes>


      