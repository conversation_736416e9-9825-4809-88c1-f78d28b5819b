---
id: create-fundreversals-retry
title: "Create fund reversals unprocessedReversalId retry"
description: "Create fund reversals unprocessedReversalId retry"
sidebar_label: "Create fund reversals unprocessedReversalId retry"
hide_title: true
hide_table_of_contents: true
api: eJzdVT1v2zAQ/SvGm1qAjdKO2oKiTTIUDexkMjycxbPNlKIYkjJqCPzvxUmy6qTO0KJTJn3c6d29x7unDo3nQMk07lajRBWYEn9tnQ685xDJxjmncIBCom1EuYQE52MQKwVPgWpOHCTawVHNKNE6H5qKY+Qp+VZDwTiU8JR2UAj81JrAGmUKLSvEasc1oeyQDl5AYgrGbaGwaUJNSWBbo5Gzmupcf/m2uHq4vzlC75g0h78Az3klydE3LnKU+KfLS7lojlUwXqRBiUVbCRsoVI1L7FKPxD9T4S1J5e7PCs36kasEBR9E5WQG/DhC/U5cN41lcsgKNcdIWz4ngmutpbXlgVFW0JTOqvUiMSuQ1kaIkL07aWVDNrJCMklysei/n49S3JDTlgP6z723puqnpHiMzRtn25/qW6eZBeH5iH/ud3+2aZ2eTds/O7vIszCaQs1p14hx+Cb2oshmlyjIm0KAPkxARXcWKRdHqMhhfzSRNljZ5ZR8LItiy3Vs/TaQ5gu5r2zT6gvyHrK7xm2aXrCR8TXX0dNhtuCULNfs0uzq7hYKgj4w3X+UU5GWa+pPeTSTf1HgmYbTsZ04Q1YDnW4UZwnyRjztmTxQKF/zzKHUSmEnIpdLdN2aIj8Em7O8fmo5HFAuVwp7CmaYkmUHbaLc62koXvQ6ORnezUe3fD8Tpz/HYXxJTkjvybbyBIUffHjV7vMqq6Mj//eGhsIn/j81Jb+HIXpVVezTSewUYnUyvnffF/fI+RcI1Xh/
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create fund reversals unprocessedReversalId retry"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/fund-reversals/{unprocessedReversalId}/retry"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create fund reversals unprocessedReversalId retry

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"unprocessedReversalId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}}}}}}
>
  
</StatusCodes>


      