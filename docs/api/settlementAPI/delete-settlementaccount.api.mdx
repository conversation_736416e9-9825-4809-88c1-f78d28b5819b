---
id: delete-settlementaccount
title: "Delete SettlementAccount settlementAccountId"
description: "Delete SettlementAccount settlementAccountId"
sidebar_label: "Delete SettlementAccount settlementAccountId"
hide_title: true
hide_table_of_contents: true
api: eJytk01rGzEQhv+KeU8NqNm0R90MMa0hhVInJ+PDZHdsi+5KijRrahb99zL+TGPnEOhptZrP99HMgBA5kbjgpw0sGm5ZeMYiLXfsheo69F5gILTKsHOcbeODbWEQKVHHwkldBnjqGBb5reu0gYHzsIgkaxgkfuld4gZWUs8GuV5zR7ADZBt3KSQ5v4LBMqSOBBZ97xqUYk5Vvk1+zMZPj9+PqddMDacPJC9loc45Bp85q/3r3Z1+Gs51clHhwGLW1zXnrLXfWO530EYXZEbXAXQs63CGDbOnYVFRdNVFlmq4kqbAIHPaHIn3qVXpIjHbqjoHfG54c7viLkfa5tD22vFtHTqoZueXYYfDSbsjufd7JWQ0/jmFgZbZS918QTGIIUtHXmMPj/BBBP/wO72H8B+pYkvOa5GdpOHAZg6KDubK9BnYazUWBuuQRSOH4ZkyP6W2FL1+6TltYecLgw0lR8+qfT6gcVnPDeyS2swXTdbBC3sdwU+/DpN1M9LFuNb84ZL8VvFR2+sfDH7z9p3FKItijrP739vZl321KaeWdA5OA3k/eZg8TlDKXyZ/a04=
sidebar_class_name: "delete api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete SettlementAccount settlementAccountId"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/SettlementAccount/{settlementAccountId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete SettlementAccount settlementAccountId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      