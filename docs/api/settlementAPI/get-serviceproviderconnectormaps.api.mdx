---
id: get-serviceproviderconnectormaps
title: "Get service provider connector maps"
description: "Get service provider connector maps"
sidebar_label: "Get service provider connector maps"
hide_title: true
hide_table_of_contents: true
api: eJztWE1vGjEQ/StoTq20hDS97Q1VEYnURCgkp4iDWQ/g1Gs79iwNRfvfq9kvlkBSiiK1hz0h7Hkz4+fngZkNWIdekLLmWkIMC6QJ+pVK0Hm7UhJ9Yo3BhKxPhQsQAYlFgPgRKrNxZXar3Lfa8kY4mEbghBcpEnq234ARKUIMY7HA2yydoYcIlIEYnjP0a4ggJEtMBcQboLVjU2UIF4XdnMNTufT1AvI82vE3Ub/wY7yNLm8mw4f7q9rbEoUsMB6fM+VRQkw+wwPuA3llFpDnUzYOzpqAgfcvzs/5Q2JIvHLMNMQwyZIEA/OZWENoqPCELzRwWnDkzX4EO3vChCAC5/nSSJX+Q+VqazizVqMwkEeQYghigft5RmAyrcVMY3miPAIpqB1OeC+YSEWYhj+noeShIA3VWaYkJxR2ZXN9Euq2uKwjjmRaqmQAmixl8Ro1K8j/yQxPoz1HpIi9QFvVgRNJPApCOaR305aCsE8qxeIKrFRz9TeYPAIhpWKpCD1usTwXOuA2u1HzWN94hXeVDjmNPW7c9iEe80hKQPHSjjOfKx9o/Ib4tjfs1aGL0+J0LFkSmsHhyEwLwB0m1stjIQZfTs7PeVwpm4XT8B8uju+qpFrWC1fCSI2+********************************+K/UVaSuInUVqatI/0FFyjnabo83QupVr7FXd9O9pp3uVf10irS0VevNlULQEmIYCKcGFbhfg/sNuF+B2aLurzOvuWElciEeDAISaUzRUF/i6myBaXBiHazOOLuzxKbAnaoyc1sQ21BR2PUmDbw3HF9DBBymPNbqS6FwGygVRfWtW+ejjrtDUXOhrc43j8qTbCoqHkE4BU1de5OMaQRLG4gBm81MBHzwOs95uZwIMEVSBZaHbBTwA9evRxIroTNOCXgq8A6gmjlszaf8xatSf4/TPKqHBwcjv+KhmQLAp7tq0vC5B9FhfsokWqOKnZzL3WGSoKPWXtvFtCW70eU95Plvkf9B9g==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get service provider connector maps"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/service-provider-connector-maps"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get service provider connector maps

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"serviceProviderId":{"type":"string","format":"uuid"},"serviceProviderName":{"type":"string","nullable":true},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"serviceProviderId":{"type":"string","format":"uuid"},"serviceProviderName":{"type":"string","nullable":true},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"serviceProviderId":{"type":"string","format":"uuid"},"serviceProviderName":{"type":"string","nullable":true},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      