---
id: update-auto-settlement-charge-setup
title: "Update auto settlement charge setup"
description: "Update auto settlement charge setup"
sidebar_label: "Update auto settlement charge setup"
hide_title: true
hide_table_of_contents: true
api: eJztWE1z2zYQ/SuaPbUpGDl2rLS8KZ5M4kNbj2WfPDqsiKWElAQQAHSi0fC/dxakRNqSG6nj5JBIF1Hgfr59C2GxAmPJYVBGX0pIobISA42rYCYUQkEl6XCxQDenCYXKgoCAcw/pHTwtMxVg0WFJgRyLrkBjSZCC59eXEgQoDSlYDAsQ4OhTpRxJSIOrSIDPFlQipCsISxvVglN6DgJy40oMHGWlJNS16FtuA7lyJlcFfRMv79/9ORnf3nxYm14QSnIHGK/raSNMPrw1cskSmdGBdOBHtLZQWSzG8KM3mte2TJnZR8oCCLCOSxcU+Si3A4GvJycgi4XrieqqnMWkNqLSVLOCGAlAKRWHh8VVz3uOhScBQYWCLdw+yaHrJvVoKtCX8ONn2S/pi99+8HTZmCNvjfZN/KcnJ/wlyWdOWfYBKUyqLCPvQfSpH9lgC1SH4dOa6gRnxhSEmkEoyXt8gMIGMF0VBXL6sV9rARLDHu7Us6MfhbWmYq8ot+jw9ZBLcuwhvK280uT9X3En28PXWvFvPTPopNLz3dzbUsQHFNlVmn0p957C5HHG1y29InSOMJAch/+sCtM2CaqMKqWRKleH6BwQ7O7mWMf7TIbW3x9Qy4Lc1iZz8BZzbKFjCx1b6P+dRo69c+ydn7134qnv4QmvOSsOuBSDjjSDhpgD3w6SJYWFkc2ElvGIFie1FIZo1ZB1k043aXSTqOuHq3aSrIdrnsSlx2flGpi07n49hlau4KktBOvT4bBnXdL9yzmV3uLSm6LiNF5mpgQe13hTuO5GtndfsLQN9XeezuEsx9/P89Hr5PzNqzfJ6/PRaTI7y7PkNPtjdJaPRpjjCLouPal5mMxNJMSmBjGUQVeEwfjqEgRwJg3E96+YT9b4UGLcs9oRdT/oH5RrQ8TeGbwWDVirtih3gFZB011PlgUEpN2Iv65Mu7qF1FTAwvjAtlerGXq6dUVd8/KnitwS0rupgHt0qmnvuxVI5flZbjj7KI3NOAG/XLcD+a8DvrHYlV67iHrJuGJR8S8Q8A8tezcVPPx/X7dbONXTWqxvGp4dhcZt715jExJn3ry9aAwmN2ygk9g6dHYa4ywjG3qyfafTXudfjW8u2OusvQspjWRxh5/5UgU/N9iYmFv8P4lrKyhQz6v45wqNa/78Cy4Cnto=
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update auto settlement charge setup"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/auto-settlement-charge-setups/{setupId}/merchant/{settlementProfileId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update auto settlement charge setup

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"setupId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"settlementProfileId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"}},"additionalProperties":false,"title":"UpdateAutoSettlementChargeRequest"}},"text/json":{"schema":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"}},"additionalProperties":false,"title":"UpdateAutoSettlementChargeRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"}},"additionalProperties":false,"title":"UpdateAutoSettlementChargeRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      