---
id: delete-auto-settlement-charge-setup
title: "Delete auto settlement charge setup"
description: "Delete auto settlement charge setup"
sidebar_label: "Delete auto settlement charge setup"
hide_title: true
hide_table_of_contents: true
api: eJztVk1v2zAM/SvBO22AWnc7+hZsQVtgA4qmPQU5KBYTq5MtVaKDBYb++yDbSdyvoQO6nnqSLZGP4hPFpxbWkZesbX2pkEORIaZpw3ZOzIYqqvlbKf2G5sSNgwDLTUC+wMs2SwEnvayIySfTFrWsCDlCWr5UENA1cjjJJQQ83Tfak0LOviGBUJRUSeQteOc6N/a63kBgbX0lGTmaRivEKMbIw0auvF1rQ/8lyvns53x6e3Oxhy5JKvL/AB7jMhkHZ+tAIa1/PTtLg6JQeO3SMSDHvCkKCgECha2Zau6Q6DdnzsgUuX0awa7uqGAIOJ9OlHWPHwaoo+HKWkOyRhSoKAS5oedIqBtj5MpQn1EUUJJfEU6rVzEKqZROyUpzNfJfSxNIgDWnwPj+Yi1eDxS+JdZ+vJC1MuR7aOeMLrrbkd0F+8H8uzHfVfsH5e9HeUzoD9tQDzORDdvJscFOig5pEgY9qIhLe5SOxFLquDky6XSWnE+Ozie980nnHLJ2UISYVeSLUtbcTT1u5RECgfx2LyeNN6n7MruQZ9kIXdH2dENVcHIXrGlSHqeFrZDarq7XtjuvgaTz3m5ypGgyvbqEQArTE7D9kgrH2cCV7Apx0IHXEfOAzEOdjNp4FH0m7cDYAtJpCPyVMwjkRx3d0zbMPtHApUBpAyfstl3JQLfexJim7xvyO+SLpcBWet2X/6KF0iF9q0NRPUrjoEj4dD2o3udJehY8l94wKetd4lWaJv1B4BftRs+BpLDvG/YJT3EZxV7O35yFPuzo8XDYUsq8X50WBTkerY0hlqNb9n32Y3YzQ4x/AK5ibAg=
sidebar_class_name: "delete api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete auto settlement charge setup"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/auto-settlement-charge-setups/{setupId}/merchant/{settlementProfileId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete auto settlement charge setup

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"setupId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"settlementProfileId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"DeleteAutoSettlementChargeResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"DeleteAutoSettlementChargeResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"DeleteAutoSettlementChargeResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      