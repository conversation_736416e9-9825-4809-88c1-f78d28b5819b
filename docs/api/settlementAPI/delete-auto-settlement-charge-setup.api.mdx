---
id: delete-auto-settlement-charge-setup
title: "Delete auto settlement charge setup"
description: "Delete auto settlement charge setup"
sidebar_label: "Delete auto settlement charge setup"
hide_title: true
hide_table_of_contents: true
api: eJztVk1v2zAM/SsBTxug1t2OvgVr0BbYgKJpT0EOjMXE6mRJlahigaH/Psh2EvcTHdD11JNlSnwUnyg+tWAdeWRlzYWEEiRpYppGtnNi1tSQ4R81+g3NiaMDAYybAOUCXl6zFODQY0NMPi9twWBDUELI0xcSBCgDJTjkGgR4uovKk4SSfSQBoaqpQShb4K3r3NgrswEBa+sbZCghRiUhJTFGHjZy6e1aafovUc5mv+bTm+vzHXRNKMn/A3hKy7w4OGsChTz//eQkfySFyiuXjwFKmMeqohBAQGUNk+EOif5w4TTmyO3TCHZ1SxWDAOfzibLq8cMAdVi4slYTGkgCGgoBN/QcCSZqjStNfUZJgER+Qzgl38QooJQqJ4v6cuS/Rh1IACvOgeH0xVq8Gih8T6zd9xyN1OR7aOe0qrrbUdwG+8n8hzHfVfsn5R9HecroD9tQDzPByHZyaLCTqkOahEEPGuLaHqQjs5Q7bgkFOlVk56OD81HvfNQ5h6IdFCEVDfmqRsOd6XErTyAgkL/fyUn0OndfZhfKothQE6LbeJR0nMeVtlEeo3OQu60ya9sd08DNGTXB4XZyYGYyvbwAARm9z/v+W64XZwM32NXf0P7fxscDDvflMereSfQJtANRC0CnQMCrVIGA8iCfO7YG6xPpWwqobeCM3bYrDHTjdUrZfBfJb6FcLAXco1d91S9akCrksdzX0qM09kIEX64Gsfs6ya+B59IbjGi2mVfUMf+BgN+0Hb0CsrB+bNgnPKVlEjsVf3cW+rCjN8N+SznzfnZaVeR4NDeGWI4u1+ns5+x6Bin9Bc2MaD0=
sidebar_class_name: "delete api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete auto settlement charge setup"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/auto-settlement-charge-setups/{setupId}/merchant/{settlementProfileId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete auto settlement charge setup

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"setupId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"settlementProfileId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"DeleteAutoSettlementChargeResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"DeleteAutoSettlementChargeResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"DeleteAutoSettlementChargeResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      