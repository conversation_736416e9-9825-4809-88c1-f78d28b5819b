---
id: add-settlementmapping-add
title: "Add SettlementMapping add"
description: "Add SettlementMapping add"
sidebar_label: "Add SettlementMapping add"
hide_title: true
hide_table_of_contents: true
api: eJztV01T4zAM/SsdnfYjpXyW3dwKy7Ac2O1QOHV6UGOlNTixsZ1CJ5P/vqMktIW0DLDDrb20Yz3Jkv2eK+WgDVn0UqcXAkJAIQbkvaKEUp+gMTKd9ISAADxOHIRDWJovKzOMAjBoMSFPliE5pJgQhHB+djno3Vz/hgBkCiFMCQVZCMDSfSYtCQi9zSgAF00pQQhz8HPDns5bjlwUowpMzp9oMWdEpFNPqeefaIySUZl859bplNcaofT4liIPARjLpXpJrsQtyuhbHUtFXP7L/QOItU3QQwhZJgUUQV1aE5hmSuFYUVVSEUB9eCtYtBbnfBaeEveeDHtRpLPUr8+wsXHD789bU256ZsmY7Dt9TzC9+8CW7HaqxRvddGYjYhf38QMevyfR8dvTKwKWkWRaouqvbBmjchSAl56xMFgU8Ys8SuVKfn0sVq3FSy1I/UeY3qr865hXlf6Ag3h69FupbaW2ldqnS231z+3b963mtprbau5zNcdhLDmjU1ed4P7uLn8JcpGVhqNzWVkUkXOlRp9bekK0GuFbWHbPCfmp5gbbaFdeFfophNBBIzsNn07l48jOnjrqzCpuoL03Lux0lrRqC5rtTChxBudOq4xT2Yl0Atw586Nxteyezx4xMYo2Pg9wEOOPo7h72D463jtuHx5199vjgzhq70c/uwdxt4sxduHpjVjyY/EUDDeoegndIN7XALVG10GWUtxkrSi9Yl0V1nBVI0vMuOFXjIpRwTNMrEt91Gw6r4595c5bvf4FBMC3VlFitsds5TtPsHy/67N7jSrPSLVQY9l5GYUy5YglHfKaRUNAIyFYM5mVsuD5bMqsC4eQ52N0dGNVUfDyfUZ2DuFwFMAMrawUNeRa61GNz0hIxwaxUNOLBBcDGXy5que6ry0eGNclfkfz54PhDFXGMCiCvLaeVgHb1xxgiWhMe0zxha76fwfXfHn1lJhUF2jxgcdNfOAdAtBl0qW2y7UcFKaTDCeMrWLy5x/9LFLI
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Add SettlementMapping add"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/SettlementMapping/add"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Add SettlementMapping add

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"mapping":{"type":"array","items":{"type":"object","properties":{"settlementAccountId":{"type":"string","nullable":true},"settlementAccountName":{"type":"string","nullable":true},"settlementAccountNumber":{"type":"string","nullable":true},"settlementBankName":{"type":"string","nullable":true},"settlementBankCode":{"type":"string","nullable":true},"sourceBanks":{"type":"array","items":{"type":"object","properties":{"bankName":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"SourceBankDetails"},"nullable":true}},"additionalProperties":false,"title":"MappingModel"},"nullable":true}},"additionalProperties":false,"title":"AddSettlementMappingRequest"}},"text/json":{"schema":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"mapping":{"type":"array","items":{"type":"object","properties":{"settlementAccountId":{"type":"string","nullable":true},"settlementAccountName":{"type":"string","nullable":true},"settlementAccountNumber":{"type":"string","nullable":true},"settlementBankName":{"type":"string","nullable":true},"settlementBankCode":{"type":"string","nullable":true},"sourceBanks":{"type":"array","items":{"type":"object","properties":{"bankName":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"SourceBankDetails"},"nullable":true}},"additionalProperties":false,"title":"MappingModel"},"nullable":true}},"additionalProperties":false,"title":"AddSettlementMappingRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"mapping":{"type":"array","items":{"type":"object","properties":{"settlementAccountId":{"type":"string","nullable":true},"settlementAccountName":{"type":"string","nullable":true},"settlementAccountNumber":{"type":"string","nullable":true},"settlementBankName":{"type":"string","nullable":true},"settlementBankCode":{"type":"string","nullable":true},"sourceBanks":{"type":"array","items":{"type":"object","properties":{"bankName":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"SourceBankDetails"},"nullable":true}},"additionalProperties":false,"title":"MappingModel"},"nullable":true}},"additionalProperties":false,"title":"AddSettlementMappingRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      