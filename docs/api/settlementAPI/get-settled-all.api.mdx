---
id: get-settled-all
title: "Get settled all"
description: "Get settled all"
sidebar_label: "Get settled all"
hide_title: true
hide_table_of_contents: true
api: eJytVU1v2kAQ/StoTq3khDS9+YaalHJIhQI5IQ6DPdir7oezO0allv97NbYhMaXBinJC7L43fm8881yBK8gjK2dnKcSQES+IWVM60RoiYMwCxCvoDmEdQYEeDTF5uajAoiGIYY4Z/SzNhjxEoCzE8FyS30MEIcnJIMQV8L4QqLJMWYPbOm+Q26Ovt1DXUa/eQv2hj6n2LUdrSV8sFtgrm/WoE+NKyxeZ9uD9qCJ15UZTr9bSow2YSLcfaUuebHLZ4BlNc/LKpe9hfvfODOW99oJMV6zMiR33YaXucP9htR7IJzlanr2rQ7Mws3PvMk8hXORvnNOEtldgev+wmDwtfxzIOWHaTIan51J5SiFmX9JbatYCDoWzgYLc397cyE9KIfGqkPGBGBZlkojGuo5ObqbEo9Au7AibNTbEuev2G2SFOYcYxliocQcct8BAfnfY7NJrkc9chHjc4QxZvkppd52RCQXug9OlPPU6cQZEt7Jb11hSrJtutLjR4kgfTeYziEAe08rdfYE6gsIFNmiFe2jkPzZ6No9tY/rN40KjslKnUV11FleAhWpstfEVgRRaR5C7wHJdVRsM9OR1Xctx+6LFfKoCboQSb1EHiuAX7U9jboe6FAEgb/8NQpdjQ+AvQTUEfcymIeD/hM8gE4e8GQLuImaQJDcQ2MbDEGRv+YcQTvb9hbKWP14JB+LVuo4Om3x2PE5GM3GWyUpGfXrs1v7zSL6n50a2FfIqN3oijrs7vV9CXf8F1QiMzA==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settled all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settled/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settled all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"Channel","in":"query","schema":{"type":"string"}},{"name":"Amount","in":"query","schema":{"type":"number","format":"double"}},{"name":"TransactionReference","in":"query","schema":{"type":"string"}},{"name":"Period","in":"query","schema":{"type":"string"}},{"name":"From","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"To","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"Day","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"MerchantId","in":"query","schema":{"type":"string"}},{"name":"IsInProgress","in":"query","schema":{"type":"boolean"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      