---
id: create-fundreversals-reverse
title: "Create fund reversals transactionId reverse"
description: "Create fund reversals transactionId reverse"
sidebar_label: "Create fund reversals transactionId reverse"
hide_title: true
hide_table_of_contents: true
api: eJztWEtv2zgQ/ivGnPZB12nSuFvd0qDb9rC7hp2eDB/G5MhWS5EqSbk1BP33xUiyLMUOage9FHAukTkPksPvm+GwAJuRw5BY81FBBNIRBvo7N8rRhpxH7afVB4GAgCsP0RxYPG3EsBCQocOUAjmWFmAwJYggODQeZeNaQGIgggzDGgQ4+ponjhREweUkwMs1pQhRAWGbsbEPLjErEBBbl2KACPI8UVCWovX//t0/s7tPDx92rteEitwZzstyUSuTD2+t2rKGtCaQCfyJWaYTWYVm9Nlbw2MHruzyM8kAAjLHgQwJ+Ura2/uPtyUgJSfXaMKJ6pja3IQJJl11k6fLKgKturL5UhMbdFY0pZgcGUnHZjK51sg2VfBKAZ7cJpE0cXaTKHL/VsE/3+7eqmfZzXS+OsmOo2dIn6TrzoqAzH2wKbm3aL6cvI2u0ckx2xndScnH+2y7Ggc/tmQgKZUwKFBPOgiOUXsSEJLAuj3CT2vCMBUh0Pdw4caFGxduHHKjWz7++PNCkgtJLiR5RJKyiqTPrPE16q+vrvifIi9dkrFXiGCWS0neg+hezqrKk2lMzmKVb1ztFZfWakJTE8h7XJ0WL4Xh6G31meGZVfbTJhQf0ChN7iCLnJtDfr3dPus+8atts4J9H+L3Vdc1iHOjBm3fNejVgGac27CUwtpys5ZZX4WDe6oIRpglI3YxbF2Mip6PcrR3wlly17DlTnP/FELmo9HIUwiaUjJhqGjzYkWpz3Drrc7ZywtpU+DGiY9pum+e3n3HNNN0pHbBTYx/3cbjV8Pb1y9fD1/djq+Hy5tYDq/lm/FNPB5jjGPoF7BTbbpV7OqpGrU/yaOl6ElxnaqfFNeFZS9u68d+yB1bxGE1OC57vLqjuf1pcZPC22aXu+TYVufTAPR9fbKDWXvgg7vJRxDAwKiBuXnJhoyzFCtSNr33eYDtgb3lVyeFl6LGYNFgeQ6YJXwN6aEZBDOw/6iwm2QhYM1siOZQFEv09MnpsuThrzm5LUTzhYANuqQm8rwAlXj+Vi1vH62yLTbw27R5UPh9wI8gx1bfDKLZcvhQ5/wLBHyh7cFLSLkoxe6x4qcvpJ6w8zTSLoZfTmrpfe1w+MAO9hoHpWZvcSclZaGj25100clIk/9mDyBg2bympDW8HX7jo8JvdVBstbUqYVdjBWg0q7xK01DPzH//AxppiwM=
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create fund reversals transactionId reverse"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/fund-reversals/{transactionId}/reverse"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create fund reversals transactionId reverse

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"transactionId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"transactionId":{"type":"string","format":"uuid"},"merchantId":{"type":"string","format":"uuid"},"amountPaid":{"type":"number","format":"double"},"transactionReference":{"type":"string","nullable":true},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"serviceProviderSlug":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"customerBankCode":{"type":"string","nullable":true},"customerBankName":{"type":"string","nullable":true},"customerAccountName":{"type":"string","nullable":true},"customerAccountNumber":{"type":"string","nullable":true}},"additionalProperties":false,"title":"FundReversalRequest"}},"text/json":{"schema":{"type":"object","properties":{"transactionId":{"type":"string","format":"uuid"},"merchantId":{"type":"string","format":"uuid"},"amountPaid":{"type":"number","format":"double"},"transactionReference":{"type":"string","nullable":true},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"serviceProviderSlug":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"customerBankCode":{"type":"string","nullable":true},"customerBankName":{"type":"string","nullable":true},"customerAccountName":{"type":"string","nullable":true},"customerAccountNumber":{"type":"string","nullable":true}},"additionalProperties":false,"title":"FundReversalRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"transactionId":{"type":"string","format":"uuid"},"merchantId":{"type":"string","format":"uuid"},"amountPaid":{"type":"number","format":"double"},"transactionReference":{"type":"string","nullable":true},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"serviceProviderSlug":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"customerBankCode":{"type":"string","nullable":true},"customerBankName":{"type":"string","nullable":true},"customerAccountName":{"type":"string","nullable":true},"customerAccountNumber":{"type":"string","nullable":true}},"additionalProperties":false,"title":"FundReversalRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}}}}}}
>
  
</StatusCodes>


      