---
id: create-fundreversals-reverse
title: "Create fund reversals transactionId reverse"
description: "Create fund reversals transactionId reverse"
sidebar_label: "Create fund reversals transactionId reverse"
hide_title: true
hide_table_of_contents: true
api: eJztWEuT2jgQ/itUn/YhwmQmQza+TaaySQ67S8HkRHFo5DY4kSVFDxKK8n/fatsYMzAVmMolVcxljPohqfV93WptwFhyGHKjP6aQgHSEgf6OOnW0IudR+XH1QSAg4MJDMgUWjxsxzARYdFhQIMfSDWgsCBIIDrVH2bgWkGtIwGJYggBHX2PuKIUkuEgCvFxSgZBsIKwtG/vgcr0AAZlxBQZIIMY8hbIUrf/37/6Z3H16+LB1vSRMyZ3hvCxntTL58Naka9aQRgfSgT/RWpXLKjSDz95oHjtwZeafSQYQYB0HMuTkK+ne3n+8LQEFOblEHU5Ux8JEHUaYd9V1LOZVBFr11MS5IjborGhMGTnSko7NpKNSyDZV8EoBntwqlzRyZpWn5P6tgn++3b1Jn2U3UXFxkh1HT5M6SdedFQEZfTAFubeov5y8ja7RyTHbGt1Jycf7bLsaBz+2ZCClac6gQDXqIDhD5UlAyAPr7hF+XBOGqQiBvocLNy7cuHDjkBvd8vHHnxeSXEhyIckjkpRVJL012teov7664n8peelyy14hgUmUkrwH0b2cVZXHKszPYpVvXO0U58YoQl0TyHtcnBavFMPR2+ozwzOp7MdNKD6gThW5gyxybg759Xb7rPvEr7bNCvb7EL+vuq5eFnXaa/uu3l4NaMa5DSsoLA03a9b4KhzcUyUwQJsP2EW/dTHY7PkoBzsnnCW3DVt0ivunEKxPBoMFFT7ahcOUXvC3VCamL9Ba4H6JT2e865nefcfCKjpSsuAmw79us+Gr/u3rl6/7r26H1/35TSb71/LN8CYbDjHDIezXrVNtusXr6qnStDvAoxXoSXGdoZ8U1/VkJ27Lxm7IHVvEYRE4Lnu8uqMp/Wlxk7nbHpeb48xU59Pg8j0V3uK6N6EQFBWkQ+9u9BEEMB5qPK5esiHDq8CKi03LfR5O9zDe0qqTuUtRQ2/TQHgKaHO+feyBGAQTb/8tYTvJTMCSSZBMYbOZo6dPTpUlD3+N5NaQTGcCVujymr/TDaS55++0peujVbY1Bn4bN+8Iv/f47ePY6ptB1GsOH6rIv0DAF1ofPICUs1Js3yh++kLqCTsvIu1i+MGklt7XDvsP7GCncVBhdhZ3UpINHd3upLNOIhr9N3kAAfPmEaWo4e3wGx8VfquDYqqtVXm6GtuAQr2IVXaGemb++x/7vYc4
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create fund reversals transactionId reverse"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/fund-reversals/{transactionId}/reverse"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create fund reversals transactionId reverse

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"transactionId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"transactionId":{"type":"string","format":"uuid"},"merchantId":{"type":"string","format":"uuid"},"amountPaid":{"type":"number","format":"double"},"transactionReference":{"type":"string","nullable":true},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"serviceProviderSlug":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"customerBankCode":{"type":"string","nullable":true},"customerBankName":{"type":"string","nullable":true},"customerAccountName":{"type":"string","nullable":true},"customerAccountNumber":{"type":"string","nullable":true}},"additionalProperties":false,"title":"FundReversalRequest"}},"text/json":{"schema":{"type":"object","properties":{"transactionId":{"type":"string","format":"uuid"},"merchantId":{"type":"string","format":"uuid"},"amountPaid":{"type":"number","format":"double"},"transactionReference":{"type":"string","nullable":true},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"serviceProviderSlug":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"customerBankCode":{"type":"string","nullable":true},"customerBankName":{"type":"string","nullable":true},"customerAccountName":{"type":"string","nullable":true},"customerAccountNumber":{"type":"string","nullable":true}},"additionalProperties":false,"title":"FundReversalRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"transactionId":{"type":"string","format":"uuid"},"merchantId":{"type":"string","format":"uuid"},"amountPaid":{"type":"number","format":"double"},"transactionReference":{"type":"string","nullable":true},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"serviceProviderSlug":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"customerBankCode":{"type":"string","nullable":true},"customerBankName":{"type":"string","nullable":true},"customerAccountName":{"type":"string","nullable":true},"customerAccountNumber":{"type":"string","nullable":true}},"additionalProperties":false,"title":"FundReversalRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}}}}}}
>
  
</StatusCodes>


      