---
id: get-healthcheck
title: "Get health check"
description: "Get health check"
sidebar_label: "Get health check"
hide_title: true
hide_table_of_contents: true
api: eJxtkkFr3DAQhf+KeacWlGzao26hhE0OhdJNTosPij1Zi8iSoxmbLkb/vYx3vbBNT4KZN9L33mhGGig78Sk+tbA4kDySC9I1HTXvMBB3YNg9TtUfS7U2GFx2PQllbc6IridYbB9+7u5fnh9h4CMsOnItZRhk+hh9phZW8kgG3HTUO9gZchx0kiX7eEAptYp5SJGJtf/97k6PlrjJflBOWOzGpiFmlGL+6WxJqm5BrVYHPUmXzt6g5NLBYnNS3awqpjytbsYcFF5kYLvZMIkE6inKTUvT7YF6HtyRUxj1zdsm9VBqH9/SYshLWLI46ardZby6//UEA33mBDt9QzEYEkvvos6uMX42ceXykprQH9kMwfmoFy3Y89ngHlcGa4MusWh9nl8d00sOpWj5Y6R8hN3XBpPL3r0q/b4uZt2eJtJ61kYL++YC0yeeJkWhKLD48vu86q+V/p7/cb7T8fqvTC6MKltyvKxr+/CMUv4C3KDjqA==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get health check"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/health-check"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get health check

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      