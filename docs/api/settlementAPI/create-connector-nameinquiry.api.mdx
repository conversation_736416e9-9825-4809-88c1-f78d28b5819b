---
id: create-connector-nameinquiry
title: "Create connector connector name inquiry"
description: "Create connector connector name inquiry"
sidebar_label: "Create connector connector name inquiry"
hide_title: true
hide_table_of_contents: true
api: eJztV0tv2zAM/isBT3uoy7ajb10xbAG2Lmi6XYJgYGQ20SpLmkRnCwz/94G247hrD+4el625RJZJivxIyvwq8IEisvFulkMGOhIynXnnSLOP51iQcV9LE/eggHGTIFtC/xpWCgJGLIgpyqsKHBYkdnoRBcZBBgF5CwoiiTHKIeNYkoKkt1QgZBXwPohi4mjcBupa9bbevH6/OP14+fZgakuYU7yXsVUrTIlf+XwvEto7JseyxBCs0Q0G0y/JO9m7Zcqvv5BmUBCiIMaGUqOrtS8dn5fFmuLtkxW40lpcW2p9rBWs0V2f+ZxGCNcKMM+NOIZ2Pjj3Cm0iBWxYZEGyNGuzdNFGKfgB03f+pwIaJurJ038nMtGOlIJ3qXX15fPn8pdT0tEEMQoZLEqtKSVQw9ptchwsmntBkTpTR8G195bQQS3WR0WsoKCUcDNONkf+Kyk6aDQ3xXj5mUtsuBRkRyZYQaKUumtyhDRHdAk1j9eQavtE0Vx1JX4PFK73+h3tyP6tQm1LExoQ4s5omke/M/lI79gUtGAswl3SVz4WyJBJhdCJiMLv+Hg+m38+rN+iyy3FW1fHfS+Oh2556Jb/q1t+aXJ4aJOHNvmf2qQZ226OaGcNe5r05GewEi4zOVKpgnjrhXAFn5qSFXqUwRSDmfZK06pf1lMxcHI0IJgdOFcZrdAi5pCy6XRDRSrDJmJOz2StrS/zZxgCCA2Srr44UqHX37EIlu7okyOaxzG4J1TCxK58A30H2xsqUsD9ZEHMlgpyPDmdz0CBeNmis3shihJwgc3N0vG78ajdQLtP+2AGrlULRtUBugQMpp2YezqaDR9uoLpSsJV0ZEuoqjUm+hhtXcv215LiHrLlSsEOo2krcVlBbpKs876KfvKwn9Th0UVHVh9PhEnf5Xm3iU5C3aEt5QkUXNP+BqOuV7U6kOA/7kR72IBy944II2/fnrUGTy7FwFHi1ox11DjVmgIPZIeHrgbtMP+wuJSS61h60ZZdxG9C9/FbC4hvQmsu92avAotuUzafDWhPlt8PRC30cg==
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create connector connector name inquiry"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/connector/{connector}/name-inquiry"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create connector connector name inquiry

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"connector","in":"path","required":true,"schema":{"type":"string"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NameInquiryRequest"}},"text/json":{"schema":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NameInquiryRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NameInquiryRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"accountName":{"type":"string","nullable":true},"accountInstitutionCode":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"bankVerificationNumber":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NameInquiryResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"NameInquiryResponseNIP_ResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"accountName":{"type":"string","nullable":true},"accountInstitutionCode":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"bankVerificationNumber":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NameInquiryResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"NameInquiryResponseNIP_ResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"accountName":{"type":"string","nullable":true},"accountInstitutionCode":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"bankVerificationNumber":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NameInquiryResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"NameInquiryResponseNIP_ResponseHandler"}}}}}}
>
  
</StatusCodes>


      