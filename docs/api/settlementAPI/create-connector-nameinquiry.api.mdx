---
id: create-connector-nameinquiry
title: "Create connector connector name inquiry"
description: "Create connector connector name inquiry"
sidebar_label: "Create connector connector name inquiry"
hide_title: true
hide_table_of_contents: true
api: eJztV99z2zYM/ld8eNoPpu72qLc012t9t2W+ON2Lz7eDKcRmQ5EsCbn16fS/7yDKsrLkQdnWlzZ+MUUBIPABoPA14ANFZOPdooQCdCRkuvLOkWYfr7Ei4z7VJh5BAeMuQbGG4TVsFASMWBFTlFcNOKxI7AwiCoyDAgLyHhREEmNUQsGxJgVJ76lCKBrgYxDFxNG4HbStGmy9e/v76vLD7fuTqT1hSfFZxjZZmBK/8eVRJLR3TI5liSFYozsM5h+Td7L3yJTffiTNoCBEQYwNpU5Xa187vq6rLcXHJytwtbW4tZR9bBVs0d1f+ZImCLcKsCyNOIZ2OTr3Dm0iBWxYZEGytMhZuslRCn7A9IW/qYDGifrp528nMtGOlIJ3Kbv66+vX8ldS0tEEMQoFrGqtKSVQ49rtchwsmmdBkXpTZ8Gt95bQQSvWJ0WsoKKUcDdNtkT+Kik6aXQ3xXT5hUtsuBZkJyZYQaKU+mtygjRHdAk1T9eQavuTornrS/wZKNwf9W90IPu1CjWXJnQgxIPRtIz+YMqJ3rGpaMVYhaek73yskKGQCqELEYX/4uP1YvnXaf0eXWkpPro6nntxvHTLS7d8X93yryaHlzZ5aZPvqU26se3hiHbVsafZQH5GK+EyszOVqoj3XghX8KkrWaFHBcwxmPmgNG+GZTsXAxdnA4LZiXPV0QotYg6pmM8TMVuqyPFFSYdXO6pSwGPytqvMV9pXIHxI2vvmzInefsEqWHqiYc6wnufhgVkJJbvzXQ56/N7l82arwY3Z5XIBCsTdDNPhF1GUyCvsrpie6E2H7wHsQ/5Hw3CrMipNj+waMJg8Og+8tBg/PIB3o2AveSnW0DRbTPQh2raV7U81xSMU642CA0aTS3LdQGmSrMuhnP7h4TCyww83PWv9cSaU+inP+010EuoBbS1PoOCejg+odbtp1YkN/+9O5MNG3HtwRKh5fnuVDV7cioGzxKNh66xxqTUFHsmOD92M+mL5x+pWSq6n61Uuu4ifhffj5wyI70LrbvlurwGLbld33w/IJ8vvbyD8+D0=
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create connector connector name inquiry"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/connector/{connector}/name-inquiry"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create connector connector name inquiry

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"connector","in":"path","required":true,"schema":{"type":"string"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NameInquiryRequest"}},"text/json":{"schema":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NameInquiryRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NameInquiryRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"accountName":{"type":"string","nullable":true},"accountInstitutionCode":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"bankVerificationNumber":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NameInquiryResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"NameInquiryResponseNIP_ResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"accountName":{"type":"string","nullable":true},"accountInstitutionCode":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"bankVerificationNumber":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NameInquiryResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"NameInquiryResponseNIP_ResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"accountName":{"type":"string","nullable":true},"accountInstitutionCode":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"bankVerificationNumber":{"type":"string","nullable":true},"kycLevel":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NameInquiryResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"NameInquiryResponseNIP_ResponseHandler"}}}}}}
>
  
</StatusCodes>


      