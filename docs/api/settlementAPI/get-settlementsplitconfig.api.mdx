---
id: get-settlementsplitconfig
title: "Get settlement split config settlementSplitConfigId"
description: "Get settlement split config settlementSplitConfigId"
sidebar_label: "Get settlement split config settlementSplitConfigId"
hide_title: true
hide_table_of_contents: true
api: eJytk01v2zAMhv+K8Z42QK27HXUrhiLNYcCwtKfAB9VmEqG2pEp0MMPQfx9o56PrmgEbdrIsUi/Jh+QIHygatt4tG2hsiVfE3FJHjlNoLdfebewWCmy2CXqNs30l9i++IVQKwUTTEVMUpxHOdASN9NZZxJYNFKyDRjC8g0Kkl95GaqA59qSQ6h11BnoED2GS4WidJLHxsTMMjb63DXJWp0iLu6+r28eH+6P0jkxD8S/Ec67EOQXvEiWxf765kU9DqY42CCNorPq6ppQk9hvLgrg411tM9IoZX3GZQ0e88wf0UDMRjdIEW54fXU1iV7NYOV5Qy1BIFPfHHvSxFRDMIenytVpD++stdSmYIfm2l/yva99BCFi38RMcy+3EdfYrzm0vbr8toSBh5sL3n5AVgk/cGSdvjy35JyC/QD01iekHl6E11kmsqbLxAGsNE+xU+ru4oKAvRasUdj6xaIzjk0n0GNuc5fqlpzhAryuFvYnWPAmM9YjGJjk30BvTJvot3do7JicT+uH7YfA+FrI975VxuDRuEJ6m7eUPCs80/GF3cpXVcbz/e0pz6FfLdEpLhuM0rIu7B+T8E5RbeqQ=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settlement split config settlementSplitConfigId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settlement-split-config/{settlementSplitConfigId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settlement split config settlementSplitConfigId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementSplitConfigId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      