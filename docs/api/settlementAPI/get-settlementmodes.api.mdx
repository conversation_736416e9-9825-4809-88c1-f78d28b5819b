---
id: get-settlementmodes
title: "Get settlement modes"
description: "Get settlement modes"
sidebar_label: "Get settlement modes"
hide_title: true
hide_table_of_contents: true
api: eJztVU1PGzEQ/SvRnFrJJbTHveWAAKlIiMApymGyHhJTr208s1Gjlf97NZuvDVCVQy9InLLKvMy8jySvg5goo7gYri1UsCSZkoinhoI00RKDAcElQzWD4+QmWoK5gYQZGxLKOu8gYENQweXFzXTycH8FBlyAClaEljIYyPTcukwWKsktGeB6RQ1C1YFskn6SJbuwhFLmCuYUAxPr/Mf5ub5Y4jq7pHShgmlb18TKsI5BKEi/iX7LOHnUy93rC3HxRLWAgZRVubjtft6tOgIXMXrCAMVAQ8y4pNc8DYTWe1x42ioqBizK8BzmjBv1Qajhf9PYGviOM+zb5TuAxQBa69Qv9LeDU4/omQyIE98nNoxdw73bma/y//fOn45l/3yFwXrK0G9Nybu6/y6Onzh+5vfB8ut/eZ/BfbTgii4+/WO9JBnxYcVo3wMNySruWkIdRFlBBWNMbnxEf9ujmfJ6Xwxt9toDIomr8RBsaX22pIYTbjj6Vu+f1bEBLQAXHmNv9UFXjxsdxY0mt9dgQM9sia+/q28psjQYjrn+TdCJ6kOmgwYpZku924mdASbXa3shd25gFVkU0XULZHrIvhR9+7mlvIFqNjewxuy2ec7mxexrUf2xjnVgD1m+YHboN/hyt+vQryNt5rcY/6LNaQmv0bcKg2K63XRS15RkMBuumA+ivry4h1L+AKvO3u4=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settlement modes"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settlement-modes"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settlement modes

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"name":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true}},"additionalProperties":false,"title":"GetSettlementModeResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementModeResponseListResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"name":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true}},"additionalProperties":false,"title":"GetSettlementModeResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementModeResponseListResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"name":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true}},"additionalProperties":false,"title":"GetSettlementModeResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementModeResponseListResponseHandler"}}}}}}
>
  
</StatusCodes>


      