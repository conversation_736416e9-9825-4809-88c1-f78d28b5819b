---
id: get-batchperiod-get-code
title: "Get Batch<PERSON>eriod Get batchPeriodCode Code"
description: "Get Batch<PERSON>eriod Get batchPeriodCode Code"
sidebar_label: "Get Batch<PERSON>eriod Get batchPeriodCode Code"
hide_title: true
hide_table_of_contents: true
api: eJztWEtv2kAQ/itoTq20qdP05ltSRSSHtiiPU8Rh8A6w6Xp3sztGRZb/ezU2DwdQS9IeORkzM9+8R3zU4ANFZOPdrYYcZsRXyMU8UDReD4m/ek2ggHGWIH+CVjhqhTBWEDBiSUxRhDU4LAlymGyVVubGQQ4BeQ4KIr1UJpKGnGNFClIxpxIhr4GXQcwTR+Nm0DRqgzi8/nZ/+fhws4aaE2qKbwIbi3IK3iVKIr84P5eHplREE6QAkMN9VRSUEigovGNy3CLRL86CRfFc73vwk2cqGBSEKLVk0+GnFdRWceK9JXTQKCgpJZzRfpwKXGUtTix1GTUKNPIR7no1l0bu4059LJEhh6oyWmLY7dJRsdAUK8uHk5pGXz6Y8jgk9kerdrN446vYUzeOadaOwCYx4/jLhQRSREImfcl/rINGpjOWIKQhXpupeZ/N1fKoNLbq1yUae5RNImZLJTkeRT81lvrzhDHiUjaCqUx/n5CSYjFHx1dVMo5S+o5H1n9t+MNNPEZt3OzwhO0ZYsX+fpPBoaERJa2NbB/aUS/cKdpECtiwbfef+H63FHerbZZW7Lh+A2zvoG0B/81+/bxBpy3FDi4Ea4r2zmbPyZ8uyemSnC7J6ZK845K0v0ZOJ+R0Qk4n5HRC3nNCmqbdnT7tGRIPerYDed/ZyMGKyZXEc79iitJV4XQ5ZBhM1kPIhsRZvQPRZCuMRHGxpoxVtMLnmEPKs2w7YmeaFp9mVKaAy+RtJZF+KnwJQuSMm/q2g5vUW73Bti2Dy9EtKBA3XYqLz9Ka4BOX2J7ONbM8PvVXNduMT48dNqpLp16V5QkwGFCvSLMSj6Ag3yfJ7WOsYO4Ti3FdTzDRY7RNI1+/VBSXkD+NFSwwmm7CnmrQJslnvRmInTg3TBY+3K3Y8seBUPpD8a+**************************/H/Hkrnssf8N+HIHwOd9LIoKHBP1ocY96Z1eP0ATfMbviHu0g==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get BatchPeriod Get batchPeriodCode Code"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/BatchPeriod/Get/{batchPeriodCode}/Code"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get BatchPeriod Get batchPeriodCode Code

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"batchPeriodCode","in":"path","required":true,"schema":{"type":"string"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      