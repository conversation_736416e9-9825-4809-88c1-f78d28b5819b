---
id: get-settlementaccount-all
title: "Get SettlementAccount all"
description: "Get SettlementAccount all"
sidebar_label: "Get SettlementAccount all"
hide_title: true
hide_table_of_contents: true
api: eJytU8tu2zAQ/BVjTi3ARml6082HwM2hRVAnJ8MHWlpLRPkKuTLqCvr3YiXbiR0jp54Ecmd3Z4ajHiFS0myCf6hRoiFeErMlR551VYXO89xaKLBuMsoVXsvzqYy1QtRJO2JKAunhtSOUeNQN/ezchhIUjEeJl47SHgq5aslplD14HwVqPFMz4rYhOc3T1bc7DIM6m7c0f+n/TFvc/1jOn5++H6e1pOuxJ9FLZxLVKDl1dGV85mR8g2FYCzjH4DNlqd/d3sqnplwlE8VUlFh2VUU5y+6LyoJ49s7OmR7tdsRtOLwIxGBuUaLQ0RTvWoqpJVPaHV+gS1YkMcdcFkU+dXypaXfTkMtR73OwnTC5qYKDaDF+G0aZhu3o0IR7w3E2f3yAgqyZJOy+YlCIIbPTXnqP5n4g7cyEk6lMf7iIVhsvE0f+/UH2CjoaqCvRU5CRa4U2ZBZg3290pudkh0Gup4SIIbXJemPlUbfaZlL4TfvLiO607YQKJCUfNBwy+ApfyyEZwaNcrQd1DNPVzRf6q+CZvGT0069D8j7P5Ie75stE4k10z0icQrO4f8Iw/AOLJU76
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementAccount all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementAccount/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementAccount all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      