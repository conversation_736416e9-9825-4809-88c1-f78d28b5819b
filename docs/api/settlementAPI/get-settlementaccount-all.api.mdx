---
id: get-settlementaccount-all
title: "Get SettlementAccount all"
description: "Get SettlementAccount all"
sidebar_label: "Get SettlementAccount all"
hide_title: true
hide_table_of_contents: true
api: eJytU7Fu2zAQ/RXjTS1ARGm6afMQuBlaBHUyGR7O0lkmSpEMeTLqCvr34iQ7iRMjUyZJvHeP99499QiRE4kN/q5GiYZlySKOW/ZCVRU6L3PnYCDUZJQrvJTnUxlrg0iJWhZOCunhqWWUuKeGf3XthhMMrEeJp47TAQa52nFLKHvIISrUeuFmxG1Dakmmo+83GAZzxre0//hz2Ba3P5fzx4cfJ7YdUz32JH7qbOIapaSOL9BnSdY3GIa1gnMMPnPW+s31tT5qzlWyUU1FiWVXVZyz3v2msmCZvbNzRqPdLcsuHDcCNVh2KFFQtMW7lmJqyZz2pw10yakkkZjLomi4zV1sEtV8pe+VC119RTFCJVi/DaM6K240htsc6fBqtNn8/g4Gyj5Nvv+GwSCGLC157T15+oGiM+3PXgr/lSI6sl4Zx7H7o9oVKFqYC4kzUMq1wS5kUWDfbyjzY3LDoMdTMNSH2mbaON3lllxmgz98eJvMPblOR4GG44OGY/Re4Gv9SFbxKFfrwZwydPHmN/qr4IW9RvPL72Pgvs70P7vkyzTEq8SeDfGclcXtA4bhP195Sy8=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementAccount all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementAccount/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementAccount all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      