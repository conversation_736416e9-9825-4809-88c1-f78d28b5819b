---
id: get-settlementprofile-merchant-profile
title: "Get SettlementProfile merchant merchantId profile"
description: "Get SettlementProfile merchant merchantId profile"
sidebar_label: "Get SettlementProfile merchant merchantId profile"
hide_title: true
hide_table_of_contents: true
api: eJztVstu2zAQ/BVjTy3ARmmPuqVA4OSQNsjjZPiwFtc2U4pkyJVRQ+C/F9TbsYGmRY4+6cHlLHd2pJ0arCOPrKy5lZDDhviRmDWVZNh5u1aa7sgXWzR83z6CAMZNgHwBY2i/thTg0GNJTD6F1GCwJMih7EBuJQhQBnJwyFsQ4Om1Up4k5OwrEhCKLZUIeQ28d2lnYK/MBgSsrS+RIYeqUhJiFAP4/Pru8er56aaH3hJK8v8AHuMyBQdnTaCQ1r9dXqaLpFB45RI/kMNjVRQUAggorGEy3CDRb86cxpS5Ps5gVy9UMAhwPlHNqsUPHdQYuLJWExqIAkoKATd0igRTaY0rTW1FUYBEfk+6t51K3f4rxWJo2/cqKEMh/GgIf8ex+o0/zcqil8psTmc82ogV21FXp/hJQVKq1BPU95My16gDCWDFulEFcS/dI6E+dL3+WLT+eoNGavItuHNaFc0Xlr0EexbJWSTHImn+IWd1nNVxQh0x4R/OoTnx7Ahh1vM1G2ftzA0zuyTe2m7Ep96m6ZtDhk5lR1BZj5DVI1bMRrBAftcP+MrrNHKZXcizbFTJF0m7iw2VweE+WF2ls18UtoQ0a5VZ26YxAzVN3KSq2dX9LQhIadqid1+TmpwNXGLznfTD/z/IOKBzkMdkkkfR1lV3RC0AnQJxwvGMOgUB+YHNcaMp2trACaWuVxjo2esY0+vXivwe8sVSwA69alW8qEGqkO7lIKE3Bx7sB3x66CzO51kyZqcK6V6i2Sc+UVfpCQT8ov2hMYvLKHrv9OGnaLNNnNpwkmTk2tWroiDHk7UpxHKi4fn1E8T4B7+P0Qk=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementProfile merchant merchantId profile"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementProfile/merchant/{merchantId}/profile"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementProfile merchant merchantId profile

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      