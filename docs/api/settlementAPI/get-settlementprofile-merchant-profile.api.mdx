---
id: get-settlementprofile-merchant-profile
title: "Get SettlementProfile merchant merchantId profile"
description: "Get SettlementProfile merchant merchantId profile"
sidebar_label: "Get SettlementProfile merchant merchantId profile"
hide_title: true
hide_table_of_contents: true
api: eJztVstu2zAQ/BVjTy1ARGmPuqVAkOSQNsjjZPiwFtcyU4pkyFVQQ+C/F9TbsYGkRY4+6cHlLHd2pNkGrCOPrKy5kZBDSfxAzJoqMuy83ShNt+SLLRq+6x5BAGMZIF/CFDqsrQQ49FgRk08hDRisCHKoepAbCQKUgRwc8hYEeHqplScJOfuaBIRiSxVC3gDvXNoZ2CtTgoCN9RUy5FDXSkKMYgS/urx9uHh6vB6gt4SS/D+Ax7hKwcFZEyik9e/n5+kiKRReucQP5PBQFwWFAAIKa5gMt0j0hzOnMWVuDjPY9TMVDAKcT1Sz6vBDDzUFrq3VhAaigIpCwJKOkWBqrXGtqasoCpDIH0n3tlOp2+9SLMa2/aiDMhTCz5bwDxxr2PjLrC16qUx5POPBRqzZTro6xk8KklKlnqC+m5W5QR1IACvWrSqIB+keCPW+7/Xnog3XazRSk+/AndOqaL+w7DnYk0hOIjkUSfsPOanjpI4j6ogJf9+HrogXBwiLga/F5LULN3p2Rby1vcWn3ib3zSFDp7IDqGxAyJoJK2YTWCD/Ohh87XWyXGYX8iwrqQq1Kz1KOkv3hba1PEPnIFmsMhvb9mNkpAoOd7NiFhd3NyAgoXe1vn5LInI2cIXt5zF4/n9wsMfiqIqZgUfRldP0/CwBnQJxZNCZ5AkC8r3pxk2z0NYGTihNs8ZAT17HmF6/1OR3kC9XAl7Rq068ywakCulejsp5c+Bx6oAv9/1k83WR5rFjhfQv0ewSn6jr9AQCftNufx6LqyiGkenTT9Flmw1o40nS/NatXhQFOZ6tzSFWM+leXT5CjH8BuLfNPg==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementProfile merchant merchantId profile"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementProfile/merchant/{merchantId}/profile"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementProfile merchant merchantId profile

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      