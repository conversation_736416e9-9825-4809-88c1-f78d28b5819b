---
id: get-tunnel-all
title: "Get tunnel all"
description: "Get tunnel all"
sidebar_label: "Get tunnel all"
hide_title: true
hide_table_of_contents: true
api: eJztWN9P2zAQ/leqe9okQxl7y1u1IUDaporCE+rDNb6mZo4d7EsFq/K/T47TNpSMBcS0l/Slqn0/vzvfqd8GbEEOWVlzKSGBjPi6NIb0RGsQwJh5SG5hRsyacjLNLcwFFOgwJyYXJDZgMCdIYIoZ/SjzBTkQoAwkcF+SewQBPl1RjpBsgB+LIKoMU1bLLa3LkePR51OoKvHE3kz9ovexdn72fTa5ub7YWlsRylrH0X2pHElI2JXUYd6zUyaDqpoHYV9Y48mH+9OTk/AlyadOFQFISGBWpil5DwJSa5gM15bogceFxuB589yDXdxRyiCgcKEmrKJ935jaCy6s1YQGKgE5eY8ZPY9TgCm1xoWmmFElQCK33aFzGIBUTLn/exhKdjnZQV2WSoaAIs59omnj1UPe6zLrJYgpqzV1w5U6QiY54Rdzkch0xCqnGmEr1VK9TiddYXgk/p+j3TjqhUuBjg25L1b2K5DyX2mJpeZuKJWf/G+gQ7GlVKGFUE9b+C1RexLAinX96Im/RKD2Y+yqecJ1zz5N/RVmD8fiC1ZDAXaTsc/Uigr16OsnvlTO8/QP02DfOk51VVvj23XZMuqg7HtGWitcUWqd7Kti6OHN8RWO1sqW/m3679AO31QEV24PLtBITS62cFFoldYbeHzn7bAbht0w7IZhNwy7YdgN8R/DsBSGpTAshWEpDEthWApV+BzMwmBoxLX+CGvWLide2YbNC5MCeQUJjLFQ4yg3jnKe3HpL35VOBz6MufDJeOx3gR1JWh9nlPsCH73VZfB5nNocAhGmzNLWAO1SquVG+7xGk+klCAhuYrDrT3WnWs851oN8y8wdJtE971scWiVi0JsmwVvAIpQjWgEBwc5cwMp6DrebzQI93ThdVeE4Eokhdal8KJ/cVegnPR4ymWvUZfAPgUx8QaGhKvfi8/DDqdgft/NKbDnHTs8HSe/IQ/hw1RCUH0chxS4wYhAthvNJzPF2kqZUcOuubWLe6pzzs2uoqt+auqMV
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get tunnel all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/tunnel/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get tunnel all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      