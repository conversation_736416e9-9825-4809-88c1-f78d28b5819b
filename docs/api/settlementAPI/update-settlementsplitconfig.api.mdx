---
id: update-settlementsplitconfig
title: "Update settlement split config settlementSplitConfigId"
description: "Update settlement split config settlementSplitConfigId"
sidebar_label: "Update settlement split config settlementSplitConfigId"
hide_title: true
hide_table_of_contents: true
api: eJztVstu2zAQ/BVjT33IcR6N0+rmBkWbQ4Egj14MH9biymYqkQxJOTUE/XuxpC0pTtOmRW+JLxJEenZ3ZsjdGrQhi15qdSYghcoI9HRJ3hdUkvLOFNJnWuVyAQl4XDhIp9CtX/L6qRYEswQMWizJk+VNNSgsCVJwu5sZ7ExAAlJBCgb9EhKwdFtJSwJSbytKwGVLKhHSGvzaBBhvpeIkcm1L9JxrJQU0TdJG+vzp6+Xk+urLFnpJKMj+BXjTzOJmcv6jFmvekWnlSXl+RWMKmQWyRjdOK/72AErPbyjzkICxTK2X5Hg1pviwGlUVBc4Liqk1CQTGr8KuGkhVJROeFxggyWakPC4C3btQXnrGgcsWYQs3yTJdKe96CaC1uGaiPJXuz+kHmG9YVP0iVFXOA7+tJEJXXAvHbVXfBGd7PUFMQCEkM4zFeS+DHAtHXYkTIXY8uIlyEcXjDHaYfSr09c4JaB3eQjcJePrhXyzwrC3QvwzevH3xwvP1AmNZckYrF9k53N/nhyCXWWk4BMtQZRk5F4q6vxJDDDqKBoHeQWy7g8f7Z0l+qUXsoRk30dBLUxihkaPub8MAN4xwo/oRvAZYJbvadu/KFtxCvTcuHfXRBK32FlQ6g2uni4qL2Mt0Cdw7+RhcdP3z0w8sTUGd4Tule77e2nnHm9P7Ntt/xERwlOP743z8bnh8cnAyfHc8PhzOj/JseJh9GB/l4zHmOIZm1vBQkOvguo22n2MVg07dweT8DBJgEqI2qwP2jtHOl6h6dfyzZvekbw9A6CemQKk4XKC+3qg5BTQS+sXf0xMSSB+LNktgqZ1njLqeo6NrWzQNf76tyK4hnc4SWKGV8VxMaxDS8btoj8FOuu0wBK8uNjPV6wEkvy5je7UovlhWUUWABL7T+jdjYVBqM7n995Ri6N6c2KbFY2RcPY2Aw2DObseD4Y/93p7A88nVKePNN1NjySNxChbvePzEu1i5DlmHOyJ8q6FAtaj4Ak8hgvLvJ2L3Epc=
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update settlement split config settlementSplitConfigId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/settlement-split-config/{settlementSplitConfigId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update settlement split config settlementSplitConfigId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementSplitConfigId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"splitType":{"enum":["flat","percentage"],"type":"string","title":"SplitType"},"splitAccounts":{"type":"array","items":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"},"nullable":true}},"additionalProperties":false,"title":"UpdateSettlementSplitCodeRequest"}},"text/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"splitType":{"enum":["flat","percentage"],"type":"string","title":"SplitType"},"splitAccounts":{"type":"array","items":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"},"nullable":true}},"additionalProperties":false,"title":"UpdateSettlementSplitCodeRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"splitType":{"enum":["flat","percentage"],"type":"string","title":"SplitType"},"splitAccounts":{"type":"array","items":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"},"nullable":true}},"additionalProperties":false,"title":"UpdateSettlementSplitCodeRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      