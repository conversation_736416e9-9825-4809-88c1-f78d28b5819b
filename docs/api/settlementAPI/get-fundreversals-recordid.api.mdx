---
id: get-fundreversals-recordid
title: "Get fund reversals recordId"
description: "Get fund reversals recordId"
sidebar_label: "Get fund reversals recordId"
hide_title: true
hide_table_of_contents: true
api: eJztWE1vEzEQ/SvRnEByCR+3vYWqlB5AVQqnKoeJPUkMXnuxxxFRtP8dzW6y3UApGzgh7SlKPG888zx+jt4eQkUR2QZ/Y6CANfG77E2kLcWELs1Jh2isAQWM6wTFPcj6/LAOCwUVRiyJKcrqHjyWBAXEBngjQOuhgG+Z4g4UJL2hEqHYA+8qCUwcrV+DglWIJTIUkLM1UNeqy1XFoCkl+nOyZQiO0IMCQyvMjqFYoUvUz3Z99eFu9vnT+2OyDaGhCAoifcs2koGCY6bfl1rXCwlOVfCJkqy/fvlSPgwlHW0lZEIBd1lL1aBAB8/kuclE33laOZSd97/uEJZfSDMoabmiyLbNnw6pfm20VlBSSrimxyj12TlcOmo7qhUY5AHbWTPgfBRwRJ9QH2dnAKKkqDfoeWA4liF7vsWTenwul81xdeEmZOnxtKI5rSiS18N4SRS3VtNtDFtrKH5sJuV83GUwf4W7c3k9CCfseXKDYuNZDOicOJQU36L/OriNPmgwZ0fQTGs53r/GtXMwjAmOu0sB9cKtZ1qfDpL1/Oa1zNGqkTg5JHTnDdKDVD16V1PIUdNZHD9Ahk9lAzmX31PUcHZ1JGQyM37yThtkumBbNve0DMau7DkYUQNjrNxsdLc9tWrkXQFbdo24t+/X8X2aH1T6nxMcP9+jN45im6+qnNXN2zn9ksKo6KOij4o+Kvqo6P+tojf/zkcpH6V8lPJRykcp/3+lvJaUp3bMNfFERmfSeVuTnkdVEm/CwQATyUXeQAFTrOxUQBcdaNoDyQweba8cnXhJzFUqptNEzI5K8nxhaPtiTWWqcJeCy1LNCx1KEBPJ+lVoSO76a+Imdx18Mru9AQWyTdvG9pWcURUSl9g8U0dX68n2TqjoDrVnRtWq7WB/aP0esLLQXraH5huT7JB0oWATEkvofr/ERJ+jq2v5uXXnhBRjk0yj6Q7yK+1OvcEtuiylgBh0vwnv238P8Qv5Em077feLWh2NvEc3/omAzpGDZ/OD6/d8Ih7nY8S0VfRsw5Oi29WZ1lRxb62fYtGbr+urT1DXPwCMLYuv
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get fund reversals recordId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/fund-reversals/recordId"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get fund reversals recordId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"recordId","in":"query","schema":{"type":"string","format":"uuid"}},{"name":"processed","in":"query","schema":{"type":"boolean","default":false}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"merchantId":{"type":"string","format":"uuid"},"amountPaid":{"type":"number","format":"double"},"transactionReference":{"type":"string","nullable":true},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"serviceProviderSlug":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"customerBankCode":{"type":"string","nullable":true},"customerBankName":{"type":"string","nullable":true},"customerAccountName":{"type":"string","nullable":true},"customerAccountNumber":{"type":"string","nullable":true},"retryCount":{"type":"integer","format":"int32"},"fundReservalReference":{"type":"string","nullable":true},"processed":{"type":"boolean"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetFundReversalResponse"}},"additionalProperties":false,"title":"GetFundReversalResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"merchantId":{"type":"string","format":"uuid"},"amountPaid":{"type":"number","format":"double"},"transactionReference":{"type":"string","nullable":true},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"serviceProviderSlug":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"customerBankCode":{"type":"string","nullable":true},"customerBankName":{"type":"string","nullable":true},"customerAccountName":{"type":"string","nullable":true},"customerAccountNumber":{"type":"string","nullable":true},"retryCount":{"type":"integer","format":"int32"},"fundReservalReference":{"type":"string","nullable":true},"processed":{"type":"boolean"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetFundReversalResponse"}},"additionalProperties":false,"title":"GetFundReversalResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"merchantId":{"type":"string","format":"uuid"},"amountPaid":{"type":"number","format":"double"},"transactionReference":{"type":"string","nullable":true},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"serviceProviderSlug":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"customerBankCode":{"type":"string","nullable":true},"customerBankName":{"type":"string","nullable":true},"customerAccountName":{"type":"string","nullable":true},"customerAccountNumber":{"type":"string","nullable":true},"retryCount":{"type":"integer","format":"int32"},"fundReservalReference":{"type":"string","nullable":true},"processed":{"type":"boolean"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetFundReversalResponse"}},"additionalProperties":false,"title":"GetFundReversalResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      