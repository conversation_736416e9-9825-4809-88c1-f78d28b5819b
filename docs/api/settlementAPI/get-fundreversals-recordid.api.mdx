---
id: get-fundreversals-recordid
title: "Get fund reversals recordId"
description: "Get fund reversals recordId"
sidebar_label: "Get fund reversals recordId"
hide_title: true
hide_table_of_contents: true
api: eJztWE2P0zAQ/SvVnEAylI9bbgXBsgfQqgunVQ9Te9oaHNvY44qqyn9Hk7TdFBZI4YSUU9p63mTmefxcvT2ESAnZBn9toII18dviTaItpYwuz0mHZKwBBYzrDNUdyPr8sA4LBRET1sSUZHUPHmuCClILvBag9VDB10JpBwqy3lCNUO2Bd1ECMyfr16BgFVKNDBWUYg00jTrliiloypn+nGwZgiP0oMDQCotjqFboMvWzXb15fzv79PHdMdmG0FACBYm+FpvIQMWp0K9LbZqFBOcYfKYs6y+ePZOHoayTjUImVHBbtFQNCnTwTJ7bTPSNp9GhvHn/8xvC8jNpBiUtR0psu/z5kOrnRhsFNeWMa3qIUl+cw6WjrqNGgUEe8DprBuyPAk7oM+rj7AxA1JT0Bj0PDMc6FM83eFaPL/Wy3a5TuAlFejyvaE4rSuT1MF4ypa3VdJPC1hpKH9pJuRz3Opi/wt26sh6EE/Y8uUGx6SIGdMkcakqv0H8Z3EYfNJizI2imtWzvX+O6ORjGBKfdawH1wq1nWp8PkvX88oXM0aqVONkkdJcN0r1UPXhWcyhJ00Uc30OGT2ULuZTfc9RwdnUiZDIz/u2ZNsj0hG3dntM6GLuyl2BEDYyxcrLR3fTUqpV3BWzZteLe3V/H+2l+UOl/TnB8vkNvHKUuX4zO6vbunH7OYVT0UdFHRR8VfVT0/1bR23/no5SPUj5K+Sjlo5T/v1LeSMpzO+aKeCKjMzl5W5OeR1UTb8LBABPJRd5ABVOMdiqgJyfQtAeSGTzaXiU58ZKYY66m0zXVucR1QkNP5bN2oZinGCOId2T9KrTcntqqc8Td5JaYHdXkeTK7uQYFkr2rfvtctiaGzDW2t9PRzPptV2cMnPay50E1qit8f+j4DjBa6M7Yfc+tN3ZIulCwCZkldL9fYqZPyTWN/NyZcsKFsVmG0Jz27wvtzi3BLboipYD4cr8I77t+9/EL+ZJsN+R3i0Yd/bsHX/wDAScjDh7ND2bf44lYmw8R01XRcwvPiu5WZ1pT5N5aP8WiN1ZXbz5C03wHH1qH5A==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get fund reversals recordId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/fund-reversals/recordId"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get fund reversals recordId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"recordId","in":"query","schema":{"type":"string","format":"uuid"}},{"name":"processed","in":"query","schema":{"type":"boolean","default":false}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"merchantId":{"type":"string","format":"uuid"},"amountPaid":{"type":"number","format":"double"},"transactionReference":{"type":"string","nullable":true},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"serviceProviderSlug":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"customerBankCode":{"type":"string","nullable":true},"customerBankName":{"type":"string","nullable":true},"customerAccountName":{"type":"string","nullable":true},"customerAccountNumber":{"type":"string","nullable":true},"retryCount":{"type":"integer","format":"int32"},"fundReservalReference":{"type":"string","nullable":true},"processed":{"type":"boolean"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetFundReversalResponse"}},"additionalProperties":false,"title":"GetFundReversalResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"merchantId":{"type":"string","format":"uuid"},"amountPaid":{"type":"number","format":"double"},"transactionReference":{"type":"string","nullable":true},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"serviceProviderSlug":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"customerBankCode":{"type":"string","nullable":true},"customerBankName":{"type":"string","nullable":true},"customerAccountName":{"type":"string","nullable":true},"customerAccountNumber":{"type":"string","nullable":true},"retryCount":{"type":"integer","format":"int32"},"fundReservalReference":{"type":"string","nullable":true},"processed":{"type":"boolean"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetFundReversalResponse"}},"additionalProperties":false,"title":"GetFundReversalResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"merchantId":{"type":"string","format":"uuid"},"amountPaid":{"type":"number","format":"double"},"transactionReference":{"type":"string","nullable":true},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"serviceProviderSlug":{"type":"string","nullable":true},"channel":{"type":"string","nullable":true},"reference":{"type":"string","nullable":true},"customerBankCode":{"type":"string","nullable":true},"customerBankName":{"type":"string","nullable":true},"customerAccountName":{"type":"string","nullable":true},"customerAccountNumber":{"type":"string","nullable":true},"retryCount":{"type":"integer","format":"int32"},"fundReservalReference":{"type":"string","nullable":true},"processed":{"type":"boolean"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetFundReversalResponse"}},"additionalProperties":false,"title":"GetFundReversalResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      