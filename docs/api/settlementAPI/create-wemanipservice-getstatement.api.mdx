---
id: create-wemanipservice-getstatement
title: "Create WemaNIPService GetStatement"
description: "Create WemaNIPService GetStatement"
sidebar_label: "Create WemaNIPService GetStatement"
hide_title: true
hide_table_of_contents: true
api: eJztWG1v2kgQ/itoPt21m0DoVb3zN9ogGinhIiA63SGEBnsC29rr7e44LUL+76exSTAvlfBdPpovWOt5ZufNj73PBlJLDlmn5iaCAEJHyPQXJWi09eSedEgDYs/IlJBhUMC49BBMQYyGN/fj0ghmCiw6TIjJyf0NGEwIAhj078a9h8lnUKANBLAijMiBAkffMu0ogoBdRgp8uKIEIdgAr60gPTttlpDns9KYPH9Mo7VYhKlhCSfYAFob67BIof3Fp0bWjlyliy8USvTWScKsyRfYMEwzw8MsWZA73lmByeIYFzGVMeYKPKPja2Q6Zf2YugQZAoiQ6YJ1QpArIBPVAeQKMIq05IPxfSXcR4w9KWDNEk9R/wFxr0xh/NyhUVmowg/TD25qsl+T6ri8edsUp1Ic8eTI29T4MttupyN/EfnQaSsbQADjLAzJe1DVh7CYNBujrlVNv3W1M1ykaUxopAoJeY/Lk1U4KnKEXN0OncO18A1T4s9uaslXZ+zmQtefT27u+ue3NA5Xg5qQp8fr+XVvUgNhPdeFkGH3d00MOxzWhDxhnPXrb2N0dFZDLDrWYRaj82fZi2tHCbqv59lHxaN+tByeXl5gjCasjpIp6aKaaZoJpIx9Mp+MesP5eHQ7Hz7cVXDaMC33gdrwu67gtPHsEjM5gPw8ieVtrQb8b1YpeURCPQjllTzfav9y/RlNFJM7Ivi69N4QUkNIDSE1hPSqhPSfvsIbJmqYqGGiholekYmK893+We5TIfe09qWc1oB2LkHYhlepiEM29QVDIK8ggDZa3d5Htg+QIiA9q0GZi0X8YbY+aLeXlPjMLh1GdCnXYZxm0SVaCyL2CFmOdoJP/wcmNqYT5+9dnyrHbOh2ur9ddD5cdP+YXL0P3l8F3d8vOx+u/oHK0frnRsUAPabFMGzLPaDEW1y3xsQcF8m1evc3oECyKyv5dFVMbOo5wYLot+rXWRXea8rLDFbO1Lkq67fZFn8KaDWoQw1OwZ7jmYKVtCyYwmazQE8PLs5zWf6WkVtDMJ0J/zhdTuB0lqtndU46FmkvN6KX6TsI8+X4D7+MtlLery0RCE+F/5XW+1qg8J6YQa4227ufSocXE3Gwszj6oN8hemFIliu21U1nldG9/3M8AQWLrXyYpJFYO/wuOiR+lzgUpEVqBe8VaxuI0Syz4k0L5c7y+xcnj06s
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create WemaNIPService GetStatement"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/WemaNIPService/GetStatement"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create WemaNIPService GetStatement

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementRequest"}},"text/json":{"schema":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"accountname":{"type":"string","nullable":true},"rcrE_TIME":{"type":"string","format":"date-time"},"lchG_TIME":{"type":"string","format":"date-time"},"vfD_DATE":{"type":"string","format":"date-time"},"pstD_DATE":{"type":"string","format":"date-time"},"entrY_DATE":{"type":"string","format":"date-time"},"traN_DATE":{"type":"string","format":"date-time"},"valuE_DATE":{"type":"string","format":"date-time"},"tranid":{"type":"string","nullable":true},"particulars":{"type":"string","nullable":true},"tranremarks":{"type":"string","nullable":true},"dr":{"nullable":true},"cr":{"nullable":true},"balance":{"type":"number","format":"double"},"parT_TRAN_SRL_NUM":{"type":"integer","format":"int32"},"instrmnT_NUM":{"type":"string","nullable":true},"gL_DATE":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementResponse"},"nullable":true}},"additionalProperties":false,"title":"WemaGetAccountStatementResponseListResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"accountname":{"type":"string","nullable":true},"rcrE_TIME":{"type":"string","format":"date-time"},"lchG_TIME":{"type":"string","format":"date-time"},"vfD_DATE":{"type":"string","format":"date-time"},"pstD_DATE":{"type":"string","format":"date-time"},"entrY_DATE":{"type":"string","format":"date-time"},"traN_DATE":{"type":"string","format":"date-time"},"valuE_DATE":{"type":"string","format":"date-time"},"tranid":{"type":"string","nullable":true},"particulars":{"type":"string","nullable":true},"tranremarks":{"type":"string","nullable":true},"dr":{"nullable":true},"cr":{"nullable":true},"balance":{"type":"number","format":"double"},"parT_TRAN_SRL_NUM":{"type":"integer","format":"int32"},"instrmnT_NUM":{"type":"string","nullable":true},"gL_DATE":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementResponse"},"nullable":true}},"additionalProperties":false,"title":"WemaGetAccountStatementResponseListResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"accountname":{"type":"string","nullable":true},"rcrE_TIME":{"type":"string","format":"date-time"},"lchG_TIME":{"type":"string","format":"date-time"},"vfD_DATE":{"type":"string","format":"date-time"},"pstD_DATE":{"type":"string","format":"date-time"},"entrY_DATE":{"type":"string","format":"date-time"},"traN_DATE":{"type":"string","format":"date-time"},"valuE_DATE":{"type":"string","format":"date-time"},"tranid":{"type":"string","nullable":true},"particulars":{"type":"string","nullable":true},"tranremarks":{"type":"string","nullable":true},"dr":{"nullable":true},"cr":{"nullable":true},"balance":{"type":"number","format":"double"},"parT_TRAN_SRL_NUM":{"type":"integer","format":"int32"},"instrmnT_NUM":{"type":"string","nullable":true},"gL_DATE":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementResponse"},"nullable":true}},"additionalProperties":false,"title":"WemaGetAccountStatementResponseListResponseHandler"}}}}}}
>
  
</StatusCodes>


      