---
id: create-wemanipservice-getstatement
title: "Create WemaNIPService GetStatement"
description: "Create WemaNIPService GetStatement"
sidebar_label: "Create WemaNIPService GetStatement"
hide_title: true
hide_table_of_contents: true
api: eJztWNuO4kYQ/RVUT7k0A0Oy2sRvZAexI82QETCKEoRQYddA79rd3u4yuwj536OyGTCXlXAyj/AC6q5TXTcf3GcDNiWHrK25jyCA0BEy/UUJGp16cisdUp/YMzIlZBgUMC48BBMQo8H906g0gqmCFB0mxORkfwMGE4IA+r3HUfd5/BEUaAMBLAkjcqDA0ZdMO4ogYJeRAh8uKUEINsDrVJCenTYLyPNpaUye/7DRWixCa1jCCTaAaRrrsEih9clbI2snruz8E4USfeokYdbkC2wY2szwIEvm5E5PVmCyOMZ5TGWMuQLP6PgOmc5Zv1iXIEMAETI1WScEuQIyUR1ArgCjSEs+GD9Vwn3B2JMC1izxFPXvE3fLFEavHRqWhSr8MH3ja00Oa1Idl59+vhanUhzx5Min1vgy2067LV8R+dDpVA6AAEZZGJL3oKoPYTFpaYy6VjX91tXecG5tTGikCgl5j4uzVTgpcoRcPQ6dw7XwDVPiL25qyVcXnOZC15uN7x97l7c0Dpf9mpDVy93srjuugUg914WQYfd3TQw7HNSErDDOevWPMTq6qCEpOtZhFqPzF9mLa0cJus+X2UfFo36yHJ5fnmOMJqyOkinpopqpzQRSxj6ejYfdwWw0fJgNnh8rOG2YFodAbfiXjuC08ewSMz6CfD+JxUOtBvxvVil5REI9CuWNPD9ov/v9EU0Ukzsh+Lr0fiWkKyFdCelKSG9KSP/pLfzKRFcmujLRlYnekImK+93hXe5DIfc0DqWcRp/2LkHYhpdWxKHU+oIhkJcQQAtT3TpEto6QIiC9qkGZi0X8YU590Gp5Yo4Ls2ZEq5sFJT7FtbdxJpHdhDYBUX2ENYd75af3DZM0pjMX8X3DKvdt6LQ7vzbb75ud38e374J3t0Hnt5v2+9t/oHLH/r5RMUkvtpiKbd37ZZyN0S78RvfpHhRImmVJV7fF6FrPCRaMv5XBLir1QXd2w1i5XOeqLORm24UJYKpBHYtxCg4cTxUspXfBBDabOXp6dnGey/KXjNwagslUiMjpchQn01y9ynTSukh72Yh2Y3gU5k4HgB+GW03vx4YohefC/0zrQ1FQCFDMIFeb7e6H0mFzLA72Fidv9ntENwwp5Ypt9dBpZYaf/hyNQcF8qyMmNhJrh19FkMSvEocCW6RWEGCxtoEYzSIr/nKhPFk+/wI81lJ3
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create WemaNIPService GetStatement"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/WemaNIPService/GetStatement"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create WemaNIPService GetStatement

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementRequest"}},"text/json":{"schema":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"accountNumber":{"type":"string","nullable":true},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"accountname":{"type":"string","nullable":true},"rcrE_TIME":{"type":"string","format":"date-time"},"lchG_TIME":{"type":"string","format":"date-time"},"vfD_DATE":{"type":"string","format":"date-time"},"pstD_DATE":{"type":"string","format":"date-time"},"entrY_DATE":{"type":"string","format":"date-time"},"traN_DATE":{"type":"string","format":"date-time"},"valuE_DATE":{"type":"string","format":"date-time"},"tranid":{"type":"string","nullable":true},"particulars":{"type":"string","nullable":true},"tranremarks":{"type":"string","nullable":true},"dr":{"nullable":true},"cr":{"nullable":true},"balance":{"type":"number","format":"double"},"parT_TRAN_SRL_NUM":{"type":"integer","format":"int32"},"instrmnT_NUM":{"type":"string","nullable":true},"gL_DATE":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementResponse"},"nullable":true}},"additionalProperties":false,"title":"WemaGetAccountStatementResponseListResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"accountname":{"type":"string","nullable":true},"rcrE_TIME":{"type":"string","format":"date-time"},"lchG_TIME":{"type":"string","format":"date-time"},"vfD_DATE":{"type":"string","format":"date-time"},"pstD_DATE":{"type":"string","format":"date-time"},"entrY_DATE":{"type":"string","format":"date-time"},"traN_DATE":{"type":"string","format":"date-time"},"valuE_DATE":{"type":"string","format":"date-time"},"tranid":{"type":"string","nullable":true},"particulars":{"type":"string","nullable":true},"tranremarks":{"type":"string","nullable":true},"dr":{"nullable":true},"cr":{"nullable":true},"balance":{"type":"number","format":"double"},"parT_TRAN_SRL_NUM":{"type":"integer","format":"int32"},"instrmnT_NUM":{"type":"string","nullable":true},"gL_DATE":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementResponse"},"nullable":true}},"additionalProperties":false,"title":"WemaGetAccountStatementResponseListResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"accountname":{"type":"string","nullable":true},"rcrE_TIME":{"type":"string","format":"date-time"},"lchG_TIME":{"type":"string","format":"date-time"},"vfD_DATE":{"type":"string","format":"date-time"},"pstD_DATE":{"type":"string","format":"date-time"},"entrY_DATE":{"type":"string","format":"date-time"},"traN_DATE":{"type":"string","format":"date-time"},"valuE_DATE":{"type":"string","format":"date-time"},"tranid":{"type":"string","nullable":true},"particulars":{"type":"string","nullable":true},"tranremarks":{"type":"string","nullable":true},"dr":{"nullable":true},"cr":{"nullable":true},"balance":{"type":"number","format":"double"},"parT_TRAN_SRL_NUM":{"type":"integer","format":"int32"},"instrmnT_NUM":{"type":"string","nullable":true},"gL_DATE":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"WemaGetAccountStatementResponse"},"nullable":true}},"additionalProperties":false,"title":"WemaGetAccountStatementResponseListResponseHandler"}}}}}}
>
  
</StatusCodes>


      