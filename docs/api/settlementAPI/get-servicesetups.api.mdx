---
id: get-servicesetups
title: "Get service setups id"
description: "Get service setups id"
sidebar_label: "Get service setups id"
hide_title: true
hide_table_of_contents: true
api: eJztVj1v2zAQ/SvGTS3ARGlHbR6CJEOLIE4mw8NZPNtMKZIhT0YNgf+9OElWnDYtHLijJxnH47uvR99rwQeKyMa7Ow0lrIlnFLemokTchAQKGNcJyjkM9pnYYaEgYMSamKKctuCwJijBaFBgHJQQkDegINJLYyJpKDk2pCBVG6oRyhZ4F+RG4mjcGhSsfKyRoYSmMRpyViPozfW32fTp8XYPvSHUFD8AnvNCnFPwLlGS869XV/LRlKpogtQPJcyaqqIkRVfeMTnukOgnF8GiRG7/jOCXz1QxKAhRWsmmx08D1Kvj0ntL6CArqCklXNN7TXCNtbi01FeUFWjkI8IZfURHFWiTgsXd966tRwTfom2O80y2WR/lWEVCJj3lfyaskemCTU1du7w2K/ORO1kBam1krGjvDzq1QptIARu2HbFGune0fhgYcjLA/nuLTluKPV4I1lTdUyuekz+z6cymE9jU/SudaXSm0Sk0ygL5dgXeEE9Sf3nSS4BJt9Jr4o0fFIIMWpZ7CQUGUwzuF7170RqdQYFY99qgiVa2NnNIZVEkYrZUk+MLTdvLNdUp4C5520gOl5WvQda1cSvftXasqvObzMbrk+n9HSiQMH3y2y8ymeAT19g9jL1++EtRb0ofh3iw8LPqc2+HgueAwQzFvZYMCkqjRRNtfGLxatslJnqKNmcxvzQUd1DOF0LeaHrqzVthvvzW4wR/S2hUIfDpYVA6nyciyd5LdDCi28H4RgAU/KBdr8vyIqu9dPrv0fsoB0JtzEB0XH86rSoKfHB2CLE44NjN9SPk/AuYgq/I
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get service setups id"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/service-setups/{id}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get service setups id

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"id","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceSetupResponse"}},"additionalProperties":false,"title":"GetServiceSetupResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceSetupResponse"}},"additionalProperties":false,"title":"GetServiceSetupResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceSetupResponse"}},"additionalProperties":false,"title":"GetServiceSetupResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      