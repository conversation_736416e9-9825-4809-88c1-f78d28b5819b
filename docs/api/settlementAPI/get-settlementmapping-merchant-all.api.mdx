---
id: get-settlementmapping-merchant-all
title: "Get SettlementMapping merchant merchantId all"
description: "Get SettlementMapping merchant merchantId all"
sidebar_label: "Get SettlementMapping merchant merchantId all"
hide_title: true
hide_table_of_contents: true
api: eJytVMtu2zAQ/BVhTy3ARml6082HwPXBRVAnp8CHtbS2iIoUQ66MugL/vVi9/ETQAjkJEmdnd2eGaqF25JF1bRcFZLAjXhFzRYYsG3RO292SfF6i5VlVgQLGXYDsFY6wZQ+DtQKHHg0xeYG0YNEQZGAGgkUBCrSFDBxyCQo8vTXaUwEZ+4YUhLwkg5C1wAcnlYG9MCvY1t4gQwZNowuIUU3kT7ijH43ZkB/J3xryB7jBpi3TrsNNdNryt4crvpX+Qx/DNn9crmYvz99HtpKw6Gr+dfUY1wIOrraBgpw/3N/Lo6CQe+3EOchg1eQ5hSC9L07mxMmVV8loSXL0JsHOX0Nc1kMUQPVOZZCi0+kVTTpWp+2RJ6Y9USC/H4PQ+EqWZ3YhS9Mw8XwpaH+3IxMcHkJdNTLzXV4bkK213dadIJqrTssed7JNMntagAJp0y+7/wpRgasDG7RSO9rwnyKcSThZwvSbU1ehttKl26kdBHoFdBrUjVuhpviDguzsKkivtYKyDiwMbbvBQC++ilE+98ET9QodcFNJVrZYBVLwiw6Xyd9j1ciMIOF7p2CI9hG+lhevBX+72YUWeW2ZrKT9088hw58T+S/c0mj4iPZw2nOc50SMuI5qvBwfPkXf7eQqnm0/BX7++Awx/gUr6LnY
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementMapping merchant merchantId all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementMapping/merchant/{merchantId}/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementMapping merchant merchantId all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      