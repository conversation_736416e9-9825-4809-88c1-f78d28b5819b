---
id: get-settlements
title: "Get settlements settlementId"
description: "Get settlements settlementId"
sidebar_label: "Get settlements settlementId"
hide_title: true
hide_table_of_contents: true
api: eJytkstu2zAQRX9FuKsWYKO0S+6yCFwvChR1sjK0YKSxRVQiGXJkVBD478X4IStuUSBAV6KG8zqXd4IPFA1b79YNNPbEG2LuqCfHCQps9gl6i2sUlUIw0fTEFOVugjM9QSPNOesGCtZBIxhuoRDpdbCRGmiOAymkuqXeQE/gMRxrOVq3h8LOx94wNIbBNshZze1Xj982D89PXy+tWzINxXc0z7mS5BS8S5Tk/sv9vXwaSnW0QVSAxmaoa0pJZt/crIiLK2QqboB74tafVYQ6oWuUJthyUVVOy7IMhUTxcJFyiJ2gMYeky0XZp4YOd3vqUzBj8t0gG93VvocwWbfzR1zL3VGpU15xfbTi4fsaCjLmhHL4jKwQfOLeOKm9iPxvxDd6zPoy/eIydMY6aXpEmM74W5hgj4xLV+k3bSuF1ieW5Gl6MYmeY5ezhF8HiiP0tlI4mGjNi+BtJzQ2ybmB3pku0R971d6xWFXjw4+zOT4W4ua/7XsOGjeKQqYb5A8KP2m8NXWusrr47r/vcZq3cPm8i7zxbK7V4xNy/g1F1kay
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settlements settlementId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settlements/{settlementId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settlements settlementId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      