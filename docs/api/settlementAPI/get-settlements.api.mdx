---
id: get-settlements
title: "Get settlements settlementId"
description: "Get settlements settlementId"
sidebar_label: "Get settlements settlementId"
hide_title: true
hide_table_of_contents: true
api: eJytUstu2zAQ/BVjTi1ARGmPvOUQuD4UCOLkZPiwkdYSUYliyKVRQ+C/F+t33KJAgZ5E7WM4M5wJY+BI4ka/aGDRsixZpOeBvSQYCLUJdoVLFWuDQJEGFo7am+BpYFik88yigYHzsAgkHQwiv2cXuYGVmNkg1R0PBDtBdmG/K9H5FgabMQ4ksMjZNSjFnOHnj9+XD68v307QHVPD8R/AS1nrcAqjT5y0//X+Xj8Npzq6oC7AYpnrmlPSu286c5bZRWSa3QgeWLrx6CLMQbpFRcFVV1vVdL1WYJA4bk9W5tirNJGQbFW1PKQc2kgN3+m57sfc3FEIUCnOb8a9Sif93iAeUqDd7PJWs4enBQwU/aBg+wXFIIxJBvK6e/L278o+2HC2VfinVKEn5xV0z3w6ql6BgttLuw6T/QC7NujGJDo8TW+U+DX2pWj5PXPcwa7WBluKjt5U3mpC45KeG9gN9Yl/41WPXjShFp+ej5n4PNMQ/4nvsUh+pw5Rn/UPBj94d5vlsi7mFLf/zuNw31W4z1z0jc+Zmj++oJRfWKtC5w==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settlements settlementId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settlements/{settlementId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settlements settlementId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      