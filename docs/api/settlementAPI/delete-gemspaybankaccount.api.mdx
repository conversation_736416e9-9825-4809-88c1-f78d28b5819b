---
id: delete-gemspaybankaccount
title: "Delete gemspay bank account id"
description: "Delete gemspay bank account id"
sidebar_label: "Delete gemspay bank account id"
hide_title: true
hide_table_of_contents: true
api: eJztVU1v2zAM/SsBTxug1t2OvmVo0BbYgKIfpyAHxmITtbKkSnSwwNB/HxgrqdsVWAcUPfUkWyQfySfisQcfKCIb7y401KDJEtMZtSngdonuAZvGd45BAeMqQT2HYvyB7mFajAsFASO2xBTFpweHLUENRoMC46CGgLwGBZEeOxNJQ82xIwWpWVOLUPfA2yARiaNxK1Bw52OLDDV0ndGQszqAns1+XU9vb8730GtCTfE/wHNeiHMK3iVKYv9+ciKHptREE4QNqOG6axpKCRQ03jE53iHRb66CRcnc/53BL++pEbZCFGLZDPipQD05Lr23hA6ygpZSwhW9RoLrrMWlpaGjrEAjvyGd0W9iFFBrI82ivRzF36FNpIANS2I4HY/E6NWvCoHvh7Q/z9FpS3EADsGaZjeg1X3yn6x/COu7Kf+k+2PozoL9XHoGkMlqQJmIEE+KEk92mtoSr/2TYAs5IrA1VBhMVeKOJO6oxFW90RkUJIqbvUp30Yp+ModUV1UiZkstOT7StDkuIMnbTqo6bnwLIpzG3fkd86Xh0urk+hA+mV5egAJJM7Sz+SYjEHziFncjVZT8n20+Y+Xw2CMNzmpooi/9zwGDAQWvMQAKaqNlWa19YvHt+yUmuo02Z7l+7ChuoZ4vFGwwmmEm5z1ok+RbH976RVmH9QBfrsoK+jqRjflaueUS3VYoQtvJHyh4oO2wMPMiq/1Oe/fsQ5bRBj1UIAt2sE6bhgKPbGOIxWj2Tmc/ZzczyPkPDzLksQ==
sidebar_class_name: "delete api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete gemspay bank account id"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/gemspay-bank-account/{id}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete gemspay bank account id

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"id","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteGemspayBankAccountResponse"}},"additionalProperties":false,"title":"DeleteGemspayBankAccountResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteGemspayBankAccountResponse"}},"additionalProperties":false,"title":"DeleteGemspayBankAccountResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"DeleteGemspayBankAccountResponse"}},"additionalProperties":false,"title":"DeleteGemspayBankAccountResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      