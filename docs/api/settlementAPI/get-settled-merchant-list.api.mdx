---
id: get-settled-merchant-list
title: "Get settled merchant merchantId list"
description: "Get settled merchant merchantId list"
sidebar_label: "Get settled merchant merchantId list"
hide_title: true
hide_table_of_contents: true
api: eJytU01v2zAM/SsGTxsg1F13862HIguwDsXSnoocGJuxhVmyKlHBMkP/faA/8oWg6KGnOCL5yPf42EPnyCPrzi4rKKAmXhFzS9Uj+bJByz91YFDAWAcoXmGKwlqBQ4+GmLwEerBoCAp4wpp+RbMhDwq0hQLeIvk9KAhlQwah6IH3TlK1ZaqHvG3nDfL49P0OUlJneCv9jz4HzUysltWM55AbUODpLWpPFRTsI12BD+y1rU/RY9TVGfji4XF1//L8Y4ZuCKthoI+Cp7SW5OA6GyhI/O72Vn4qCqXXTtYEBaxiWVII0vsisiDOwrihbKaaHTln7bhMQ9x007pBjRIUkKPT+VSdz0V5fyxP+VQfyO/mtUffClVmF4o8r8mE6GqPFd3Id9l2sbpB50CoabvtBtaa20EwMsHhPhtNZchydv+0BAWCPjLafYOkwHWBDVqpnbX+GNMzeQ5yM/3l3LWorYAPDPpJhVdApweKo8/VwTKgoDizz9BiraDpAkth328w0ItvU5Ln0agiUaUDbgSs2GIbSMEf2l9eyg7bKKOB+OmdgukUjulr+eO15F9vdiFB2VkmK/798nuy5ddMDvyaNNMj2v1pz3meEzXSOqnZ758+xdjt5LrO2B/MvHh4hpT+A0/WoFQ=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settled merchant merchantId list"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settled/merchant/{merchantId}/list"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settled merchant merchantId list

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      