---
id: get-settled-merchant-list
title: "Get settled merchant merchantId list"
description: "Get settled merchant merchantId list"
sidebar_label: "Get settled merchant merchantId list"
hide_title: true
hide_table_of_contents: true
api: eJytU8tu2zAQ/BVhTy3ARml64y2HwDXQFEGVnAIfaGktERUfIVdGXYH/XqwetmUYRQ45CSJ3Zndmhz04j0GRdnZdgYQaqUCiFqtHDGWjLP3QkUAAqTqCfIXpFjYCvArKIGHgix6sMggSnlSNPzuzxQACtAUJbx2GAwiIZYNGgeyBDp5LtSWsh7qdC0bRePTtDlISC75C/8WPYTOTqnU183lFDQgI+NbpgBVICh1eoY8UtK3P2btOVwvy1cNjcf/y/H2mblBVw0DvJU9pw8XROxsx8v3d7S1/Koxl0J7XBBKKriwxRu59cbNCyuK4oWyWmp00Z+24TIPUuGndIEYLJOTK63xC5zMo70/wlE/4iGE/r70LLUsl8lHmE9ygpS8V7m9qNNGrQ3RtxxPelM4Aa9R25wb5mtrBubEuK47w7P5pDQK4zSht/xWSAO8iGWUZO5v+PskLn46+E/6h3LdKWyYfpPSTHa+gvB60joEXx+yAALnI0dBiI6BxkRjY91sV8SW0KfHxmFj2qtJRbZlM7lQbUcBvPFw+mb1qOx4NOFj/AUxv4lS+4Z+guf56swsLSmcJLQf5068pn58zfunXrJkOlT2c95znOXMjbZKYg//hU4zdzp7ZQv0x1auHZ0jpH+65pB8=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settled merchant merchantId list"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settled/merchant/{merchantId}/list"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settled merchant merchantId list

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      