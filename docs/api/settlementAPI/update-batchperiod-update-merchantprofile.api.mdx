---
id: update-batchperiod-update-merchantprofile
title: "Update BatchPeriod Update MerchantProfile settlementProfileId"
description: "Update BatchPeriod Update MerchantProfile settlementProfileId"
sidebar_label: "Update BatchPeriod Update MerchantProfile settlementProfileId"
hide_title: true
hide_table_of_contents: true
api: eJztWV2T2jYU/SvMfWpaEZLdLEn9xu7sZPchLbMf0weGh4t1DUptyZFkEobxf+9c2YABb0qatNNkvC+w8tW5nzqCwxpMTha9MvpWQgRFLtHTJfp4kZNVRj6GhXdk4wVqn1uTqJRAgMe5g2gCwXQcTGEqIEeLGXmy/HANGjOCCBx5n1JG2o8rgFsJApSGCHL0CxBg6UOhLEmIvC1IgIsXlCFEa/CrPEB4q/QcBCTGZug51EJJKEux9fL2+t396PHhZgO9IJRkvwC8LKeVMTl/aeSKLWKjPWnPbzHPUxWHWg3eO6N57QgKrcUVR+Apc411M3tPsQcBueWKe0Xh6WxXPq7/CekCSqk4BkzHDagEU0cCvPIp799vW6NJd1V6UDKUp0/+B0ml2Zyff/kRcmIwSy432lXBnL14wS+SXGxVzj4ggvsijsk5EM1JDX3NU1TtRXgiWVdD7QxnxqSEGkoBGTmHc2qrgS7SFGecFx+vUoBEf4q7FlL4+wpzJHXVCqc0OfdbOP0nhLXZ+LueGbRS6Xm7x6ONWHhzv422vT6NSXH/3bzt+b0y8sT+UIJF+kQiiTXZgzqxpN6cbFpdJzemsA1zpT3NA0dvE1Pan59xILEl9CRH/rN14HPU9xwEd9hIlah/tudyddoQbc2vM1TpSXuOJv0rJuT/OP2nst5b8veHpbirGY5bceD6C2D3GPRbAB7F+Yfyi1Yv/wLo5vUGtUzJwuH99uTt1hF7R+wdsXfE3hH790Psn/8O1jF6x+gdo3eM3jH698LoQbfZ12gqtafX2N6rlzYKUO2k1y6UZuQXRlZiacxqaRBNIxhgrgYN1EGFOjhAHaxbYEvgYbfLjVJb2JQlU+9zFw0Guw19Scvnc8pcjitn0oJTeh6bDFgr5WvrbqeXXn/CLOcKTo7oEc4TfHORDF/1L16/fN1/dTE868/Ok7h/Fv86PE+GQ0xwGDCVTkyY3G0zgu/eriO90fgWBHDoVX2XL3mCcuN8huEarQXhr637Xhe3Z6khrpWiqtu67sgEMFcg9kRxUYcBAg68goCozfFUwMI4z3Dr9QwdPdq0LHn5Q0F2BdFkKmCJVlXnZbIGqRy/l9tJPoh8Kw3CT3e1Fv6sxyJ+W0YbstNMdUtMC/4PBPxJqyek/HJaio3a/s3Dqdw2tP1tSCz9V0+vKsD+AwPsLI6+M+92jOKYct+wbTqdNo7cePRwxV5n9e8BWbi5weJH/mEBP1a1MSG3wPphbQ0p6nkRPoVB5Zr//gJvJ/97
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update BatchPeriod Update MerchantProfile settlementProfileId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/BatchPeriod/Update/MerchantProfile/{settlementProfileId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update BatchPeriod Update MerchantProfile settlementProfileId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementProfileId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"UpdateMerchantBatchPeriodRequest"}}},"text/json":{"schema":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"UpdateMerchantBatchPeriodRequest"}}},"application/*+json":{"schema":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"UpdateMerchantBatchPeriodRequest"}}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"batchPeriods":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponse"}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"batchPeriods":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponse"}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"batchPeriods":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponse"}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      