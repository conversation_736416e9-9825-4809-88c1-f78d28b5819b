---
id: get-settlementaccount
title: "Get SettlementAccount settlementAccountId"
description: "Get SettlementAccount settlementAccountId"
sidebar_label: "Get SettlementAccount settlementAccountId"
hide_title: true
hide_table_of_contents: true
api: eJytU7tuGzEQ/BVhqgQgfE5KdioMRUWAILIrQcX6biURuSNpcilEOPDfg9UrjqUUBlwdj7uc2RnMjgiRE4kLft7BYsOyYJGeB/ZCbRuKFxgIbTLsEn9r01NtZRAp0cDCSVtGeBoYFvlt67yDgfOwiCRbGCR+KS5xByupsEFutzwQ7AjZxwOEJOc3MFiHNJDAohTXoVZzYZk9fF9Mnx6/naG3TB2nd4DXutLmHIPPnLX+9f5ePx3nNrmozsBiUdqWc1buN5UZy+TKlslt9QPLNpxshjn6YNFQdM0VRDPewKgwyJx2Z69L6lW0SMy2aTY85BI3iTq+03Pbh9LdUYxQkc6vw0G/k/5gHQ850v7V8JPpjzkMFP2obfcF1SCGLAN5fXt2/R2a/3Hr4r7wb2liT84rw0HGePJjCYoO5kbWDOwtjpXBNmTRl+P4TJmfUl+rXr8UTnvY5cpgR8nRswpfjuhc1nMHu6Y+89WQbfDCXgP36ecpR58nuga3hj9dkt+rd9QX/YPBL97/Zw3qqppzUj98nCPtq724jKQhuCRw9vCIWv8AKzFh5A==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementAccount settlementAccountId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementAccount/{settlementAccountId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementAccount settlementAccountId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      