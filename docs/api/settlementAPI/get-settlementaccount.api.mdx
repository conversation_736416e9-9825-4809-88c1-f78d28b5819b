---
id: get-settlementaccount
title: "Get SettlementAccount settlementAccountId"
description: "Get SettlementAccount settlementAccountId"
sidebar_label: "Get SettlementAccount settlementAccountId"
hide_title: true
hide_table_of_contents: true
api: eJytU8tu2zAQ/BVjTg3ARmmPvPkQuD4UCOrkZPiwkdY2UYlkyJVRQeC/F+tX0tg5BOhJFPcxO8PZESFyInHBzxtYbFgWLNJyx16orkPvBQZCmwy7xGtseoytDCIl6lg4acoITx3DIr9PnTcwcB4WkWQLg8QvvUvcwErq2SDXW+4IdoQMcd9CkvMbGKxD6khg0feuQSnmjDK7/7mYPj3+OLXeMjWcPtG8lJUm5xh85qzx73d3+mk418lFVQYWi76uOWfFfheZsUwuZJlcZ9+xbMNRZpiDDhYVRVddtKjGKz0KDDKn3UnrPrVKWiRmW1WvBV8b3t1uuMuRhhzaXme9rUMHZev8OuyFcNLuNTzkvWExmT7MYaAwB5K7bygGMWTpyGvtSf5PkP9HtvMzCP+RKrbkvCLs+YxHYZag6GCumM7AXsNYGWxDFq0cx2fK/JTaUvT6pec0wC5XBjtKjp6V+HJE47KeG9g1tZkvhqyDF/bqvC+/joa6meg+XBv+eEl+UO2o7fUPBr95+GAfyqqYk2X/+zgH2DcLch5JTXC24uz+EaX8BTjIZa8=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementAccount settlementAccountId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementAccount/{settlementAccountId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementAccount settlementAccountId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      