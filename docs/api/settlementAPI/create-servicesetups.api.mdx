---
id: create-servicesetups
title: "Create service setups"
description: "Create service setups"
sidebar_label: "Create service setups"
hide_title: true
hide_table_of_contents: true
api: eJztVz1v2zAQ/SvGTf2g47SjNjcIkgxtgziZDA9n8WwzpUSGPCU1BP334iRZluO0cFp0CexFxvF4H4+P5GMJzlNANi6/0pBAGgiZJhQeTUqRuPARFDAuIyRTaO0TscNMgceAGTEFGS0hx4wggYvzr5Px3e0lKDA5JLAi1BRAQaCHwgTSkHAoSEFMV5QhJCXw2svMyMHkS6iqWeNMkb84vRaP1OVMOctf9N6atK55dB9dLra9UG5+TymDAh+kQzYUZVSb6C2uv9WVPs+rIC+sxbmlpsJKwSPa4hDPSgFqbaQmtNe9lAu0kRSwYfGFsdZ3Xm8hrqG8aVoFicL0k99eV/0l+/DxjbUncQJF7/LY1Pv59LQum2IajJfwkMCkSFOKspt6VK5X21s0r8IjtqG2jnPnLGEOlYKMYsTlYUBp5APSGf1SsIULGTIkUBRGS+L/tUoKoi2WBzk2p5ce8x8LlpUcssmohstpszCvmXMoay6Id/nSMOSfA2y+l5hrS2Fvf712dx3ZdGTTLpv+6g460uhIoz6N6mtx9wo8q5EYxGb+oJOXGfHKifr0LtYrjbyCBEbozah1HnbOYtgIziJY0ZfMPiajUSRmSxnlPNT0eLKkLHpcR2cLyX+SugxEWAqxb7bi8vwnZt7Sns7Y4t1yolOnImsXrl6ZDpQ61WDSVTAYX1/JVAqx6f3xk0yUBjOs91Urln+HyQ5yHQd6eqFSTftlC9cU0JsWnx5gMwUrQTWZQlnOMdJdsFUl5oeCwhqS6Uw6DKbh7HRWqY1gF4S1iTKgOx48q6vTMvDuplX37wfyYnip3h+03n0ebJCFSpXt6FkTcHgrAbYeexfcdsY4Tclzz7efdNZj1/X3yS0omLcvisxp8Q74JE8TfJI6FLi6tfqIqW0lWMyXRX14QZNZfr8AO7ekbw==
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create service setups"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/service-setups"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create service setups

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddUpdateServiceSetupRequest"}},"text/json":{"schema":{"type":"object","properties":{"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddUpdateServiceSetupRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddUpdateServiceSetupRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceSetupResponse"}},"additionalProperties":false,"title":"GetServiceSetupResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceSetupResponse"}},"additionalProperties":false,"title":"GetServiceSetupResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceSetupResponse"}},"additionalProperties":false,"title":"GetServiceSetupResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      