---
id: create-servicesetups
title: "Create service setups"
description: "Create service setups"
sidebar_label: "Create service setups"
hide_title: true
hide_table_of_contents: true
api: eJztV01PGzEQ/SvRnPphCO1xbylCwKEtInCKcpish8TUuzb2LDRa7X+vxrtJNoRWoVUvKLlkZY/n482z/VyD8xSQjSsvNWSQB0KmMYVHk1MkrnwEBYzzCNkEuvGxjMNUgceABTEFma2hxIIgg/Ozr+PR7c0FKDAlZLAg1BRAQaCHygTSkHGoSEHMF1QgZDXw0svKyMGUc2iaaWtMkb84vRSL3JVMJcsnem9NnnIe3kdXytiOKze7p5xBgQ9SIRuKMqtN9BaX31Kmz+MqKCtrcWapzbBR8Ii22seyUYBaG8kJ7VUv5B3aSArYsNjCSOtbrzcQJyiv21JBvDD95LdXVb9lHz6+sfLET6DoXRnbfD+fnKS0KebBeHEPGYyrPKcou6lH5dRtb9G8Co/YudoYzpyzhCU0CgqKEef7AaWR9whn9EvO7lwokCGDqjJaAv+vLimItprvZdieXnrEf0xYOnnEpqAEl9Pmzrxmzb6sOSfe5kvLkH92sPq/wFJbCjv767W768CmA5u22fRXd9CBRgca9WmUrsXtK/A0ITGI7frBWl4WxAsn6tO7mDqNvIAMhujNsDM+WhvLwEpwVsGKvmT2MRsO51TEys8DajqW79y6Sh+j9yB6Uvh8vdGUZz+x8JZ25MUG5o4Ka1EqavbOpYassSiix+VgTMyWCip5MLq6lKUUYlvy4ydZKHUVmLZTp5F/B8UWYOvW92RCo9qq6w6lCaA3HSw9nKYKFgJmNoG6nmGk22CbRoYfKgpLyCZTqTCYlqqTaaNWOl2A1SbKhF63/1leawkD7647Uf9+IA+Fl/L9QcvtV8EKWWhU3c2etg6PbsTBxmLnXtusGOU5ee7Z9oNOe6S6+j6+AQWz7iFROC3WAZ/kRYJPkocCl0pLJ0saq8FiOa/SmQVtZPn9AsgRoKQ=
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create service setups"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/service-setups"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create service setups

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddUpdateServiceSetupRequest"}},"text/json":{"schema":{"type":"object","properties":{"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddUpdateServiceSetupRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddUpdateServiceSetupRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceSetupResponse"}},"additionalProperties":false,"title":"GetServiceSetupResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceSetupResponse"}},"additionalProperties":false,"title":"GetServiceSetupResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceSetupResponse"}},"additionalProperties":false,"title":"GetServiceSetupResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      