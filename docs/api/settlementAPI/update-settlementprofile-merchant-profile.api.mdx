---
id: update-settlementprofile-merchant-profile
title: "Update SettlementProfile merchant merchantId profile profileId"
description: "Update SettlementProfile merchant merchantId profile profileId"
sidebar_label: "Update SettlementProfile merchant merchantId profile profileId"
hide_title: true
hide_table_of_contents: true
api: eJztWEuT2zYM/isenPqgo2Q367S6OTuZZA9pPfs4eXyARchmIpEMSW3i0ei/dyjq5Udar5t2MhP7YpsEP4LABxJACUqTQSeUvOEQQ6E5Oroj5zLKSTptVCoyek8mWaN0s/AXGDhcWYjn0Iu2cwsGGg3m5Mh4kRIk5gQx5A3IDQcGQkIMGt0aGBj6VAhDHGJnCmJgkzXlCHEJbqP9SuuMkCtgkCqTo/N6FoJDVbEOvFH0P8F+++b93fTh/l0LvSbkZJ4AXlWLIEzWvVZ84yUSJR1J53+i1plIaidEH6ySfmwPSi0/UOKA+ZNqMk6Q9bOtUV8XVkiy9o9a5f3TySLLcJlRULVigIVTvfMGS5ZKZYQSKgbCTnku5G3Q/LCM7TCmSaKKLaiv6LxE+fFa8eP09MJHH8oLN2qctEYdtULYmRE5ms0/WWTWkfIYvgFyLjwJMJsN7JViZomBE86rAFPO73ZN3jroKSAPdaS3gb0XxkNIR1/cmZhnYn53xBxenL/8embomaHfF0M9qCGrlbTBfRfPn/svTjYxQvu9IIa7IknIWmDDnKC+c3WG4kmUtg3UQePmZC2ujvMsR3fMdie5i50eee3CP+VSoeFCrg7veELIHuv/t+T+xvPB198Wrf1+h5JnZPZuvqfee2eS/CAkOSlvO7Pjh2BH/TZtv0PhWRvtgYxak436Cn7U1NujYd2dk1srHmrvxBffdQ0eQ4RaRHuwUYsWlT1uFTWAUdkhV+ATA/PYthMKk/ki3Dlt4yjqCTTm9PhsRbnVuLEqK/yxniUqB199+zC47SvwN18w1xl9PQ3sibDr91Dq7+Z3TQPgUEbX52w9aJ+abY9tZWCHp9RwYpBP7WowiCe4TPG3q3Tycnz16sWr8curycV4eZkm44vk98llOplgipOacUKmIZHryFWbc0CK0XR2Awy8NwJtHl/4eNTKuhzrm6bpmvxrOm2xs4u2QWJUscCFsiHaHFALYAd6Un3YA4N4qxGlO5G433zBYK28W+dQlku09GCyqvLDnwryxp4vGDyiEeGGmJfAhfW/eReeO9p3qR38dNt0jX4e+T7aoVM1gyg33tKYFf4fMPhIm+0+mm9S/Y879waqFhVr+2Df/Phhs0HXrVPEnzfMXgfA8b0H6CX28qJ+xTRJSLuB7HDTxeD2mk3vr/2uy6ZTl4fYNfjZt/zwc7CIqs9Wv2H1WAkZylVRv44QtvafvwBe2Jhw
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update SettlementProfile merchant merchantId profile profileId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/SettlementProfile/merchant/{merchantId}/profile/{profileId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update SettlementProfile merchant merchantId profile profileId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"profileId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"isAdminRequest":{"type":"boolean"},"settlementAccount":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"settlementProfileId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementAccountRequest"}},"additionalProperties":false,"title":"AddUpdateMerchantSettlementProfileRequest"}},"text/json":{"schema":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"isAdminRequest":{"type":"boolean"},"settlementAccount":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"settlementProfileId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementAccountRequest"}},"additionalProperties":false,"title":"AddUpdateMerchantSettlementProfileRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"isAdminRequest":{"type":"boolean"},"settlementAccount":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"settlementProfileId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementAccountRequest"}},"additionalProperties":false,"title":"AddUpdateMerchantSettlementProfileRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      