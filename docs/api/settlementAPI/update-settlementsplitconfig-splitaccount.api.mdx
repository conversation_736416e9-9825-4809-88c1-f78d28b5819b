---
id: update-settlementsplitconfig-splitaccount
title: "Update settlement split config settlementSplitConfigId SplitAccount splitAccountId"
description: "Update settlement split config settlementSplitConfigId SplitAccount splitAccountId"
sidebar_label: "Update settlement split config settlementSplitConfigId SplitAccount splitAccountId"
hide_title: true
hide_table_of_contents: true
api: eJzVVk1v2zAM/SsGT/tQ625H37Ki6HoYUDTtLkUOjMUk6mRJleS0geH/PlBKYydtBwxYBzSXGDLz+MinR6YD68hjVNZcSKigdRIjTSlGTQ2ZGJxWsbZmoZZTfsS6tq2JICDiMkB1C0NsCji1kmAmwKHHhiJ5DurAYENQQTgMZuALCQKUgQocxhUI8HTfKk8SquhbEhDqFTUIVQdx4xJM9MosQcDC+gYj826VhL4XQybGn2S2b5Lg/OzHdHJz/f0JekUoyf8FeN/PcjCF+M3KDUfU1kQykR/ROa3qpEx5F6zhs2dQdn5HNavhPOsYFYUUx8X/RN3SKNa0zTwR3NUkbTvXxFUBSqk4FerLEdICdSABUUXNCDcHl2M66vFVLiSBRXqM743zuN2fPr8r8gznKThrQmbz9eSEvySF2ivHWaCCaVvXFEJKvv8mZykGbxapnCLbvnjFs8WYTPHMbg3FlZXZcjV7LlmvghKdKgfIo/TDo5yq7F7J1ZfjZGW3n60HAYH8+mnWtF6zH2N0oSrHuSStj5fUBIebYHXL5R/XtgE2Iit+NZjx7BEbp+lQ25Oe3b6wSeWtSOcZsRhkKiaXFyCACeUOr79AL8DZEBtM92o7Q96k83vi7i5j8qTTqAxTSS3qtprcAjqVeviiKiCgen1uj7mkyH06MwErGyIn6bo5Brrxuu/5+L4lv4HqdiZgjV4hG4PVkyrws9yZ4aCe3YiED1fbSfux4H30Up3bQzQb1iNrCCDgF23+sI14yv9PGvst62e9eNom/7whOeNod+3YcNH57WkGPLpmgCHi2UJi2+xsfjm5PmW8+XaTNfw/oAKPD7wS8SEXbBPrNKTSWQcazbLFJcdmUP78Bjr5B7M=
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update settlement split config settlementSplitConfigId SplitAccount splitAccountId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/settlement-split-config/{settlementSplitConfigId}/SplitAccount/{splitAccountId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update settlement split config settlementSplitConfigId SplitAccount splitAccountId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementSplitConfigId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"splitAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"}},"additionalProperties":false,"title":"UpdateSettlementSplitAccountRequest"}},"text/json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"}},"additionalProperties":false,"title":"UpdateSettlementSplitAccountRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"}},"additionalProperties":false,"title":"UpdateSettlementSplitAccountRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      