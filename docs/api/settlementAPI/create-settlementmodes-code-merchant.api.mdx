---
id: create-settlementmodes-code-merchant
title: "Create settlement modes settlementMode code settlementModeCode merchant merchantId"
description: "Create settlement modes settlementMode code settlementModeCode merchant merchantId"
sidebar_label: "Create settlement modes settlementMode code settlementModeCode merchant merchantId"
hide_title: true
hide_table_of_contents: true
api: eJztVsFu2zAM/RWDpw3Q6m5H3YpgaHsoVjTtLkEOjMUm6mRLleRggaF/H2g7id1kWDd0KAr0ZEdS3iP5KD82YB15jNpWlwokFJ4w0pRiNFRSFUurKEysoivyxQqrCAIiLgPIGexPXVlFMBfg0GNJkTzvN1BhSSAhjM8J0BVIcBhXIMDTY609KZDR1yQgFCsqEWQDcePaf0evqyWkJH4DOHkZ0LJP8FL9I5iAe+tLjCChrrUagZ9/vZqe3d1ebKFXhIr8X0U658PB2SpQ4P0vp6f8UBQKrx0LCBKmdVFQCCCgsFWkKrZI9DPmziAzN4cMdvFABevqPPdC1B1+6KH2BxfWGsIKkoCSQsAlHStCVRuDC0NdRkmAwvgMuoJVfA6cDt/RaHU8riet9me8JACV0lw+NNeDiO7RBBIQdeSz0HKObsa29W56UeAFsbbPC6yUId9BO2d00d7U/CHYdy3fsJbtjXwX8S2LmBh9/PGdtN6Z7VPOWvfMxjXIuLDZoYVlWwPKRk5UUlxZdmZnQysSm5KEHJ3O9yCfWqa8GcOmnLmerk7anS1J3uzpErBgfr3179ob9qoYXZD5kE3R+mRJZXC4CdbUnP9JYUtgk9LVvW117ot73p3L9sXNzq4vQQDTdIVbf+ZW4QRLbK9E75r/paAjzXbtOPDIJLrEm77WM0CnYdjKXbVBgDwYbYruIY+OKOV+hpKDmOYCViyunEHTLDDQnTcp8fJjTX4DcjYXsEavu2sya0DpwO9q18lPktqZP3y46QeMjxlPbseS7Rex2rAoaGr+BQJ+0OZweOOZ5tXYJ68QwUCnNE9iO7m9uAod22BO3EXCCXe7Z0VBLg72hhDzwbfi+tv0FlL6BfzBLjE=
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create settlement modes settlementMode code settlementModeCode merchant merchantId"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/settlement-modes/{settlementMode}/code/{settlementModeCode}/merchant/{merchantId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create settlement modes settlementMode code settlementModeCode merchant merchantId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementMode","in":"path","required":true,"schema":{"type":"string"}},{"name":"settlementModeCode","in":"path","required":true,"schema":{"type":"string"}},{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"code":{"type":"string","nullable":true},"isValid":{"type":"boolean"},"settlementMode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"ValidateSettlementModeCodeResponse"}},"additionalProperties":false,"title":"ValidateSettlementModeCodeResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"code":{"type":"string","nullable":true},"isValid":{"type":"boolean"},"settlementMode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"ValidateSettlementModeCodeResponse"}},"additionalProperties":false,"title":"ValidateSettlementModeCodeResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"code":{"type":"string","nullable":true},"isValid":{"type":"boolean"},"settlementMode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"ValidateSettlementModeCodeResponse"}},"additionalProperties":false,"title":"ValidateSettlementModeCodeResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      