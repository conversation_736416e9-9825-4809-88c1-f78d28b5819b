---
id: delete-settlementmapping
title: "Delete SettlementMapping settlementMappingId"
description: "Delete SettlementMapping settlementMappingId"
sidebar_label: "Delete SettlementMapping settlementMappingId"
hide_title: true
hide_table_of_contents: true
api: eJytk01v2zAMhv9K8J42QKu7HXUr0GALsALD0p6CHFibiYXZkirRwQJD/31gPrsmOxTYybL48ZKPyBEhciJxwc8aWDTcsfCcRTru2UtPMTq/hoHQOsMucLY9HGxLg0iJehZO6jLCU8+wyG9dZw0MnIdFJGlhkPhlcIkbWEkDG+S65Z5gR8g27lJI2suvQupJYDEMrkEp5qTydfowv3t6/HZM3TI1nN6RvJSlOucYfOas9i+3t/ppONfJRYUDi/lQ15yzar+x3O+gTS7ITK4D6FnacIYNs6dhUVF01UWWarySpsAgc9ociQ+p09ZFYrZVdQ741PDmZs19jrTNoRu04ps69NCenV+FHQ4n3Y7k3u9VI5O7HzMYqMy+1c1nFIMYsvTkNfbwCO9E8Be/03sI/5YqduS8iuxaGg9sFqDoYK5Mn4G9prE0aEMWjRzHZ8r8lLpS9Ppl4LSFXSwNNpQcPWvvixGNy3puYFfUZb4osg5e2OsIfvh5mKyPE12Ma8UfLslvFR91g/7B4Bdv/7EYZVnMcXb/ezl72VebcipJ5+A0kPfT79PHKUr5Aw6oa0I=
sidebar_class_name: "delete api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Delete SettlementMapping settlementMappingId"}
>
</Heading>

<MethodEndpoint
  method={"delete"}
  path={"/api/SettlementMapping/{settlementMappingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Delete SettlementMapping settlementMappingId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementMappingId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      