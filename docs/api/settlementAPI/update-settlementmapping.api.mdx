---
id: update-settlementmapping
title: "Update SettlementMapping settlementMappingId"
description: "Update SettlementMapping settlementMappingId"
sidebar_label: "Update SettlementMapping settlementMappingId"
hide_title: true
hide_table_of_contents: true
api: eJztV0tT20AM/isZnfpYCO3Rt0AZyoEOw+OUyUH2isSw9i6760DG4//ekdexDU7ahPaYXOJY0ietVp8ilaANWfSpzi8lRFAYiZ5uyXtFGeU+Q2PSfA4CPM4dRFPoZFeNbCbAoMWMPFlWKSHHjCAC9171UoKANIcIDPoFCLD0XKSWJETeFiTAJQvKEKIS/MrUEN4G9w/aZug5wiKVUFWi9XJxfnU7ub/7uYZeEEqye4BX1Swok/OnWq5YI9G5p9zzIxqj0qRO0fjR6ZzfDaB0/EiJBwHGckJ9So6lIcThafJCKYwVhdAqAes8d7poLa74TJ4y93dPXa4nSaKL3PN17uB4YPdr15CHlkUWk93T9hTzpw+4ZLMzLXc004VNiE3cxxMc7xNovHt4lQCUMuXyQnXdc/mAypEAn3rWhdv2ED/IY6ocVB/Gauh4pSWpf4C5f9csGtibQCUmKXh69QfWHFhzYM0+rOn/5Xz5eqDPgT4H+uxMH0ay5IzOXUji95MT/pLkEpsadsAnK5KEnKvp9lYSHIwGHkabx9mM/ELLMNImPNPWo20EYzTpeAAyLjegVMBVZpfr8bmwiudY742LxuPO4EjS8nhOmTO4cloVHPBxojPgAZa7xE03xJ6/YmYUdf2gq4WW9tMtDO5UtxD1TwoNHzepdLTbJg3l25P2STTt86HTiQd21ayaVbwNPOiaC03lXITM9W52NLm+BAGc+HD3y29cmUY7n2Hey92eNfGmoFoy1rOQUZjm7KS+5LKplimgSUFsWK4ERJt8zAQstPNsWZYxOrq3qqr49XNBdgXRdCZgiTYNDJuWIFPHz7Il1Lsg240HPt00i9PnEe99m4Jfd7Oce9kSVcG/QMATrbbsffWNNKvZfw8nuO0tgm1IvCcG6VkAPLpjgE5jsN0xl1pOX0/uzhgvbtbCLNSZxRfeL/ElnFrXUde9pn5XgsJ8XuCcdQMof34DnZ13wQ==
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update SettlementMapping settlementMappingId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/SettlementMapping/{settlementMappingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update SettlementMapping settlementMappingId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementMappingId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"mapping":{"type":"array","items":{"type":"object","properties":{"settlementAccountId":{"type":"string","nullable":true},"settlementAccountName":{"type":"string","nullable":true},"settlementAccountNumber":{"type":"string","nullable":true},"settlementBankName":{"type":"string","nullable":true},"settlementBankCode":{"type":"string","nullable":true},"sourceBanks":{"type":"array","items":{"type":"object","properties":{"bankName":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"SourceBankDetails"},"nullable":true}},"additionalProperties":false,"title":"MappingModel"},"nullable":true}},"additionalProperties":false,"title":"UpdateSettlementMappingRequest"}},"text/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"mapping":{"type":"array","items":{"type":"object","properties":{"settlementAccountId":{"type":"string","nullable":true},"settlementAccountName":{"type":"string","nullable":true},"settlementAccountNumber":{"type":"string","nullable":true},"settlementBankName":{"type":"string","nullable":true},"settlementBankCode":{"type":"string","nullable":true},"sourceBanks":{"type":"array","items":{"type":"object","properties":{"bankName":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"SourceBankDetails"},"nullable":true}},"additionalProperties":false,"title":"MappingModel"},"nullable":true}},"additionalProperties":false,"title":"UpdateSettlementMappingRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"mapping":{"type":"array","items":{"type":"object","properties":{"settlementAccountId":{"type":"string","nullable":true},"settlementAccountName":{"type":"string","nullable":true},"settlementAccountNumber":{"type":"string","nullable":true},"settlementBankName":{"type":"string","nullable":true},"settlementBankCode":{"type":"string","nullable":true},"sourceBanks":{"type":"array","items":{"type":"object","properties":{"bankName":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"SourceBankDetails"},"nullable":true}},"additionalProperties":false,"title":"MappingModel"},"nullable":true}},"additionalProperties":false,"title":"UpdateSettlementMappingRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      