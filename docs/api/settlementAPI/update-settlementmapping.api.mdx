---
id: update-settlementmapping
title: "Update SettlementMapping settlementMappingId"
description: "Update SettlementMapping settlementMappingId"
sidebar_label: "Update SettlementMapping settlementMappingId"
hide_title: true
hide_table_of_contents: true
api: eJztV0tv2zAM/isBT3toTbejb203dD10KPo4BTnQFpuotSVVktMGhv/7QMux3SbZkm7H5BJFJD9SFD+FrMBYchiU0RcSEiitxEA3FEJOBelQoLVKz0BAwJmHZAK97LKVTQVYdFhQIMcqFWgsCBLwb1UvJAhQGhKwGOYgwNFTqRxJSIIrSYDP5lQgJBWEpW0ggovu740rMHCEpZJQ16Lzcv7j8ubk7vbnCnpOKMntAV7X06hMPpwauWSNzOhAOvASrc1V1qRo/OCN5r01KJM+UBZAgHWc0KDIszSGuH4aXeY5pjnF0GoBqzz3uugcLvlMgQr/d099rk+yzJQ68HXu4HjN7teuIa9blkVKbk/bU9SP73DJZmdG7mhmSpcRm/j3JzjdJ9B09/BqASil4vLC/Grg8h5zTwKCCqwLN90hvlNAlXuo343V0vHSSMr/AebuzWPRwl5HKjFJIdBLOLDmwJoDa/ZhzfAv59PnA30O9DnQZ2f6MJIjb432MYnfjo/5S5LPnLLsgE9WZhl539DttSQ6GK15GG1uZwsKcyNjS5txT9u0tgmM0arxGsi42oBSA1eZW6za59Ll3MeGYH0yHs+o8KWdOZR0xOssN6U8QmuB+1Z+HK773vXHCxY2p/4Z6EugY/tkC3F71S38/JNCS8NNKj3btklj1Q6kQ+5MhjToddI1u3paT2seAu5NQ4G2YM6p8BaXgwsdnVxdgADOd7zyxVcuSGt8KFAPcrdnKbyqo46DTQtkc1SanTR3W7VFMgG0CsSGmUpAssnHVMDc+MCWVZWipzuX1zVvP5XklpBMpgIW6FQk1qQCqTyvZcejN0F2gw58uG7npY8jHvc2Bb96xDQ/YQvMS/4FAh5puWXca26kncj+ezjR7WD+60Li8TBKzyLgl1sG6DXWhjrmUkflq5PbM8ZL22mwiHXm8JnHSnyOpzZN1M0T0+xVkKOelThj3QjKn9/0jXP2
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update SettlementMapping settlementMappingId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/SettlementMapping/{settlementMappingId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update SettlementMapping settlementMappingId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementMappingId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"mapping":{"type":"array","items":{"type":"object","properties":{"settlementAccountId":{"type":"string","nullable":true},"settlementAccountName":{"type":"string","nullable":true},"settlementAccountNumber":{"type":"string","nullable":true},"settlementBankName":{"type":"string","nullable":true},"settlementBankCode":{"type":"string","nullable":true},"sourceBanks":{"type":"array","items":{"type":"object","properties":{"bankName":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"SourceBankDetails"},"nullable":true}},"additionalProperties":false,"title":"MappingModel"},"nullable":true}},"additionalProperties":false,"title":"UpdateSettlementMappingRequest"}},"text/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"mapping":{"type":"array","items":{"type":"object","properties":{"settlementAccountId":{"type":"string","nullable":true},"settlementAccountName":{"type":"string","nullable":true},"settlementAccountNumber":{"type":"string","nullable":true},"settlementBankName":{"type":"string","nullable":true},"settlementBankCode":{"type":"string","nullable":true},"sourceBanks":{"type":"array","items":{"type":"object","properties":{"bankName":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"SourceBankDetails"},"nullable":true}},"additionalProperties":false,"title":"MappingModel"},"nullable":true}},"additionalProperties":false,"title":"UpdateSettlementMappingRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"mapping":{"type":"array","items":{"type":"object","properties":{"settlementAccountId":{"type":"string","nullable":true},"settlementAccountName":{"type":"string","nullable":true},"settlementAccountNumber":{"type":"string","nullable":true},"settlementBankName":{"type":"string","nullable":true},"settlementBankCode":{"type":"string","nullable":true},"sourceBanks":{"type":"array","items":{"type":"object","properties":{"bankName":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"SourceBankDetails"},"nullable":true}},"additionalProperties":false,"title":"MappingModel"},"nullable":true}},"additionalProperties":false,"title":"UpdateSettlementMappingRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      