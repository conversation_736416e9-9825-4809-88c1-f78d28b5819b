---
id: get-bank-statement-by-reference
title: "Get bank statement by reference"
description: "Get bank statement by reference"
sidebar_label: "Get bank statement by reference"
hide_title: true
hide_table_of_contents: true
api: eJztWE2P2zYQ/SvGnFqAG2+TXqrbbpJuAqRAsHZOCx9G4thmliIZcmTUEPTfi5FsWXaMxE5zyEEnySLncT6enoxXgw8UkY137zVksCK+R/c8Y2QqyfH99pGWFMkVBAoYVwmyJ5Atj1R4Vxhr2mBYKAgYsSSmKHtqcFgSZBAH8cZBBgF5DQoifalMJA0Zx4oUpGJNJUJWA2+DBCaOxq2gaVSP9fD2n9ndp/m7PdSaUFO8Cmwhm1PwLlGS9Ze3t3LRlIpoQltKBrOqKCglUFB4x+S4RaJ/eRosysn11yf4/DMVDApClJay6fDTDuqwMffeEjpoFJSUEq7o6zwVuMpazC11FTUKNPIFxxl9DmzpY4kMGVSV0XLwYSiXHJ0PGfH3DqsGclUpbHDeyXBL/uvPW+HBKR4bFjC4PwPTKCGgM251jxaPE3JVmbfT7dPXvpK8GgWF9enqoMQY+Q3y2aoP+5Hphk3ZhpDT1wVwRJewECK99tWOOV2kcUyr49yM41cvT8KGXMEYcStsZyrTT5t+2g/hg3H0hhiNTRcRYZDlI5UYny+KwvKkEd+a0eCIebv7wLMikjZStKbc8MVMm58gtvTvtIv0+ffysH7Hl4/epBnFjSno9Rrj0Vs9gC4iIV+HW3ptluaaGGm61kZKRvtxQJEl2kSHPj2cqP18ON9OJCWBk4n+KPoB8v8i7K/v0GlLsQMMwZqi/RhNPyc/ivQo0qNIjyI9ivSvJNLtf+hRnUd1HtV5VOdRnX8pdW4E89gHeSCeiHJNerpP8u1k6OiUxGu/M47kXRJvJ4MpBjOVwJt45BJ1z3qwNK17rGaab28iLUFBorjZm0hVtOLzMIeUTacrKlMVVhE1vZD7wvpKv8AQQHwd45a+HW9fepkCbiczYrZd9ncf34MCQe8K3Pwhwwk+cYntN2lvNH238KNG9ZQaeESN6pKvd015AgwGui/BSVv2Tw+NAQXZ8LRdbxYK1j6xgNV1jok+Rds08vhLRXEL2dNCwQaj6aj2VIM2Se51z4uTvHt/C3573Hlov09Ana9nr6hO9HSDtpJfoOCZtkdGX7No1N6b++lJdIcNnMA+ETEKu9W7oqDAg7UhxGLA2oe3c2ia/wByTFUW
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get bank statement by reference"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/bank-reconciliation/bank-statements/{reference}/by-ref"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get bank statement by reference

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"reference","in":"path","required":true,"schema":{"type":"string"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"reference":{"type":"string","nullable":true},"bankStatementFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"openingBalance":{"type":"number","format":"double"},"closingBalance":{"type":"number","format":"double"},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"},"transactionCount":{"type":"integer","format":"int32"},"transactions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponse"}},"additionalProperties":false,"title":"GetBankStatementResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"reference":{"type":"string","nullable":true},"bankStatementFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"openingBalance":{"type":"number","format":"double"},"closingBalance":{"type":"number","format":"double"},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"},"transactionCount":{"type":"integer","format":"int32"},"transactions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponse"}},"additionalProperties":false,"title":"GetBankStatementResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"reference":{"type":"string","nullable":true},"bankStatementFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"openingBalance":{"type":"number","format":"double"},"closingBalance":{"type":"number","format":"double"},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"},"transactionCount":{"type":"integer","format":"int32"},"transactions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponse"}},"additionalProperties":false,"title":"GetBankStatementResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      