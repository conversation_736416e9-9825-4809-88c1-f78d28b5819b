---
id: get-bank-statement-by-reference
title: "Get bank statement by reference"
description: "Get bank statement by reference"
sidebar_label: "Get bank statement by reference"
hide_title: true
hide_table_of_contents: true
api: eJztWE2P2kgQ/SuoThupJ0yye1nfZvIxibQrRQM5jTiU3QV0pj+c7jJaZPm/r8oGYwjKwiaHHHwC7K5XX8/P6NUQSorIJviPGjJYEd+jf54xMjnyfL99pCVF8gWBAsZVguwJ5MgjFcEXxpo2GBYKSozoiCnKmRo8OoIM4iDeeMigRF6DgkhfKxNJQ8axIgWpWJNDyGrgbSmBiaPxK2ga1WM9vPt7dvd5/mEPtSbUFK8CW8jhVAafKMn917e38qEpFdGUbSsZzKqioJRAQRE8k+cWif7haWlRMtffZgj5FyoYFJRRRsqmw087qMPBPARL6KFR4CglXNG3dSrwlbWYW+o6ahRo5AvSGX0ObBmiQ4YMqspoSXxYyiWp8yEj3u+waiBfOWGDD16W6/jPP26FB6d4bFjA4P4MTKOEgN741T1aPC7IVy5vt9uXr0MldTUKChvS1UGJMfJb5LNdH84j0w0b14aQ19cFcESfsBAivQnVjjldpPFMq+PajOffX5+EDbmCMeJW2M7k0k/bftov4S/j6S0xGpsuIsKgykdyGJ8vikJ3Mojv7WiQYt6ePvCsiKSNNK0pN3wx0+YniC39O+0iff65PNy/48tXb9KM4sYU9GaN8eipHkAXkZCvw3VBm6W5JkaGrrWRltF+GlBkiTbRYU4PJ2o/H+63E0kp4GSj/xf9APmjCPvPD+i1pdgBlqU1Rfsymn5JYRTpUaRHkR5FehTpX0mk2//QozqP6jyq86jOozr/UurcCOaxD/JAPBHlmvR0n+TbydDRccTrsDOO5FkSbyeDKZZmKoE38cgl6q71YGla91jNNN/eRFqCgkRxszeRqmjF52EuUzadJmK2beiNps3LFblU4jYFWwn6yyI4EIPH+GVo99zPoD03mfXhk7tPH0GBpOk63bySLZUhscP25bR3nP5zAkcT67k1MIsa1XVR76bzBFga6F4JJ/PZXz1MCBRkw2y7IS0UrENiAavrHBN9jrZp5PLXiuIWsqeFgg1G03HuqQZtknzXPUFO6u6NLvjtcWemvZiAOt/PXlq9COsGbSW/QMEzbY8cv2bRqL1J99OL6JINLMG+EHEMu7t3RUElD+4NIRYD+j68m0PT/AtAQFjh
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get bank statement by reference"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/bank-reconciliation/bank-statements/{reference}/by-ref"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get bank statement by reference

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"reference","in":"path","required":true,"schema":{"type":"string"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"reference":{"type":"string","nullable":true},"bankStatementFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"openingBalance":{"type":"number","format":"double"},"closingBalance":{"type":"number","format":"double"},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"},"transactionCount":{"type":"integer","format":"int32"},"transactions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponse"}},"additionalProperties":false,"title":"GetBankStatementResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"reference":{"type":"string","nullable":true},"bankStatementFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"openingBalance":{"type":"number","format":"double"},"closingBalance":{"type":"number","format":"double"},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"},"transactionCount":{"type":"integer","format":"int32"},"transactions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponse"}},"additionalProperties":false,"title":"GetBankStatementResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"reference":{"type":"string","nullable":true},"bankStatementFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"openingBalance":{"type":"number","format":"double"},"closingBalance":{"type":"number","format":"double"},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"},"transactionCount":{"type":"integer","format":"int32"},"transactions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponse"}},"additionalProperties":false,"title":"GetBankStatementResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      