---
id: trigger-background-settlement
title: "Trigger background settlement job"
description: "Trigger background settlement job"
sidebar_label: "Trigger background settlement job"
hide_title: true
hide_table_of_contents: true
api: eJyNkkFr3DAQhf+KeacG1DjtUbcUSppCaOhuTmYPY3tiq7ElRZJNF6P/Xsa72WXTHnoy6M2M3/dmFjjPgZJx9r6FRgqm6zh8oealC26y7YZTGnhkm6CQqIvQFc7yd1djp+Ap0MiJg8gLLI0MjbuvD5vbp+03KBgLjZ6p5QCFwK+TCdxCpzCxQmx6Hgl6Qdp76YwpGNsh550UR+9s5Cj655sb+bQcm2C82IbGZmoajhE5q3fK9oBT1CfDRTwBFb9cDYWRU++E3bsokJ5SD42SvCkvQMtDKxQih/mNdQqDoKXkoy7L8/SPLc/XHY/R0z66YRJH140bIUzGPrsV18hAjbtDXXFOu7h9vIeC/OaAMn9CVqvHkaz0HkP+H8SLUE4hJ/6dSj+QsTJ55ViO9BXIG6h3ixbwNYGdQi9h6QrLUlPkpzDkLM+vE4c9dLVTmCkYqgWv2mX1tnyJrDVRhBb6mYbIf/lrnE1ycBoffh4v5aqQ8/uX7xfeX57aTMMkZWvQp+U+/thskfMfVc4C1w==
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Trigger background settlement job"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/BackgroundJob/settle"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Trigger background settlement job

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      