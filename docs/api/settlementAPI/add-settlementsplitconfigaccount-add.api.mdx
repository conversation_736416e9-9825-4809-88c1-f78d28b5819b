---
id: add-settlementsplitconfigaccount-add
title: "Add SettlementSplitConfigAccount add"
description: "Add SettlementSplitConfigAccount add"
sidebar_label: "Add SettlementSplitConfigAccount add"
hide_title: true
hide_table_of_contents: true
api: eJzlVctuGzEM/BWDpz7kOE+n3ZsbBGkORYM46cXwgV5xbaVaSZG0TozF/ntB7caPpDV6bn2xIQ0pcoYc12AdeYzKmmsJGaCUY4pRU0kmBqdVzK0p1Bzz3FYmjqQEARHnAbIJbJBjRl4k5KhFwlSAQ48lRfKMrsFgSZDB1eW38ej+7isIUAYyWBBK8iDA02OlPEnIoq9IQMgXVCJkNcSV48gQvTJzaJppC6YQv1i5YkRuTSQT+Sc6p1WeWho8BGv47E0qO3ugPIIA55mAqCgkHPfxA3VFW1hTlbNUYGF9iREykLaaaYJGQFhT0PXNLL4ueCuyqpSEphFMtOISUd9sVVCgDiQgqqg5frStRuK4e+W27T5livQc/4tGt4X98PHf75hzeQrOmtC2cHx4yF+SQu6V4ycgg3GV5xRCenn3ZiRlb9+K9jBtc0lxYXn3nQ2JNowLyGCATg32hQ/a8EB++bLhlde80DG6kA0GGwL7kpYHcyqDw1WwuuICD3JbAm8yC3m72ebLZyydpteSHf5BEDgp8NNZMTztn50fnfdPz4bH/dlJkfeP88/Dk2I4xAKHLKcyhU36dQJcteVsMdQb3VyDAO6mJXB5xIFMS4lp1joH+0tid9RYD05aV6dRGU6eGKs7zieAToHY76tpvthdFyxXNoG6nmGge6+bho8fK/IryCZTAUv0Cnmas8m0ES9Gy0JJFfhCrsfyVa1rO4V3t50rv++x8/+uh5+02rX1ZSsZQCPq7vaiTdi/4wQbxBuv5oFYD+TN9/EdCJh1Hl9ayTEen/jPAp/4BQE2FZ32I53VoNHMK5wzts3Jn1+jl2tP
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Add SettlementSplitConfigAccount add"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/SettlementSplitConfigAccount/add"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Add SettlementSplitConfigAccount add

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"}},"text/json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      