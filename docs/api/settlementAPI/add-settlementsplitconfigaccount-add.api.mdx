---
id: add-settlementsplitconfigaccount-add
title: "Add SettlementSplitConfigAccount add"
description: "Add SettlementSplitConfigAccount add"
sidebar_label: "Add SettlementSplitConfigAccount add"
hide_title: true
hide_table_of_contents: true
api: eJzlVU1v2zAM/SsBT/tQmn6mm29ZUXQ9DCuSdpcgB8aiHXW2pOojbWD4vw+U3TRtt2LnLZcY8iNFvkc+N2AsOQzK6EsJGaCUMwqhopp08LZSITe6UCXmuYk6TKQEAQFLD9kcnpAzRp4l5KRDwkKARYc1BXKMbkBjTZDBxfm32eTm+isIUBoyWBFKciDA0V1UjiRkwUUS4PMV1QhZA2FjOdIHp3QJbbvowOTDFyM3jMiNDqQDP6K1lcpTS6NbbzSfvUpllreUBxBgHRMQFPmE4z5+YBVpB6tjvUwFFsbVGCADaeKyImgF+C0Ffd/M4suCdyJjVBLaVjDRikvE6mqnggIrTwKCChXHT3bVSBz3t0y77lOmQA/hv2h0V9gPH//9jjmXI2+N9l0Lh/v7/CfJ505ZvgIymMU8J+/Tzc/fTKQcvLWiA0zbXFNYGd59a3yiDcMKMhihVaO3wkdduCe3ftzw6Cpe6BCsz0ajkmofbelQ0h4/55WJcg+tBV5g1m/6tMTnD1jbil4qtf8HHeCowE8nxfh4eHJ6cDo8PhkfDpdHRT48zD+Pj4rxGAscs4pKFybJ1vN+QbW3uNkhZjC5ugQB3ETH2/qAA5mNGtOI9cb1l3w+E2E7L2lLbYVKc/JEVNNTPQe0CsTbdprGik11xSplc2iaJXq6cVXb8vFdJLeBbL4QsEankIc4my9a8eivrI9Unl/I7TS+qHXrovBu2pvx+wEb/u96+Emb526+7iQDaEXTvz3rEg6vOcET4pVF80Bs5/Dq++waBCx7a6+N5BiH9/yNwHu+QYBJRae1SGcNVKjLiCVju5z8+wULmGeE
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Add SettlementSplitConfigAccount add"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/SettlementSplitConfigAccount/add"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Add SettlementSplitConfigAccount add

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"}},"text/json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      