---
id: get-batchperiod-getdefault
title: "Get BatchPeriod GetDefault"
description: "Get Batch<PERSON>eriod GetDefault"
sidebar_label: "Get Batch<PERSON>eriod GetDefault"
hide_title: true
hide_table_of_contents: true
api: eJztV0tv2kAQ/itoTq20KWl68420EcmhLQrJKeIweAfYdL272R2jIsv/vRrzMgGlNL36ZIl57Hzz+MRXgQ8UkY13dxoymBNfI+eLQNF4PSTWNMPSMihgnCfInqCxjxo7TBQEjFgQUxRjBQ4LggyGN9/Hg8eHW1BgHGSwINQUQUGkl9JE0pBxLElByhdUIGQV8CpIZOJo3BzqeiLOKXiXKIn96vJSPppSHk2QiiGDcZnnlBIoyL1jctxkot/cDxbl5er4BT99plwQhSjg2azzp02qvePUe0vooFZQUEo4p+M6FbjSWpxaWiOqFWjkM56b7tsonT/OO/OxQIYMytJoqaEV8dXrM2vZjO8kqFn0xYMpzsvE/mzX9fLc+jK23I1jmjcrsANmHH+5kkLySMikB/xmHzQyXbAUIQPx2szM+2KuV2fB2LvfFGjsWTGJmC0V5HgU/cxYau8TxogruQimIv19QwqK+QIdX5fJOErpB57Z/23gTzf1GLVx89MbdhSIJfvxDsGppREnrY1cH9pRq9wZ2kQK2LBt7p94/LoV95trllG8evof0rboZ5/w/+K331t02lJcpwvBmrwhxv5z8h2TdEzSMUnHJO9gkubfSEchHYV0FNJRyHsopK6b22nLniFxrxXbGxJ/2wm1gnjhN2JO5oi8gAz6GEy/FdM/iEkUl1sJV0Yrio05pKzf3y/RhablpzkVKeAqeVtKLZ9yX4BINeNmvpnRDlzj19s3vjcY3YECeWYNYvlZmh984gIbctxqx7fAHfRhtxItxVerNYBqA/wJMBhQB7JVQSvnRMHCJxbPqppiosdo61p+fikpriB7mihYYjTrFXma1GorZ6Vb2iQx6N14X1W406Xw4X6jfT/2RE6fqvwXrQ7F8xJtKW5Qq2pjHeQ5BW7Z2ikmrfEPbx6grv8AysWU4g==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get BatchPeriod GetDefault"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/BatchPeriod/GetDefault"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get BatchPeriod GetDefault

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      