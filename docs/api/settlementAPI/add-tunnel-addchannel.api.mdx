---
id: add-tunnel-addchannel
title: "Add tunnel settlementTunnelId add channel"
description: "Add tunnel settlementTunnelId add channel"
sidebar_label: "Add tunnel settlementTunnelId add channel"
hide_title: true
hide_table_of_contents: true
api: eJztV01v2zAM/SsBT/tQm21H37Ku6HoYVrTdKciBsZhEnSypEp0tMPzfB9pu7DbBkAzdpWgudmyKH49P1mMFPlBENt5dasgAtb4tnSM70TpfodyBAsZlgmwKN8RsqSDHrRHMFASMWBBTFIsKHBYEGaQnlpcaFBgHGQTkFSiIdF+aSBoyjiUpSPmKCoSsAt6ExgNH45agYOFjgQwZlKXRUNdqG+Ti/NvN5Mft1wfXK0JN8QjndT1rjSnxZ683YpF7x+RYbjEEa/IGnPFd8k6e7bjy8zvKGRSEKFCyodS46cDbU5ArrcW5pTa7uoGQHcUzr+kA+1pJl4xkhfZqEHSBNpECNiy2MNH6rE3iaduu24oFS2D6zS+2uGED371/mVWKs0gpeJfatD99+CAXTSmPJkgMyOCmzHNKCdSQ303vg0VzFCypc9Ubzr23hA5qBQWlhMtDMFCgkQ8IZ/QBHwX1H5ulwKQvtMDS8v6aTZrkbNa0/20eCZn0hP9ahkamEzYFNSB6bRbmmDWHEuqCeIdQ1x15nsfLw/UrOm0p7mzDYzfhK9te2XYc2/7pRHul2SvNjqFZc+w+PmInWo+4OZxHu+p3hFqPekFdEK+86O3gU8MOEcUZjDGYcetjXO06qceo9UnvJVFcP+juMlpRwMwhZeNxv/ZE0/p0SUUKuEnelpLsae4LEOkru+S6l7/nv7EIlh5pn75Fjwi2VdAivRe+aekWyCbYqIdxNLm6BAWSawvV+qMslNoLbLZpJ+iPgfAR+FtCDSRNrVpQqg7dKWAwMsyUnYds75AyhHimYCUNyqZQVXNM9CPaupbH9yXFDWTTmYI1RtPurGkF2iS511t6PUlzq77gzXU3pLwdSVL70u8eotsIemhL+QcKftJm/4hVz2r1MAU9ezZt1MHMtc1IRrL27Vnr8ORWHPQWOwKgXzHJcwo8sB0GnQ12ytX3m1tQMO/GtKKlYcRfMu/hrxYZ35TWfGKbZxVYdMuy+XhDG1l+fwBd1UF6
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Add tunnel settlementTunnelId add channel"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/tunnel/{settlementTunnelId}/add-channel"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Add tunnel settlementTunnelId add channel

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementTunnelId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddChannelSettlementTunnelRequest"}},"text/json":{"schema":{"type":"object","properties":{"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddChannelSettlementTunnelRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddChannelSettlementTunnelRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"}},"additionalProperties":false,"title":"GetChannelSettlementResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"}},"additionalProperties":false,"title":"GetChannelSettlementResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"}},"additionalProperties":false,"title":"GetChannelSettlementResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      