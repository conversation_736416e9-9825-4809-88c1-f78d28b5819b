---
id: get-auto-settlement-charge-setups-by-merchant
title: "Get auto settlement charge setups by merchant settlement profile"
description: "Get auto settlement charge setups by merchant settlement profile"
sidebar_label: "Get auto settlement charge setups by merchant settlement profile"
hide_title: true
hide_table_of_contents: true
api: eJztV8Fu2zAM/ZWApw1Qlm5H39KhaAOsW9G0pyAHxmIcdbKkSlSwwPC/D7LjxG3TtcXaW05RbOqRenyUyQqsI4+srJlIyKAgHke2U2LWVJLh7yv0BU2Jowunm0vy+QoNgwDGIkA2g2fNYS7AoceSmHwyrcBgSZBB2JlfebtUmiYSBCgDGTjkFQjwdB+VJwkZ+0gCQr6iEiGrgDeugWCvTAECltaXyJBBjEpCXYudl/Ozy+n49uaig14RSvJvAK/reTIOzppAIb3/dnKSfiSF3CuXSIMMpjHPKQQQkFvDZLhBoj88chqT5+qpB7u4ozyx6Hzin1WLH7ZQe8OFtZrQQC2gpBCwoEMkmKg1LjS1J6oFSOS+O/QeN4kHpjK8HIaSr2BaQN7kumdqYrloGN6ZShtTWK2xMaRfFf0TfbwccrkV5mkMylAIPxsNvMJXt/GXWVj0UplicvD0TzbiA90fSlkyklIlmaC+6oW7RB1IACvWjVCJp49PfL2VXUOdJ2SSY/5nViQyDVmVzZbSSrVUb9nzhmAPVXw/3kdUvRPyDxW4W1+gkZp8G7VzWuXNDTa6C/ZYb8d6O9bbB9Zb82U7Ftqx0I6F9pGFVif4h43mOfEgJWewl9GglWp6El0YLDaDLu19I7cVWxIFr+y2z09SS912BiN0apSQh/tNwxZ52CKPOthRdaB5ryFJ26+7Nj96nfptZhey0aigMkRXeJT0Ja1zbaP8gs5B6q+VWdomlTuyyuBwM9izNRhfTUBAQm95WH9NyXA2cInNLdQ1/P/PzwPCd/rqNfO1aE9XbbmbAToFbdE8yx7sqxEEZIfGn7mAlQ2cAKtqgYFuva7r9Pg+kt9ANpsLWKNXrf5mFUgV0lru5PYo9t0wAp+utwPP50Ga2w6dqbtOTbpM16hj+gcCftPmmXmtnteiG6nePZzWbW+A24WU5rv27TjPyXHvXR9i3pP6+dkN1PVf96dHBQ==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get auto settlement charge setups by merchant settlement profile"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/auto-settlement-charge-setups/merchant/{settlementProfileId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get auto settlement charge setups by merchant settlement profile

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementProfileId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"},"nullable":true}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseListResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"},"nullable":true}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseListResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"},"nullable":true}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseListResponseHandler"}}}}}}
>
  
</StatusCodes>


      