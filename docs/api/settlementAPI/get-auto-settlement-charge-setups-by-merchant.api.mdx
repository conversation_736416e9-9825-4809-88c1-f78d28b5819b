---
id: get-auto-settlement-charge-setups-by-merchant
title: "Get auto settlement charge setups by merchant settlement profile"
description: "Get auto settlement charge setups by merchant settlement profile"
sidebar_label: "Get auto settlement charge setups by merchant settlement profile"
hide_title: true
hide_table_of_contents: true
api: eJztV8Fu2zAM/RWDpw1Q5m5H39KhaAOsW9G0pyAHxWISdbKkSnSwwPC/D7Rjx23TtcXaW05xLOqRenyUyQqcxyBJOztRkMEKaVySmyKRwQItfV/LsMIpUunj6fYSQ76WlkAAyVWEbAbPmsNcgJdBFkgY2LQCKwuEDGJvfhXcUhucKBCgLWTgJa1BQMD7UgdUkFEoUUDM11hIyCqgrW8gKGi7AgFLFwpJkEFZagV1LXov52eX0/HtzUUHvUapMLwBvK7nbBy9sxEjr387OeEfhTEP2jNpkMG0zHOMEQTkzhJaapDwD6XeSPZcPfXgFneYM4s+MP+kW/y4g9obLpwzKC3UAgqMUa7wEAm2NEYuDLYnqgUoSUN3MgS5ZR4Ii/hyGFq9gmkBeZPrgakti0XDcG+qXMlhtcbWonlV9E/08XLIxU6Yp2XUFmP82WjgFb66jb/swsmgtF1NDp7+yUb5QPeHUsZGSmmWiTRXg3CX0kQUQJpMI1Sk6eMTX+9k11AXUBKqMf0zK0oSjkgXzZbCKb3Ub9nzhmAPVfww3kdUvRPyDx2pe76QVhkMbdTeG503N1h6F92x3o71dqy3D6y35st2LLRjoR0L7SMLrWb4h43mOVLCyUn2MkpaqfKb0sdksU26tA+N/E5sLApau12fz1LjbjuDVHqdMvJov2nUIo9a5LSDTasDzXsNLO2w6dr8Mhjut4l8zNJ0gKlw82WFRfRyG50p+VxfclcAN9raLl2T0561xi7Z05aMryYggN20hGy+cla8i1TI5jrqOv//J+oB873QBl19LdpjVjsSZyC9hrZ6nqUR9mUJArJDc9BcwNpFYsCqWsiIt8HUNb++LzFsIZvNBWxk0K0QZxUoHflZ9bp7FHs/lcCn693k8znhAe7Qmbp71fKtupGm5H8g4Ddunxnc6nktutnq3cNp3Q4muT4kHvTa1XGeo6fB2hBiPtD8+dkN1PVfSLBK0A==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get auto settlement charge setups by merchant settlement profile"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/auto-settlement-charge-setups/merchant/{settlementProfileId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get auto settlement charge setups by merchant settlement profile

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementProfileId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"},"nullable":true}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseListResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"},"nullable":true}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseListResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"},"nullable":true}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseListResponseHandler"}}}}}}
>
  
</StatusCodes>


      