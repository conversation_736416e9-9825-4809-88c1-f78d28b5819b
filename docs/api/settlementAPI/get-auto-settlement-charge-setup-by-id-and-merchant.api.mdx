---
id: get-auto-settlement-charge-setup-by-id-and-merchant
title: "Get auto settlement charge setup by ID and merchant"
description: "Get auto settlement charge setup by ID and merchant"
sidebar_label: "Get auto settlement charge setup by ID and merchant"
hide_title: true
hide_table_of_contents: true
api: eJztV8Fu2zAM/RWDpw1Q6m5H39KtaHPoVjTtqchBsZhEnSypEh0sMPTvg2QncZt0S4Zup5ycWOQj+UjKZAPGouMkjR4JKGCONKzJjJFIYYWaviy4m+MYqbYXq5EYanGDrlxwTcCA+NxD8QhvqsCEgeWOV0joomgDmlcIBfh4PBLAQGoowHJaAAOHz7V0KKAgVyMDXy6w4lA0QCub1MhJPQcGM+MqTlBAXUsBIbA+cufIrTMzqfCfWLm6vBkPH+6v19AL5ALdEeAhTKKwt0Z79PH88/l5fAj0pZM2pgQKGNdlid4Dg9JoQk0JCX9SbhWPlptdC2b6hGXMj3UxuyRbfN9BbQWnxijkGgKDCr3nc9xHgq6V4lOFbUSBgeB0gDkpDmCUQZmqpSeq62qamNyIClNH862w1qgO8nKnDv7sctWV9kXtpUbvv6VcH2BrrfhdTw13Qur5aG/0O4r8RefsS00UEkLGcuDqtufujCuPDEiSSgWJNH4d8V1XXok6h5xQDOm3WRGccECySiqVEXImj9E5wtl9d8bW33cCWj+vuRYKXYtrrZJluvLyJ29OLXRqoVMLHddC6ftz6p1T75x658jeCRH65YR3hZTFPGTbisnaqszSkJxNV9noa8a1yKrt6F0hLUw3scdCipNtATm3Mo9ggy3YoAUbJDCfN93kHfI1Wnr1emQOEEvYLddje+1UnHKJrC/yvIcucHk2x8pbvvJG1TGos9JUEMdbqWcm5W5DV5LLtnxlw9sRMIhmWjaWn2LqrfFU8XS9rOftv2LpBdObGuqNz4G1kTUdg4/ArYS2Md7kEBgU2/2lZ67Yt3tMGCyMp4jdNFPu8cGpEOLr5xrdCorHCYMld7LtzMcGhPTxt9iU26swNpsAfLjrto2PWVzH9oXXveR6FXnmqo7/gMEPXPXWsLjZ/F+zOzyFSWDrNerdWWjN9pa2jUsx8vZ0WJZoqXfWh5j0Wu7q8h5C+AWgEF4M
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get auto settlement charge setup by ID and merchant"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/auto-settlement-charge-setups/{setupId}/merchant/{settlementProfileId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get auto settlement charge setup by ID and merchant

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"setupId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"settlementProfileId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"charge":{"type":"number","format":"double"},"channel":{"type":"string","nullable":true},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponse"}},"additionalProperties":false,"title":"GetAutoSettlementChargeResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      