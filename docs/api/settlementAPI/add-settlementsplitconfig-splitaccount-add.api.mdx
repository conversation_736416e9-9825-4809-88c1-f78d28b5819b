---
id: add-settlementsplitconfig-splitaccount-add
title: "Add settlement split config settlementSplitConfigId SplitAccount add"
description: "Add settlement split config settlementSplitConfigId SplitAccount add"
sidebar_label: "Add settlement split config settlementSplitConfigId SplitAccount add"
hide_title: true
hide_table_of_contents: true
api: eJzlVU1P20AQ/SvWnPqxwXyG1rcUIcqhKiLQS5TDxDtOltq7y+4aiCz/dzTrkBhoqlbi1OYSaz1+8/Fm32vAWHIYlNHnEjJAKccUQkkV6eBtqUJudKHmY37EPDe1DiMpQUDAuYdsApvwGHNiJMFUgEWHFQVyHNSAxoogA/8ymLHPGU5pyMBiWIAAR7e1ciQhC64mAT5fUIWQNRCWNsIEp/QcBBTGVRggg7pWEtpWrDOdnX4bj66vvj5BLwglub8Ab9tpF0w+fDFyyRG50YF04Ee0tlR5HFx6443ms1dQZnZDeQAB1vGYgyIf47j3H1jW1IvVdTWLBa57kqaelQSt6I1t1FHAXP3BNJhOxSViedGroMDSk4CgQsnfj/qcR15WWS677iNSoIfwXzTaJ/bDx3+/Y8Zy5K3Rvmthf3eX/yT53CnLKSCDcZ3n5H3M/PzNSMpk00QSB5B0opFsue5Jv5IEo5xUFBaGFcgaH8fKUpBBilalG5hBhB908GmzBb9N+wnSLoEnd/ekRrUrWRJCsD5L0zlVvrZzh5J2+DkvTS130FpgCeANuNzIwOkDVrakl1zvbmESDgr8dFQMDwdHx3vHg8Oj4f5gdlDkg/388/CgGA6xwCHvgdKFicSvmDujyltcJhv6ktHFOQjgJrrJ3+3xhzyvCuOSrqTvjRh5RvN6I6MO2BKV5uRxkM2KrAmgVdAfxDO6QEC2Xf/7BUDcbnaRBS9DNoGmmaGna1e2LR/f1uSWkE2mAu7QKeS7xLxK5flZru/Bix7W+g3vLlc28D5hL/tVb6tD1EueekczgICftPyNk7XTVjyZzZuX1KXuWdu6LHa+7u1JBzi4YoBNxCu/4t1eX7qL7+MrEDBb+VzFJp6Bw3s2TLzvGjex6KgR8ayBEvW8xjnHdpj8ewRy5es0
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Add settlement split config settlementSplitConfigId SplitAccount add"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/settlement-split-config/{settlementSplitConfigId}/SplitAccount/add"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Add settlement split config settlementSplitConfigId SplitAccount add

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementSplitConfigId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"}},"text/json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      