---
id: add-settlementsplitconfig-splitaccount-add
title: "Add settlement split config settlementSplitConfigId SplitAccount add"
description: "Add settlement split config settlementSplitConfigId SplitAccount add"
sidebar_label: "Add settlement split config settlementSplitConfigId SplitAccount add"
hide_title: true
hide_table_of_contents: true
api: eJzlVctu2zAQ/BVhT31QcZ5Oq5sbBGkORYM46cXwYS2ubKYSyZCUE0PQvxdLObaS1EUL9NT6YoFazT5mOdOAseQwKKMvJWSAUo4phJIq0sHbUoXc6ELNx/yIeW5qHUZSgoCAcw/ZBLbhMebMSIKpAIsOKwrkOKgBjRVBBv5lMGNfMpzSkIHFsAABju5r5UhCFlxNAny+oAohayCsbIQJTuk5CCiMqzBABnWtJLSt2GS6OP8yHt3efH6CXhBKcn8A3rbTLph8+GTkiiNyowPpwI9obanyOLjBnTeaz15Bmdkd5QEEWMdjDop8jOPev2FZUy9W19UsFrjpSZp6VhK0oje2UUcBc/Ub02A6FZeI5VWvggJLTwKCCiV/P+pzHnlZZ7nuuo9IgR7Df9Fon9h37//9jhnLkbdG+66Fw/19/pPkc6csp4AMxnWek/cx8/M3IymTbRNJHEDSiUay47on/UoSjHJSUVgYViBrfBwrS0EGA7RqsIVJI3zawQ+aHfjtoJ9g0CXw5JZPalS7kiUhBOuzQR9e0nJvTpW3uPKmrLnFvdxUwFrAq3C91YPzR6xsSS9J399BKRwV+OGkGB6nJ6cHp+nxyfAwnR0VeXqYfxweFcMhFjjkhVC6MHED1hRedOUkWx6T0dUlCOBuOgqWB/whD67CuK1rDfxL1Dzje7OaURBsiUpz8jjRZs3aBNAq6A/iGW8gINttBP0CIK4528mCtyKbQNPM0NOtK9uWj+9rcivIJlMBS3QK+VIxwVJ5fpabC/Gih42Qw5vrtR+8TdjUftbb+hD1iqfe0Qwg4DutfmFp7bQVT67z10vqUvc8blMWW2D39qwDTG8YYBvxyrh4tze37+rr+AYEzNaGV7GbZ+DwgZ0TH7rGTSw6ikU8a6BEPa9xzrEdJv9+AC3/7v8=
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Add settlement split config settlementSplitConfigId SplitAccount add"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/settlement-split-config/{settlementSplitConfigId}/SplitAccount/add"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Add settlement split config settlementSplitConfigId SplitAccount add

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementSplitConfigId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"}},"text/json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"splitValue":{"type":"number","format":"double"},"settlementAccountId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"AddSettlementSplitAccountRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      