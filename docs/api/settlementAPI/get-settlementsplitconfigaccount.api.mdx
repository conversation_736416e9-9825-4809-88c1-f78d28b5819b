---
id: get-settlementsplitconfigaccount
title: "Get SettlementSplitConfigAccount settlementSplitAccountId"
description: "Get SettlementSplitConfigAccount settlementSplitAccountId"
sidebar_label: "Get SettlementSplitConfigAccount settlementSplitAccountId"
hide_title: true
hide_table_of_contents: true
api: eJytk0Fv2zAMhf9K8E4bINTdjroFQ5HlMGBY2lOQA2szjjBbUiU6WGDovw9MnKzd1gBDd7IsPT2SH6kRIXIiccEvG1i0LCsW6bhnLzl2Turgt66lug6DFxgItRl2jV+ylco+HWXzSbYxiJSoZ+Gk6hGeeoZFfnlr0i8bGDgPi0iyg0Hip8ElbmAlDWyQ6x33BDtCDvHoI8n5FgbbkHoSWAyDa1CKuYRa3H1ZzR/uP5+td0wNp38wL2Wj4hyDz5z1/OPtrX4aznVyUaHBYjXUNeessX87WbDMrmGaXaHRs+zC1BGYExeLiqKrrllW42ueBQaZ0/7ckCF1CkUkZltVLfd5iG2ihm90XXdhaG4oRigE57fhyMdJd0TLfY50eFbcbP51CQN1P9W+/4BiEEOWnrzePXflDUxe0L10S/iHVLEj5zXisaxx4rUGRQdzfVYN7KsxNwa7kEWdxvGRMj+krhTdfho4HWDXG4M9JUePCmY9onFZ1w3slrrMfyRdBy/sdWDffZvm8P1Mn9Xfipk2yR+ULXWD/sHgOx+uvaWyKeY87v89p1PsZ4/rkpdOymVsF3f3KOUn2TiIuA==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementSplitConfigAccount settlementSplitAccountId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementSplitConfigAccount/{settlementSplitAccountId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementSplitConfigAccount settlementSplitAccountId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementSplitAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      