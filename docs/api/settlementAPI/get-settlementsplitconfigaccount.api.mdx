---
id: get-settlementsplitconfigaccount
title: "Get SettlementSplitConfigAccount settlementSplitAccountId"
description: "Get SettlementSplitConfigAccount settlementSplitAccountId"
sidebar_label: "Get SettlementSplitConfigAccount settlementSplitAccountId"
hide_title: true
hide_table_of_contents: true
api: eJytk01v2zAMhv9K8J42QKu7HXULhiLLocCwtKcgB9ZmEqG2pEp0sMDQfx+YOGm7tQGG7WRZevn1kBwQIicSF/y8gcWGZcEiLXfsJcfWSR382m2orkPvBQZCmwy7xLNsobKvB9l0lK0MIiXqWDipeoCnjmGRX1uN+nkDA+dhEUm2MEj81LvEDaykng1yveWOYAfIPh78SHJ+A4N1SB0JLPreNSjFnEPNbm4X0/u7byfXW6aG0184L2Wl4hyDz5z1/cv1tX4aznVyUaHBYtHXNeessX97mbFMLmGaXKDRsWzD2BGYIxeLiqKrLrmshvd8FhhkTrtTQ/rUKhSRmG1VPVt9anh3teEuR9rn0PZay1UdOigN59fhAMpJe2B81L2ocjL9PoeBhjlC2H1GMYghS0debU/t+Qc4rzCf2yb8U6rYkvMa8VDfMIJbgqKDuTy0BvbdmCuDbciinobhgTLfp7YUvX7qOe1hlyuDHSVHDwpmOaBxWc8N7JrazH8kXQcv7HVyP/wYB/LjRPfrrWLGS/J7ZUttr38weOT9paUqq2JOc//fczrGfrFl57x0Us7zO7u5Qym/AEqQjIM=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementSplitConfigAccount settlementSplitAccountId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementSplitConfigAccount/{settlementSplitAccountId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementSplitConfigAccount settlementSplitAccountId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementSplitAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      