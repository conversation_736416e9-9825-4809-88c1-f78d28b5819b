---
id: get-tunnel
title: "Get tunnel settlementTunnelId"
description: "Get tunnel settlementTunnelId"
sidebar_label: "Get tunnel settlementTunnelId"
hide_title: true
hide_table_of_contents: true
api: eJztV01v2zAM/SsBTxugNt2OvgVd0fYwoOjHqciBsZhEnSypEp0tMPTfB/kjcVO3S4ECu/hkICIfySfqIa8C68gjK2uuJWSwIr4vjSENAhhXAbJHuCNmTQWZ7mguwKHHgph8iqjAYEGQQTiIvJYgQBnIwCGvQYCn51J5kpCxL0lAyNdUIGQV8NbVCOyVWYGApfUFMmRQlkpCjGJX5Lfi9fkaE37o4J9L8lsYwFtYqwnNC4DLi593s4f7qy55TSjJf6C7GOcpODhrAoV0/v3sLH0khdwrl9iEDO7KPKeQesytYTJcI9EfnjqNqXL1uoJdPFHOIMD5dDGsGvzQQg0MJqCgEHBFQyyaUmtcaGomigIk8hHllDziSkTL5zFV+7wcER90uToqEHNWGxqmJfeETHLG784ikemEVUE1k1aqpfpYTt6t4j4Dvce0jYqpCJ/GdlvoKF4cejbkz6087oJU+EFLLDUPU6nC7H8TnS5bSpVWCPVNj78l6kACWLGuHzdxKw572bptn2q9sy9H/wDsoQzuUT8BpPteoZGafIPpnFZ5rc3Tp2BHwRgFYxSMUTDeFoz6v8WoFKNSjEoxKsU7ShHj4cNJ+ROu0yaDJq4gXtvWIKYtS3Yugyk6NW3SptXrvJgcGflN5xJLr5PdYnYhm0738SeSNqcrKoLDbbC6TD2d5raA5LOUWdr6JneT1nGT/biT2c01CEhlmmE239J1OBu4wFoVOuP3jyGHtaTn2KJoZqhaAh4BnUpOubPM2QDuXMDaBk7RVbXAQA9ex5h+blxrYkaqkDZH7u71F21fW90N6jJ1VPOyQa+abRvKPxhlZ0Dhy21rcr9OUuNDI3ZSY7b9ml1PAxPGeRSdi/70bpqqPc++Z0FU7eksz8lx76wPMe9t7+XFPcT4F9Nn6fM=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get tunnel settlementTunnelId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/tunnel/{settlementTunnelId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get tunnel settlementTunnelId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementTunnelId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"withChannels","in":"query","schema":{"type":"boolean"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      