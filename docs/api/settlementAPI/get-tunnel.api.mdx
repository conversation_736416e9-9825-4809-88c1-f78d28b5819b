---
id: get-tunnel
title: "Get tunnel settlementTunnelId"
description: "Get tunnel settlementTunnelId"
sidebar_label: "Get tunnel settlementTunnelId"
hide_title: true
hide_table_of_contents: true
api: eJztV01v2zAM/SsBTxugNd2OvgVd0fYwoOjHqciBsZhEnSypEp0tMPTfB/kjcROvS4ECu/jkIBIfyUf6wa8C68gjK2tuJGSwIn4ojSENAhhXAbInuCdmTQWZ7mguwKHHgph8ulGBwYIgg3Bw80aCAGUgA4e8BgGeXkrlSULGviQBIV9TgZBVwFtXI7BXZgUCltYXyJBBWSoJMYpdkl+K1xdrTPihg38pyW9hAG9hrSY0rwCuLn/czx4frrvgNaEk/47qYpyny8FZEyik82/n5+khKeReucQmZHBf5jmFVGNuDZPhGol+89RpTJmr4wx28Uw5gwDn02BYNfihhRpoTEBBIeCKhlg0pda40NR0FAVI5BPSKXnCSETL5ylZ+7yccD/ocnXSRcxZbWiYltwTMskZv9mLRKYvrAqqmbRSLdX7YvJuFfcR6D2mbVRMRfgwtttEJ/Hi0LMhf2HlaQNS4TstsdQ8TKUKs/9NdBq2lCqtEOrbHn9L1IEEsGJdv9zErTjsZeuufVXrnX3d+jtgD2Vwj/oBIN3zGo3U5BtM57TKa22ePgc7CsYoGKNgjILxd8Govy1GpRiVYlSKUSneUIoYD1+cFD/hOmwyaOIK4rVtDWLasmTnMpiiU9MmbFodx8XkyMhvOpdYep3sFrML2XS6oiKUbuVR0ln6nWtbyjN0DpK9UmZp6wHuGiyCw+1k3+VkdnsDAhJ608Pma5qCs4ELrMWg83v/6G1YQnpGLYqm9Krt+wnQqWSQO6ecDeDOBaxt4HS7qhYY6NHrGNPfjVlNhEgV0sLI3Th/0vbY4W5Ql6mimpcNetUs2VD8QSs73wmf7lpv+3mSCh9qsVMYs+3n7Goa6DDOo+jM84dX02TtWfU9C6JqT2d5To57Z32IeW9pry4fIMY/4aTmKA==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get tunnel settlementTunnelId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/tunnel/{settlementTunnelId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get tunnel settlementTunnelId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementTunnelId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"withChannels","in":"query","schema":{"type":"boolean"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      