---
id: create-settlements-marksettlementlogassettled
title: "Create settlements mark settlement log as settled"
description: "Create settlements mark settlement log as settled"
sidebar_label: "Create settlements mark settlement log as settled"
hide_title: true
hide_table_of_contents: true
api: eJztWc9v2zoM/lcCnt4Pe+naNXvzLeuGrcD6GiTtKciBsRhHqyx5ktwtCPy/P9B2ayfNNqd4hwFzLkFkkhL5kfoccgsmI4teGn0pIILYEnqakfeKUtLeXaG9c48/lUnQVT8FBOAxcRDNoZGHRQAZWkzJk+VnW9CYEkTw4f3VbHx78xECkBoiWBMKshCApS+5tCQg8janAFy8phQh2oLfZKzpvJU6gaJYVMLk/FsjNiwRG+1512gLmGVKxqUnw8/OaF57YsosP1PsIYDMst9ekuOnRqvNbSbQ040ySUt+aYwi1FAE0AThk0k4VvvnC2BlbIoeIshzKVgnww0rTGlFlnRMh5R0rhQuFVXulxs5V8PRQZoPPTHOUzdxS95uLkxexawWl9pTUmLx6IHU/uwUiiIAFEJyUFFNWjFboXIUgJeejQNnySeTjF2VCWJawVQa8PTN94j8Soi0S+Wvv3tofhloinJTlxntqlifnpzwlyAXW5mxZYhglscxOQdB+/orqyxTKI/C0tWmDoKYknOYdMNBoO+yXeVwx4Rokmgcx4zC5btOZ/EWtcOYo3VcMmG6h7XO0+Uu1MLkrFEEEK/R7gTnR8I7AB7nwrvnq86OKo4m3FdGdAtYSjZeo/YdAa1eLsTY/1CaizT0MqWDOdA1ydpp0+10S9R3F109Z+F/yxebjsL1WZ6lYzppSDexMkW7OVzNcTfXul5iH8jP9gN9rdVmWl9fu+BNrFlJRT8H7yGj3uZOanKuc7weFK/10qAVUn+Hdp4Wfe5N6/31QOyeFZLa4yYcR1sR/4fuw/dH1EKRfUL/x5J/Txg9YfSE0RNGTxi/EWE86x98zxQ9U/RM0TNFzxS/DVOU/avdXtVFWZeDBi43SNHetRYGyiQDdINmqpCSXxseRmTGlTCiX0MEQ8zksGVoyIbCZiFUJgnRhY0hR/b+YQqRW8VDB+8zFw1bZkJB9y8SSl2GG2dUzud+EZsUeNrAnDdtJg7vv2GaVam23wStRxf7bU84W+E/56vRq/D89cvX4avz0Wm4PFvF4Wn8ZnS2Go1whSM41Pts0qrV4mwW253MZrXdsDzhutWrqsAfQSz9HDS5NBhPLiEADlMF2P3LshdrnE+xZIV6fPMcIA9TU6tTWQQVLtsa4zlgJqEdR+5y/gTnRQBrTpRoDtvtEh3dWlUUvPwlJ76y5osA7tHKqjbniyJ4GD5xYgjp+IF4zPi9Uz/2WOGPaT2p+nPAs69D3tzRZnfUdY8qZzEogm399KIyGN6wgUbiyb/yRmMcx5T5lmx700WrYCbXsxsIYFlPx9LyXgaLXzk38CufIwBTulZeieXaFhTqJC9fx6DamT//Ado19J8=
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create settlements mark settlement log as settled"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/settlements/mark-settlement-log-as-settled"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create settlements mark settlement log as settled

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"onlyUpdateTlog":{"type":"boolean"},"settlementLogId":{"type":"string","format":"uuid"},"paymentReference":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"datePosted":{"type":"string","nullable":true},"retryCount":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"MarkLogAsSettledRequest"}},"text/json":{"schema":{"type":"object","properties":{"onlyUpdateTlog":{"type":"boolean"},"settlementLogId":{"type":"string","format":"uuid"},"paymentReference":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"datePosted":{"type":"string","nullable":true},"retryCount":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"MarkLogAsSettledRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"onlyUpdateTlog":{"type":"boolean"},"settlementLogId":{"type":"string","format":"uuid"},"paymentReference":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"datePosted":{"type":"string","nullable":true},"retryCount":{"type":"integer","format":"int32"}},"additionalProperties":false,"title":"MarkLogAsSettledRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settledId":{"type":"string","format":"uuid"},"settlementAccountID":{"type":"string","nullable":true},"transactionReference":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"charge":{"type":"number","format":"double"},"description":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionSessionId":{"type":"string","nullable":true},"settlementMode":{"type":"string","nullable":true},"merchantId":{"type":"string","format":"uuid"},"createdAt":{"type":"string","format":"date-time"},"settlementAccount":{"type":"object","properties":{"settlementAccountId":{"type":"string","format":"uuid"},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"code":{"type":"string","nullable":true}},"additionalProperties":false,"title":"GetSettlementAccountOnlyResponse"},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"}},"additionalProperties":false,"title":"GetSettledResponse"}},"additionalProperties":false,"title":"GetSettledResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settledId":{"type":"string","format":"uuid"},"settlementAccountID":{"type":"string","nullable":true},"transactionReference":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"charge":{"type":"number","format":"double"},"description":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionSessionId":{"type":"string","nullable":true},"settlementMode":{"type":"string","nullable":true},"merchantId":{"type":"string","format":"uuid"},"createdAt":{"type":"string","format":"date-time"},"settlementAccount":{"type":"object","properties":{"settlementAccountId":{"type":"string","format":"uuid"},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"code":{"type":"string","nullable":true}},"additionalProperties":false,"title":"GetSettlementAccountOnlyResponse"},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"}},"additionalProperties":false,"title":"GetSettledResponse"}},"additionalProperties":false,"title":"GetSettledResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settledId":{"type":"string","format":"uuid"},"settlementAccountID":{"type":"string","nullable":true},"transactionReference":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"charge":{"type":"number","format":"double"},"description":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionSessionId":{"type":"string","nullable":true},"settlementMode":{"type":"string","nullable":true},"merchantId":{"type":"string","format":"uuid"},"createdAt":{"type":"string","format":"date-time"},"settlementAccount":{"type":"object","properties":{"settlementAccountId":{"type":"string","format":"uuid"},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"code":{"type":"string","nullable":true}},"additionalProperties":false,"title":"GetSettlementAccountOnlyResponse"},"settlementProfile":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"}},"additionalProperties":false,"title":"GetSettledResponse"}},"additionalProperties":false,"title":"GetSettledResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      