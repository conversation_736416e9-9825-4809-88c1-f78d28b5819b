---
id: get-settled-merchant-aggregated
title: "Get settled merchant merchantId aggregated"
description: "Get settled merchant merchantId aggregated"
sidebar_label: "Get settled merchant merchantId aggregated"
hide_title: true
hide_table_of_contents: true
api: eJytUz1v2zAQ/SvCTS3ARklHbh4C10OAInamwMNZOktEJZIhT24Fgf+9OH04duAlQCYC5L139949DuA8BWTj7KYEDRXxlpgbKp8oFDVaXlVVoAqZSlDAWEXQrzDXwF6Bx4AtMQV5GMBiS6Dhr+F6F9BGLIQ7ggJjQcNbR6EHBbGoqUXQA3DvBXBwriG0kJI6k7TzCJtygXvkGhQEeutMoBI0h45usEUOxlag4OhCiwwaus6UV+Trx6ft6mX3a6GuCUsKnyBPaS/F0TsbKcr7z/t7OUqKRTBedIOGbVcUFKP0/vCyJs7iZGS2SM3eNWd46XxLXLt5Q6AmIzTk6E0+c+QLNB/eSVJ+xRIpnJZNdaER2cw+6nwmacnyj5JOdxW10WMfXdPJtHeFa0H0Gnt0oxWGm9HFqS7bnuHZ6vcGFEibSebpAZIC7yK3aAW7LOAz8q+cO2+C6R/nvkFjpcUoaJiteQX0ZlQ8JVWd0wQK9FWyLhrtFdQussCH4YCRXkKTklxPwRXfShPxIJT6iE0kBX+ov534EzadjDk6d8JgBHeb44O+wlkmK7n99jzH8Xsm/++W7vkSbX/Zc5nrQmraJ7Xk/MunmLpd/Kor9ef4rh93kNJ/47GHjg==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settled merchant merchantId aggregated"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settled/merchant/{merchantId}/aggregated"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settled merchant merchantId aggregated

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"withTransactions","in":"query","schema":{"type":"boolean"}},{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      