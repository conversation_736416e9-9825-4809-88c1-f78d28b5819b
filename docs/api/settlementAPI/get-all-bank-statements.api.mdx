---
id: get-all-bank-statements
title: "Get all bank statements"
description: "Get all bank statements"
sidebar_label: "Get all bank statements"
hide_title: true
hide_table_of_contents: true
api: eJztWU1v2zgQ/SvGnHYBpU67vaxuTj/SAt0iiNNT4MOYHNtsKFIlR0azhv77YiTLllwlVbK76EUnw+TM43Dmkc/m7MDnFJCNdx81pLAmnll7ge5uzsiUkeMICTCuI6S3IBPXpLxTxprKCxYJ5BgwI6YgNjtwmBGkcIVr+lxkSwqQgHGQwreCwj0kENWGMoR0B3yfi6lxTOvKbuVDhlwP/fEKyjLp4M3N3/TfoF2++2s++3LzoUHbEOrKJ9C3wgTSkHIoqAc+cjBuDWW5EOOYexcpyvyr83P50BRVMHmVnBTmhVIUJYfKOybHFRJ952luUVbe/biCX34lxZBAHqQ6bGr8uIc6Gi69t4QOygQyihHX9GOcCbjCWlxaqndUJqCR28thCCiJNExZ/HkYRvctckh1URgtAQVaUSCnhoW0bDPu/R5rB+SKTHjnvJO6Z/zn63Nh3CkeGxYwuOiBKRPhuDNufYEWuwG5hp+H8LUvJK4yAWV9fLJTZAz8Frl310d7ZDpjk1Uu5PTTHDigi6iEYG98sWfUz6jfcYv/e/VjU4RPxtFbYjQ2DiJCK8pryjDcDfLC7CQRj9WotcRNZX3kmQqkjWxa09LwYKbdnCBW9K9vSdL95/U4P+PhpTdxTmFrFL3ZYOic9ha0CoT8NNzMa7MyT/GRpGttZMtor1oUWaGNdMzTJfFDqbreX54SwElFn4v+CKTI1EGQhp2YvFGcYeYrEyJfPXAJH09IMH0Mtvh8X/aMVpzj0KtAHETHgx7q4uj7s+PLA22NL+Lz/P8tFz6ZOrO6GfiATlsKNYfz3BpV/ZKZfo1+1ONRj0c9HvV41ONRj0c9/gV6XP0zHoV4FOJRiEchHoV4FOJRiH+FEJcC3n3IviSeoLUTEaRJbDcHMuKN3zcP5FpE3kAKU8zNVIzPQqdhUI91ACKFbdM8KIKV13jmPKbTaSRmW9mdadq+WFMWc7yP3hYC9UL5DOQZ3riVr/J32HFlN5kf3Cezq4+QgCxTb2f7siKyj5xh9WOj6Qs8uM1OOg61aj3ll0kd/W6fglvAXMrWk4RmtLXAIoGNjyxuu90SI30JtixluG50SHK0iVJ/fSjxHd2fdlq2aAsJDKTZ8YjDvpVyNF/Il2Bqgt0uyqTpifSufJKNQ3MDfrveN1B+n0DSn6U6iFYHphNzPTtTinJuzbUhFi3SXb67gbL8B7nBRyo=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get all bank statements"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/bank-reconciliation/bank-statements"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get all bank statements

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"reference":{"type":"string","nullable":true},"bankStatementFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"openingBalance":{"type":"number","format":"double"},"closingBalance":{"type":"number","format":"double"},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"},"transactionCount":{"type":"integer","format":"int32"},"transactions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"reference":{"type":"string","nullable":true},"bankStatementFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"openingBalance":{"type":"number","format":"double"},"closingBalance":{"type":"number","format":"double"},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"},"transactionCount":{"type":"integer","format":"int32"},"transactions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"reference":{"type":"string","nullable":true},"bankStatementFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"openingBalance":{"type":"number","format":"double"},"closingBalance":{"type":"number","format":"double"},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"},"transactionCount":{"type":"integer","format":"int32"},"transactions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      