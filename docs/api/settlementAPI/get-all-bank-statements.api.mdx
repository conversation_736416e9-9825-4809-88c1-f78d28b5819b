---
id: get-all-bank-statements
title: "Get all bank statements"
description: "Get all bank statements"
sidebar_label: "Get all bank statements"
hide_title: true
hide_table_of_contents: true
api: eJztWU1v2zgQ/SvGnHYBtU7bvaxuTj/SAu0iiNNT4MNYHMtsKJIlR0azhv77YiTLllwldbItetHJMTnzOB9PfI5mC85TQNbOflCQQk48M+Yc7e2ckakgyxESYMwjpDcgG1eUOZtpo2svWCTgMWBBTEFstmCxIEjhEnP6pyyWFCABbSGFryWFO0ggZmsqENIt8J0XU22Z8tpu5UKB3Cy9eglVlfTw5vpf+jloF28/zWefr9+3aGtCVfsE+lrqQApSDiUNwEcO2uZQVQsxjt7ZSFH2X56dyYeimAXt6+KkMC+zjKLUMHOWyXKNRN946g3KydvvT3DLL5QxJOCDdId1gx93UAfDpXOG0EKVQEExYk7fx5mALY3BpaEmoyoBhdw9DkNAKaRmKuKPw9Bq6JB9qctSKwko0IoC2ey0kJZdxr3bYW2BbFkI76yz0veC//7rTBh3jMeaBQzOB2CqRDhutc3P0WA/INvycx++cqXEVSWQGRcf7RQZA79BHsz6YI9Mz1gXtQtZ9TgHDmgjZkKw167cMepH1O+5xV/e/dg24aO29IYYtYknEaET5RUVGG5P8sLiqBAP9ahzxHVtfeBZFkhpSVrRUvPJTLs+Qqzp39ySpIaf18P+jE9vvY5zChud0es1ht7T3oHOAiE/DrdwSq/0Y3yk6EppSRnNZYciKzSRDnW6IL6vVFe7y1MCOOroU9EfgBSZ2gvSaU+MbxXnNPOVDpEv77mED09I0EMMNvh0X3aMRpzjqVeBOIiOB3Wqi6VvT47PB9poV8an+f9fLnzUTWVVu/AerTIUGg57b3RW/5KZfolu1ONRj0c9HvV41ONRj0c9/g16XP9nPArxKMSjEI9CPArxKMSjEP8OIa4EvP8i+4J4gsZMRJAmsTscKIjXbjc8kGsReQ0pTNHrqRg/C72BQbPWA4gUNu3woAxG3sYz+5hOpzkVsfR5QEXP5e/MuFI9R+9B3r5ru3J12faJFtHj3WROzKZGn8wuP0ACgt5ksXlR89dFLrD+jdGOA+7NrleFfYs6b/CrpAl6u8v8BtBLtwZyb1c7BywSWLvI4rbdLjHS52CqSpab+YbUROkobVf7zt7S3fGAZYOmlMBAZhwPOOwmKAfzhXwJuuHVzaJK2lHI4MlH1djPNOCPq93c5M8JJMNVaoLoDF56MTe7sywjz529LsSiw7WLt9dQVf8BsRVDXw==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get all bank statements"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/bank-reconciliation/bank-statements"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get all bank statements

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"reference":{"type":"string","nullable":true},"bankStatementFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"openingBalance":{"type":"number","format":"double"},"closingBalance":{"type":"number","format":"double"},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"},"transactionCount":{"type":"integer","format":"int32"},"transactions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"reference":{"type":"string","nullable":true},"bankStatementFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"openingBalance":{"type":"number","format":"double"},"closingBalance":{"type":"number","format":"double"},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"},"transactionCount":{"type":"integer","format":"int32"},"transactions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"reference":{"type":"string","nullable":true},"bankStatementFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"openingBalance":{"type":"number","format":"double"},"closingBalance":{"type":"number","format":"double"},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"},"transactionCount":{"type":"integer","format":"int32"},"transactions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      