---
id: get-settlements-all
title: "Get settlements all"
description: "Get settlements all"
sidebar_label: "Get settlements all"
hide_title: true
hide_table_of_contents: true
api: eJztWktv2zgQ/isBT7uAWne7N98cp5sGaLpCnOwl8GFMjm22FKkMKaNew/99QT0s2VESygj2xFMQab7hvMUvmR0zORI4afSNYGO2QjdD5xRmqJ2dKMUS5mBl2fiRtS/YPGE5EGTokPy7HdOQIRuzFFb4vcgWSCxhUrMxeyqQtixhlq8xAzbeMbfNvajUDlel3NJQBq569Odntt8nR/pm8l98H23TNWiN6k1l1pHUqyPoJDOFdm8ideP7wQphioXCI133BNoC90G/wyUSav62gz02pUjSiHOQf5HJQnFdX8DhByezE3fMu6m6gu276bpF4mvQ7uasCN3YG52SWRFa+yZ+YYxC0EcKrr/cziYP918b8BpBlJVB+FRIQsHGjgp8zZq5F7a50Ratf//50yf/Q6DlJHNfPmzMZgXnlY3caOeb02vCX26UK/An756fYBY/kPtizsm3v5OVflur6nEsYRlaCyvsS4QulAJf5KVH+8QnpHscEFV5dZjZADMOc+abWfmp9Erqi0IKb51rW2o4om3CEOc6wKtuJoZBp2ugo2i+PDl6cFPIz4X+A6o449wrcL3h6WvC01PLmRsSnyrzIkV6juqUokXaSI4pmY0USN/LhgtSf4SbGhGGg3L2pyBFYNwqwL2pvpiBIGsK4ngJ+mewYS0kPAYlZMK5t/BMVOVECC5rZ3BITzbiAzLajIrb4JgdICmZpVQ4y5V0wSGX9hIcX/cXJhTOdG5JATKDxgCho+20vIeEXHwStvCmDptuTQoetHwq8HK4AuvDORByCEfnm9sbPE4IDsXEhY+izAi5lMMwrUF1yQ/5aNWQwJJfDOn3xZBO98JD+7yLMUEIHt44KckMaPvS9WLQpPjfC6EeFkMKoYYMnH2XhZUarQ3OWQP8Wy8MkJD6hTvT88/am9PKCwkh/WccVNpxcwnKYsKcLD9v7Bpdc9uenbp/V99gB2mbnXbTWVqe29Sq6YlH3nLXsPGaN+Q0THwpybr0hTt0Ww8k+5Kl4HysMw6UB9tAS0vAHXJDIhSi8dfZ9uWEG2kKex7+XQrim6zCK5oHX0ELhVTVW54rycs/kox+WBM5VeRUkVNFThU5VeRUkVNFThU5VeRUkVNFThU51QBOVf6HKpKpSKYimYpkKpKpSKYimYpkKpKpSKYimYpkKpKpIWRq7084Xgq8RnfR9rm9gHKRN0O3NvWWrx8L4NZszEaQy1FHeFQJ+7tos9xbkPIbjM7ldjzqyH4QuPm4wszmsLVGFf70j9xkzK8uSr2sJvTBvVLuovXtYpLesIT5YyqzN3+UVWusy6AkLM0uZa87opfcdFYf90ll+a529ZFBLll3aPq1Sa9snrC1sc6L7HYLsPhAar/3j6udTx8EIa1Pqjjk7SduTzeeNxV3Ycwvgr4CqFeaQ8TbneUQ6cOacojwC3vIQU40q8chwvW2cZBJJlCw2hQOkTzaAw4BnKz+tpC5/4Vk1dqP833SLPX2lsdJeR62c9lvd/UG8O8Xfru+r2wrQzorxEd216nmHPNuqrsq5p1uv/5yz/b7/wDucb98
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settlements all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settlements/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settlements all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"Channel","in":"query","schema":{"type":"string"}},{"name":"Amount","in":"query","schema":{"type":"number","format":"double"}},{"name":"TransactionReference","in":"query","schema":{"type":"string"}},{"name":"Period","in":"query","schema":{"type":"string"}},{"name":"From","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"To","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"Day","in":"query","schema":{"type":"string","format":"date-time"}},{"name":"MerchantId","in":"query","schema":{"type":"string"}},{"name":"IsInProgress","in":"query","schema":{"type":"boolean"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"settlementLogId":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"transactionReference":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionCharge":{"type":"number","format":"double"},"transactionChargeCap":{"type":"number","format":"double"},"transactionChargeValue":{"type":"number","format":"double"},"transactionDate":{"type":"string","format":"date-time"},"transactionChannel":{"type":"string","nullable":true},"settledPerChannel":{"type":"boolean"},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"amountPaid":{"type":"number","format":"double"},"amountToSettle":{"type":"number","format":"double"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"merchantId":{"type":"string","format":"uuid"},"merchantName":{"type":"string","nullable":true},"settlementMode":{"type":"string","nullable":true},"settlementProfileSplitCode":{"type":"string","nullable":true},"isBatch":{"type":"boolean"},"autoSettlement":{"type":"boolean"},"autoSettlementCharge":{"type":"number","format":"double"},"retryCount":{"type":"integer","format":"int32"},"batchReference":{"type":"string","nullable":true},"merchantUniqueBatchReference":{"type":"string","nullable":true},"splitReference":{"type":"string","nullable":true},"settlementInProgress":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementAccount":{"type":"object","properties":{"settlementAccountId":{"type":"string","format":"uuid"},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"code":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"merchantId":{"type":"string","format":"uuid"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementProfile":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetSettlementAccountResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetMerchantSettlementResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"settlementLogId":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"transactionReference":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionCharge":{"type":"number","format":"double"},"transactionChargeCap":{"type":"number","format":"double"},"transactionChargeValue":{"type":"number","format":"double"},"transactionDate":{"type":"string","format":"date-time"},"transactionChannel":{"type":"string","nullable":true},"settledPerChannel":{"type":"boolean"},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"amountPaid":{"type":"number","format":"double"},"amountToSettle":{"type":"number","format":"double"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"merchantId":{"type":"string","format":"uuid"},"merchantName":{"type":"string","nullable":true},"settlementMode":{"type":"string","nullable":true},"settlementProfileSplitCode":{"type":"string","nullable":true},"isBatch":{"type":"boolean"},"autoSettlement":{"type":"boolean"},"autoSettlementCharge":{"type":"number","format":"double"},"retryCount":{"type":"integer","format":"int32"},"batchReference":{"type":"string","nullable":true},"merchantUniqueBatchReference":{"type":"string","nullable":true},"splitReference":{"type":"string","nullable":true},"settlementInProgress":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementAccount":{"type":"object","properties":{"settlementAccountId":{"type":"string","format":"uuid"},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"code":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"merchantId":{"type":"string","format":"uuid"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementProfile":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetSettlementAccountResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetMerchantSettlementResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"settlementLogId":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"transactionReference":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionCharge":{"type":"number","format":"double"},"transactionChargeCap":{"type":"number","format":"double"},"transactionChargeValue":{"type":"number","format":"double"},"transactionDate":{"type":"string","format":"date-time"},"transactionChannel":{"type":"string","nullable":true},"settledPerChannel":{"type":"boolean"},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"amountPaid":{"type":"number","format":"double"},"amountToSettle":{"type":"number","format":"double"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"merchantId":{"type":"string","format":"uuid"},"merchantName":{"type":"string","nullable":true},"settlementMode":{"type":"string","nullable":true},"settlementProfileSplitCode":{"type":"string","nullable":true},"isBatch":{"type":"boolean"},"autoSettlement":{"type":"boolean"},"autoSettlementCharge":{"type":"number","format":"double"},"retryCount":{"type":"integer","format":"int32"},"batchReference":{"type":"string","nullable":true},"merchantUniqueBatchReference":{"type":"string","nullable":true},"splitReference":{"type":"string","nullable":true},"settlementInProgress":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementAccount":{"type":"object","properties":{"settlementAccountId":{"type":"string","format":"uuid"},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"code":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"merchantId":{"type":"string","format":"uuid"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementProfile":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetSettlementAccountResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetMerchantSettlementResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      