---
id: create-connector-transactionstatusrequery
title: "Create connector connector transaction status requery"
description: "Create connector connector transaction status requery"
sidebar_label: "Create connector connector transaction status requery"
hide_title: true
hide_table_of_contents: true
api: eJztWE2P0zAQ/SvVnPhwt8Axt2VZLXsAqnY5VRWaJtPWkNjGnhSqKP8djd2mWXaFWoSQVmovdezx88zzjOOXBqwjj6ytuS0gg9wTMl1ZYyhn6+88moC5DAdGroOn7zX5LShgXAXIZtDZwlyBQ48VMXkZasBgRQLamSjQBjJwyGtQIGDaUwEZ+5oUhHxNFULWAG+dTAzstVlB26oO6+b6w/Ty8937PdSasCB/Etg8GVPgt7bYikVuDZNhaaJzpc4jIaOvwRrpewBlF18pZ1DgvNDHmkIcPbAlZP6+sgJTlyUuSko+tq0CLAot9liOe1BLLAMpYM1iC71dmMZdmKRdmKQohB9g+slPyuE+0S9ePh3PBcpTcNaE5MqbV6/kr6CQe+1kJmQwrfOcQgDVz624R65EfVKoYQd1MFxYWxIaaAW9oCPCV1BRCLg6zrZAPsKvPQlXx7rQn/COltroRNYRUwOFcOxWq5OTQ0Gwtc/p1gTWXMu8o4MqkGlsA9PJrt1F2/+QuYl1iDT6jc5p7O1Gy6l5lMe6oilj5R6zXlpfIUMWaRiKKfwzhz/ejr/s2+/RFCX5BwfHqcfGuZbOtXSupUMt/dWt4VxE5yI6F9G+iOKF8P7l7yqKqEEne3qtHt2DpKgGB0lVEa+tqDBnQ0xvkUkZjNDpUQcxarpmO+rBDRPc8AAn5O6VWO1LEUvMLmSjUSDmkioyPCxoc7GiKjjcBlvGVLvIbQWikuRgmByU0vVPrFxJj1y/O20lomxpo8WO1JuEPZh2Sw4ux7egQFxLdG1ey0SJucJYfTup97c03tuMLkV6l+9WJT6aHcMzQKfTVb3TqVn/4Q80zxWsZbeyGTTNAgN99mXbSncaz2ZzBRv0OuXwrIFCB2kXXf795m8nGODZZKdpnw/Eicfi2HWikcA3WNbyBAq+0fae8G7nrdpr5X/uRFqsp8w7R0S4p9GrBDiMh8zB4sFl7jDjMs/Jcc+2v+i8Vy3jT9M7ULDYifkqnpTg8Yd8FcAfiRAbQ0vvCelroESzquMbCNLK8vsFx8IXPg==
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create connector connector transaction status requery"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/connector/{connector}/transaction-status-requery"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create connector connector transaction status requery

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"connector","in":"path","required":true,"schema":{"type":"string"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"transactionId":{"type":"string","nullable":true}},"additionalProperties":false,"title":"TransactionStatusRequeryRequest"}},"text/json":{"schema":{"type":"object","properties":{"transactionId":{"type":"string","nullable":true}},"additionalProperties":false,"title":"TransactionStatusRequeryRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"transactionId":{"type":"string","nullable":true}},"additionalProperties":false,"title":"TransactionStatusRequeryRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"responseCode":{"type":"string","nullable":true},"responseCodeDefinition":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"sourceInstitutionCode":{"type":"string","nullable":true},"datePosted":{"type":"string","nullable":true},"transactionType":{"type":"string","nullable":true}},"additionalProperties":false,"title":"TransactionStatusRequeryResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"TransactionStatusRequeryResponseNIP_ResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"responseCode":{"type":"string","nullable":true},"responseCodeDefinition":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"sourceInstitutionCode":{"type":"string","nullable":true},"datePosted":{"type":"string","nullable":true},"transactionType":{"type":"string","nullable":true}},"additionalProperties":false,"title":"TransactionStatusRequeryResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"TransactionStatusRequeryResponseNIP_ResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"code":{"type":"string","nullable":true},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"responseCode":{"type":"string","nullable":true},"responseCodeDefinition":{"type":"string","nullable":true},"sessionId":{"type":"string","nullable":true},"transactionId":{"type":"string","nullable":true},"sourceInstitutionCode":{"type":"string","nullable":true},"datePosted":{"type":"string","nullable":true},"transactionType":{"type":"string","nullable":true}},"additionalProperties":false,"title":"TransactionStatusRequeryResponse"},"serviceProvider":{"type":"string","nullable":true},"timeStamp":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"TransactionStatusRequeryResponseNIP_ResponseHandler"}}}}}}
>
  
</StatusCodes>


      