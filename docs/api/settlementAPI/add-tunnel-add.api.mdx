---
id: add-tunnel-add
title: "Add tunnel add"
description: "Add tunnel add"
sidebar_label: "Add tunnel add"
hide_title: true
hide_table_of_contents: true
api: eJztWE2P2zYQ/SvGnPrBjdMedXM2wSaHoovdzcnwYSyObaYUyZAjJ4ah/16MqLXktdt4kxYtEPkiWxwNZx4fn8y3Bx8oIhvv3mkoALV+qJ0jO9MaFDCuExRzuCdmSxU5zqOwUBAwYkVMUSL24LAiKODmzW/3s/cPb0GBcVDAhlBTBAWRPtYmkoaCY00KUrmhCqHYA++CPJk4GreGplnkYEr8yuudRJTeMTmWrxiCNWVb8PRD8k7unaTyyw9UMigIUdpjQ0lGc4lPJ1TgamtxaSmX1ijQlMpogkxyUXyy9fqiQCzZbIc1LL23hA4aBeUGBdo0GMUYcSdIMlXpy/11GS6qJGBkR/Ha60sgkdK1NgII2tvBpCu0iRSwYYmFmdbXuYinjLnLKyqNfn3uv0zaKGD6zCMjRkb0jBhKxU8/j9QYqdFRQ7JESsG7lLH49eVLuRytJNzXZUkpgRq+flqVCRbNs7iUulRnl7KilHB9IdeQL5jO6HPJVj5WyFBAXRvdQvv/ZngkZNIz/tteNDJdsamoRdJrszLPe+bbN9KFaP97+02BSa9phbXl81CaNPuvgb50394Qn2jCXbdVv0EObohP5eAx6z+Q5PH6Fp22FE9eP899+YyCMQrGKBjfmWB81QlmVIpRKUal+K6Uoj2/HJ9VZlpPuH1sgq1xVxFvvBh6waeWV8gbKGCKwUxz4DQHJorbRwuvjlYcO+aQiuk0HQq60rR9saYqBdwlb2uZ9EXpKxCrTgTrrrfr3nzGKljqT9D94h2XfLidt3T/+3HnZpuw30TzwZm1jz7i88FDXAg13cq3HDpg3DYw6YGezG7fgQLpP9e0/UWIIJBV6AYtnMB7XrYGh8NGZTT3HfJzwGDEUM32aUsLMVE3sjzFHPb7JSZ6H23TyO2PNcUdFPOFgi1Gk2k5l646P1XQ0CbJgD6w6UlVh2Mr/HDXma8/TqSGc9X+Qbtj93aLtpYwaNS+G73OCa8eJEEfcfJHt39iVpYUeBA7nHQxoOnt7/cPoGDZGb5VXs6In8Q5xk9ShwLfttYqY3tvDxbdum7fcJBnls+fBDAA4Q==
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Add tunnel add"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/tunnel/add"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Add tunnel add

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"channels":{"type":"array","items":{"type":"object","properties":{"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddChannelSettlementTunnelRequest"},"nullable":true}},"additionalProperties":false,"title":"AddSettlementTunnelRequest"}},"text/json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"channels":{"type":"array","items":{"type":"object","properties":{"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddChannelSettlementTunnelRequest"},"nullable":true}},"additionalProperties":false,"title":"AddSettlementTunnelRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"channels":{"type":"array","items":{"type":"object","properties":{"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddChannelSettlementTunnelRequest"},"nullable":true}},"additionalProperties":false,"title":"AddSettlementTunnelRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"name":{"type":"string","nullable":true},"description":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"active":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"channels":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"channel":{"type":"string","nullable":true},"partnerCode":{"type":"string","nullable":true},"isDefault":{"type":"boolean"},"isActive":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetChannelSettlementResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementTunnelResponse"}},"additionalProperties":false,"title":"GetSettlementTunnelResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      