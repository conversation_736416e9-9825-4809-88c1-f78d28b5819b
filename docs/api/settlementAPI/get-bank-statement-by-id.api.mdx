---
id: get-bank-statement-by-id
title: "Get bank statement by ID"
description: "Get bank statement by ID"
sidebar_label: "Get bank statement by ID"
hide_title: true
hide_table_of_contents: true
api: eJztWE2P2zYQ/SvGnFqAG2/SXqrbbjbZLNACwdo5LXwYk2ObWYpUyJFRQ9B/L0ayZVl1C7vNIQedbIucx/l4fDJeBaGgiGyDfzKQwZr4Hv3rjJEpJ8/3uycDChjXCbIXkLVn0sFr62wTBQsFBUbMiSnKngo85gQZpANGg2A9ZFAgb0BBpG+ljWQg41iSgqQ3lCNkFfCuaEOj9WtQsAoxR4YMytIaqGvVoT9++GN292X+6QC9ITQUrwCv64VsTkXwiZKsv7u9lQ9DSUdbNMVlMCu1ppRAgQ6eyXODRH/ytHAoJ1d/PyEsv5JmUFBE6S7bFj/toY4blyE4Qg+1gpxSwjWda4IvncOlo7aiWoFBvuA4ay7oqLRgRZG8vuzoZZ8cH/dYFZAvc+GHD55AQc6//XorzBjisWUBg/szMLUSLnrr1/fo8DQhX+bLZrpd+iaUkletQLuQrg5KjJEfkM9WfdyPTDds8yaEvLkugCP6hFqI9D6Ue+a0kdYzrU9zs55/eTcI63MFY8SdsJ0pT99t+t0l/d16eiBG69JFROhl+Uw5xteLojAfNOLfZtQ7Yt7sPvJMRzJWija0tHwx0+YDxIb+rZqROX8vj+t3fPnobZpR3FpN7zcYT251D1pHQr4ONw/Gruw1MdJ0Y6yUjO5zjyIrdImOfXocCP+8P99WJCWBwUT/K/oR8v8iHD4/oTeOYgtYFM7q5vU0/ZrCKNKjSI8iPYr0KNI/kkg3/6FHdR7VeVTnUZ1Hdf6h1LkWzFMf5JF4Iso16eg+We4mTw8iVcSbsDeP5BKJyZPBFAs7lYibeGIYtc86lDStekZRDQoSxe3BSiqjE2+HuUjZdJqI2TU7bwxt36wpTwXuUnClAL/RIQcxdaxfhWa2Xd3NvsmsC5/cfX4CBXJMW932rUymCIlzbF5IB5fpn6s+aU9HpJ4zVKs2/WrfkRfAwkKr/4OeHJ4euwIKsr6BtlCwCYkFpaqWmOhLdHUtj7+VFHeQvSwUbDHallkvFRib5LvpaDBIuLOz4KfnvWX28wTU+UIOAupFPrfoSvkFCl5pN3D66kWtDmbcd0+jPa5n/XWpiDPYrt5pTQX31voQix5bHz/Moa7/AnRZUww=
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get bank statement by ID"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/bank-reconciliation/bank-statements/{statementId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get bank statement by ID

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"statementId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"reference":{"type":"string","nullable":true},"bankStatementFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"openingBalance":{"type":"number","format":"double"},"closingBalance":{"type":"number","format":"double"},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"},"transactionCount":{"type":"integer","format":"int32"},"transactions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponse"}},"additionalProperties":false,"title":"GetBankStatementResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"reference":{"type":"string","nullable":true},"bankStatementFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"openingBalance":{"type":"number","format":"double"},"closingBalance":{"type":"number","format":"double"},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"},"transactionCount":{"type":"integer","format":"int32"},"transactions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponse"}},"additionalProperties":false,"title":"GetBankStatementResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"reference":{"type":"string","nullable":true},"bankStatementFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"openingBalance":{"type":"number","format":"double"},"closingBalance":{"type":"number","format":"double"},"startDate":{"type":"string","format":"date-time"},"endDate":{"type":"string","format":"date-time"},"transactionCount":{"type":"integer","format":"int32"},"transactions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"statementLineDetails":{"type":"string","nullable":true},"transactionRemark":{"type":"string","nullable":true},"amount":{"type":"number","format":"double"},"transactionType":{"enum":["credit","debit"],"type":"string","title":"BankStatementTransactionType"},"reconciled":{"type":"boolean"},"reconciledAt":{"type":"string","format":"date-time"},"isServiceCharge":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetBankStatementTransactionResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBankStatementResponse"}},"additionalProperties":false,"title":"GetBankStatementResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      