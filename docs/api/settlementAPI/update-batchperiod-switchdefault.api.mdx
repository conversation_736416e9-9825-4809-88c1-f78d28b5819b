---
id: update-batchperiod-switchdefault
title: "Update BatchPeriod SwitchDefault"
description: "Update BatchPeriod SwitchDefault"
sidebar_label: "Update BatchPeriod SwitchDefault"
hide_title: true
hide_table_of_contents: true
api: eJztWE1zGjkQ/StUnza7IiR2TLLcsOOKc9gNZeOTi0Mz6gFlNZIi9eBQ1Pz3VM9gGGMqi5PKIVVwGUrqz6fWm+legQ8UkY13HzUMoAwamc6Rs3mgaLy+uTeczTXlWFoGBYyzBIM7qEVGtQhMFASMWBBTlM0VOCwIBvDh8p+b4e34ChQYBwOYE2qKoCDSl9JE0jDgWJKClM2pQBisgJdBNBNH42ZQVZNGmBKfe70Uicw7JsfyF0OwJquj731O3snaE1N++pkyCT1EyZUNJdmdbhOQzHc9K8h9LJAFk9JoqCoFqLURX2hHLVM52kQK2LAV/QawFjxj/75B77rJozbF9JV/s5DbYP/51+8UuxiLlIJ3qQnm5NUreWhKWTRBfIilMssoJVDtCqvPKVg0z0o2rU1tBafeW0IHlYKCUsIZ7cPAldbiVPKSa1Ep0Mi/AFvV1rjw+sBY1hywN6k8+mJsisMssT9YtCGhK1/GlrhxTLOaRzaJGcenJxJIFgmZ9JC/i4OQXJclCDkQr01ufkznfHlQGlvxywKNPUgnEbOlghyPos+NpXY9YYy4FFplKtL/V0hBMZuj4/MyGUcp/YsH4v+g+MlNPUZt3Gx/hT1RxJL9zSaDfUVz6A3/QHyzC8X1+jbLUey4fobZFmtsDf6c/sPzCp22FJ8Q53Np88gkRyY5MsmRSeKPfzUeKeRIIUcKOVJIrDuh+u60257buuPvtNQ7TU/1ftPzF8RzL9OBIEJynshzGEAPg+m1FHu7ioni4mEkUEYrEwDmkAa93raeupoWL2dUpIDL5G0pYb3MfAHS+gvXXW/b/8uvWARLe2gGTnN8d5b333TP3r5+231z1j/pTk/zrHuS/d0/zft9zLEvB2Rc7usC2CBXe+5sT7UzHH0EBRJ4g9DitSgGn7jAmnnX040DkHsE9abqWk1lpRpgVmtM7wCDAfVovKLgsdmJgrlPLMKr1RQT3UZbVbL8paS4hMHdRMECo2kK8W5SqYfJixyENkk29KaIdoLcdL/wx/V6TPOiI5OffcH/R8vHc54F2lLEoFKr9e5FY7A7FgNbiSefxVuNYZZR4JZs2+mkVZCj4fhCvE7Xs6Gifn9AxHsZMuG9BKLA17nV3FOvrcCim5X1ew8a1/L7BtQosyQ=
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update BatchPeriod SwitchDefault"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/BatchPeriod/SwitchDefault"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update BatchPeriod SwitchDefault

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"SwitchBatchPeriodToDefaultRequest"}},"text/json":{"schema":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"SwitchBatchPeriodToDefaultRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"}},"additionalProperties":false,"title":"SwitchBatchPeriodToDefaultRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"}},"additionalProperties":false,"title":"GetBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      