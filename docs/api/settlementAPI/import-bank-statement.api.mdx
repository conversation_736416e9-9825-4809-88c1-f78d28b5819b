---
id: import-bank-statement
title: "Import bank statement for reconciliation"
description: "Import bank statement for reconciliation"
sidebar_label: "Import bank statement for reconciliation"
hide_title: true
hide_table_of_contents: true
api: eJzdVU1v2zAM/SsBTxug1N2wy3xru34dhhVNewpyUCwmUStLqkQHCwz994G2k9hp1q3XnhKY71F8TxRZg/MYJGlnbxXkoEvvAp1L+zwhSViiJRBAchkhnwJ/v8fC2UIb3ZBgJsDLIEskDIypwcoSIYfry5+Ts8eHGxCgLeSwQqkwgICAL5UOqCCnUKGAWKywlJDXQBvPzEhB2yWkNGvBGOncqQ0jCmeJS8prKCtD2stA2cKFcqwkNTleZXPzJyxYhA8slTRGju7kXWmDrw8XwFklQQ5zbWXYQBJDzlUXrwFtVbI71lkEASV9/3bKvhxmJE18FAzc7dIkARfOGCzY1LOicJUlvpA36qoqrbqqAv2Q9LYKJQnHpEtkyqVV7yGkJABt4RRDjnkXadMoY/IbRh3C/qL4SLa9wMNgT8owlJqyA0bvbGzv/OvpKf8ojEXQvmnfHCZVUWCMIPrNRfibMm8kd+7/91TsUu2Bc+cMSsuFlhijXB713FbGyDnXzi8iCdg28z+ASYBUSrMQae56pSykibhvuEnDv++suJFWGQzQ0L03umhecvYU3QdX29zqR5fZtP2wxW+boT6aS/s8itunOVq4MArDYc6CaOV4EXgXGy8krSCHTHqdMX88ZGTtvhjH3rKIGNbbXVAFw6OfyMc8yyISmQY2Vrg+WWIZvdxEZyrOdVK4Enjma7twjVGd0usWN5rs6KOzu1sQwMe0Ctdf+Da45lI2t9stoXcoH1i2u6XeIEiilVN3pkxBeg0CjtjCO+/QmJmAFXuaT6Gu5zLiYzAp8eeXCsMG8ulMwFoG3fbCdJbEdmeyk0pHDqhdHxzUuxte8Om+W7CfRyCO63jGzXBDr6WpGAZJ1F30ok04fuAEe8SxtbsnnRUFeurB++fOeu1192vywN51e710aju5m4xi/5edSOkPyNwGJA==
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Import bank statement for reconciliation"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/bank-reconciliation/import-statement"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Import bank statement for reconciliation

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"multipart/form-data":{"schema":{"type":"object","properties":{"StatementFile":{"type":"string","format":"binary"},"StatementFileFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"CollectionAccountId":{"type":"string","format":"uuid"},"StartDate":{"type":"string","format":"date-time"},"EndDate":{"type":"string","format":"date-time"}}},"encoding":{"StatementFile":{"style":"form"},"StatementFileFormat":{"style":"form"},"CollectionAccountId":{"style":"form"},"StartDate":{"style":"form"},"EndDate":{"style":"form"}}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}}}}}}
>
  
</StatusCodes>


      