---
id: import-bank-statement
title: "Import bank statement for reconciliation"
description: "Import bank statement for reconciliation"
sidebar_label: "Import bank statement for reconciliation"
hide_title: true
hide_table_of_contents: true
api: eJzdVUtv2zAM/isBTxug1N2wy3xru74Ow4qmPQU5MBaTqJUlVaKDBYb/+0DHeTjNuvXakwXzIX4fKX41+EAR2Xh3qyEHUwYf+Rzd84iRqSTHoIBxniAfg/y/p8K7wljTBsFEQcCIJTFF8anBYUmQw/Xlz9HZ48MNKDAOclgQaoqgINJLZSJpyDlWpCAVCyoR8hp4FSQycTRuDk0zWTtT4nOvV+JReMdSUl5DWVk2ASNnMx/LoUZuc7zK5qdPVAiIEAUqG0pi3cK7MpZeX65AsiJDDlPjMK6gUf2Yq85eA7mqFHacdwQKSv7+7VR4OczIhuUq6LHbpWkUXHhrqRBSz4rCV46lIW/UVVVGd1VF/oH8NgqNTEM2JUnIpdPvCWgaBeQKr8XlGHeJVy0yCX6DqEO3vyA+km0H8NC4B6VvatqyI6XgXVr3/OvpqXw0pSKa0I5vDqOqKCglUPvDxfSbs2BRJvf/Zyp1qXaOU+8toZNCS0oJ50c5d5W1OJXa5UU0CjbD/A/HRgFqbQQI2ru9UmZoE+0GbtTG33dU3KDTliK04SFYU7QvOXtK/oOjbbv60WG2Y98f8dt2qQ+m6J4HafM0BzMfB7G/zAUQL7wIQfCp5QJ5ATlkGEwm8cN+RLbWi2HaE4tEcbnRgipaWf3MIeVZNqcyVWEeUdOJnAvrK32CIYCseuNmvuWnA3hNZQq4GoyI2a5LPru7BQWSfQ1s+UWaIKWW2Da10553AO4xtW3O3vtv1BpF3XExBgwGFBxhQ6TukI+JgoVQmY+hrqeY6DHappHfLxXFFeTjiYIlRrMegfGkURupFAK1SWLQ2/Yf1LvdWfDpvtPVzwNQx3E806ovzEu0lbhBo+rOerFOOHyQBDuPY2q7CzorCgq8575/72Rvqu5+jR6Eu07OS683C7vNqHZHYaJp/gAVLQJZ
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Import bank statement for reconciliation"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/bank-reconciliation/import-statement"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Import bank statement for reconciliation

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"multipart/form-data":{"schema":{"type":"object","properties":{"StatementFile":{"type":"string","format":"binary"},"StatementFileFormat":{"enum":["none","mt940"],"type":"string","title":"BankStatementFormat"},"CollectionAccountId":{"type":"string","format":"uuid"},"StartDate":{"type":"string","format":"date-time"},"EndDate":{"type":"string","format":"date-time"}}},"encoding":{"StatementFile":{"style":"form"},"StatementFileFormat":{"style":"form"},"CollectionAccountId":{"style":"form"},"StartDate":{"style":"form"},"EndDate":{"style":"form"}}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"string","nullable":true}},"additionalProperties":false,"title":"StringResponseHandler"}}}}}}
>
  
</StatusCodes>


      