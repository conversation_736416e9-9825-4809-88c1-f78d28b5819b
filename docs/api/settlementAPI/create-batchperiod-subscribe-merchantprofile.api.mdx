---
id: create-batchperiod-subscribe-merchantprofile
title: "Create BatchPeriod Subscribe batchPeriodId MerchantProfile settlementProfileId"
description: "Create BatchPeriod Subscribe batchPeriodId MerchantProfile settlementProfileId"
sidebar_label: "Create Batch<PERSON>eriod Subscribe batchPeriodId MerchantProfile settlementProfileId"
hide_title: true
hide_table_of_contents: true
api: eJztWE2v0zoQ/SvVrEAyhI9ddvcixL0LoKIXsai6cONpa3BsY08qqsj/HU3S5qZt3qPA5ekhZZW2Hs+ZOR6fVKcG5zFI0s7eKsihCCgJryUVG49BOzWrlrEIeolvMRQbackHt9IGQQDJdYR8Dk30tImGhQAvgyyRMPBiDVaWCDks74NuFQjQFnLwkjYgIODXSgdUkFOoUEAsNlhKyGugnefNkYK2axCwcqGUBDlUlVaQkujyRyQyWKKlaVvgH0F58/rt7Orj3c0h9QalwvATyVNacHD0zkaMvP7i2TN+KGSaPR8E5DCrigJjBAGFs4SWmkz4jTJvJCPX5whu+RkLAgE+8JmSbvPHfar7wKVzBqWFJKDEGOUah0iwlTFyabDtKAlQki6BGziHH1PMlbTjdV1FbTHGdw3hF5R12PjeLp0MStv1MOLZRlmRm3XVDvPTm9o+gzIEueMZICzjjzk5nv1L2OjteOXUheeDK1mZf2hkFVx5py+klNzFoa1I3Lgq9MK1JVw316JrTFt6+YILaQVGXdG/8qAk4RPiIviEndIr/Wt7rneXDVEX/rqU2ly052zSf2NC/o/Tz0FKaVYkaaa9clfSRBRAmkyjiUizUyo+7BWOj+IE+ifS9l4sD5PwrM5PmjaDKH8g6eF5I60yGFoM740umtdv9jm6UdhHYR+FfRT2Udj/dmFv/quPij4q+qjoo6KPiv7XK3pKzbXtezSvmusx6W2fdGbZ5EhHJgfvbI86GTarSqSNYxPOu9jMHftWOWTS66yHknUoWX0Ek7ITnKweAErA9yFsD/5cFQwbWUQ+5ll2v+GJwu3TNZbRy110puKunxauBHawtF25ZhA7bpu4yT3Bk6vpLQhgmJau7XMeCG6tlM1bcW+pPTiNR6fU3ZWeeZZE23S9Z3gO0msQRz6mgK4IEJCfupcnlXDIUDELARs+y3wOdb2UET8GkxL//LXCsIN8vhCwlUG3d2Reg9KRP6tuek+66exAePRhbzk+nrAXO9TlQeAsy9tWmoq/gYAvuDtzZNnk/A/Bh9hKiyQOjuqDc9HC9vzbriTuvF29Kgr01Fvrp1j0Luj0/ewOUvoOHbo6EA==
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create BatchPeriod Subscribe batchPeriodId MerchantProfile settlementProfileId"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/BatchPeriod/Subscribe/{batchPeriodId}/MerchantProfile/{settlementProfileId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create BatchPeriod Subscribe batchPeriodId MerchantProfile settlementProfileId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"batchPeriodId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"settlementProfileId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"batchPeriods":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponse"}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"batchPeriods":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponse"}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"batchPeriods":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponse"}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      