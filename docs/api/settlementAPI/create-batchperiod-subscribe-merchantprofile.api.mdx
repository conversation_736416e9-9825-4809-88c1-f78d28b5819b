---
id: create-batchperiod-subscribe-merchantprofile
title: "Create BatchPeriod Subscribe batchPeriodId MerchantProfile settlementProfileId"
description: "Create BatchPeriod Subscribe batchPeriodId MerchantProfile settlementProfileId"
sidebar_label: "Create Batch<PERSON>eriod Subscribe batchPeriodId MerchantProfile settlementProfileId"
hide_title: true
hide_table_of_contents: true
api: eJztWMGO2zYQ/RVjTi3ARml60203CLp7SGPEG/Rg+DAWxzZTimTI0aKGoH8vRrK1sq00TrspGkAny+Zw3szj8Ml4NfhAEdl4d68hhyISMt0iF7tA0Xi9qNapiGZNbykWO3Qcot8YS6CAcZsgX0IbPW+jYaUgYMSSmKIs1uCwJMhh/RR0r0GBcZBDQN6BgkifKhNJQ86xIgWp2FGJkNfA+yCbE0fjtqBg42OJDDlUldHQNKrPn4jZUkmO512B3wTl1zdvFzcfHu6OqXeEmuJXJG+alQSn4F2iJOuvXr6UD01Cc5CDgBwWVVFQSqCg8I7JcZuJ/uQsWBTk+hLBrz9SwaAgRDlTNl3+dEj1FLj23hI6aBSUlBJuaYwEV1mLa0tdR40CjXwN3Mg5fJliqaQbr9sqGUcp/dYSfkVZx43v3Npj1MZtxxEvNmLFftFXO87PYGqHDGKMuJcZYCrTlzk5nf1r2BjseO31ledDG6zsZxrZRF8+mCspZX91aCcSd76Kg3DjmLbttegbM45/eSWFdAKjb/hvedDI9BNLEXLCXpuN+Wd7bvfXDVEf/qZEY6/aczHp/2JC/o/TL0FaG1EktPNBuRu0iRSwYdtqIvHinIr3B4WToziD/oq0gxfL8yS8qPN3w7tRlG+Q9Ph5h05bih1GCNYU7es3+5j8JOyTsE/CPgn7JOzfu7C3/9UnRZ8UfVL0SdEnRf/uFb1p2ms79Ghet9djNtg+682y2YmOzI7e2QF1Nm5WlcQ7LyZc8KmdO/GtcsgwmGyAkvUoWX0C02RnOFk9AtSA3If4ePTnqmjFyGIOKc+yLZWpCtuIml7Ic2F9pV9gCCDGlXEb385fT2mZAu5nT7zObub3oECydyw9/ixzIB2V2L4MD07as7N3cjj9FRl4Zo3qeq0PxC4BgwF1Yl8q6IsABfm5aXlWiYSMFbNSsJMjzJdQ12tM9CHappGfP1UU95AvVwoeMZruaixr0CbJs+6H9qyb3gWEH94fnMYfZ2LBjnV51DUnqvaItpJvoOAP2l8YseJt/ofgY2w1q0YdjdRn56KDHdi2fUnSebd6UxQUeLA2TLEa3Mv5u8UDNM1f40g2RQ==
sidebar_class_name: "post api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Create BatchPeriod Subscribe batchPeriodId MerchantProfile settlementProfileId"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/api/BatchPeriod/Subscribe/{batchPeriodId}/MerchantProfile/{settlementProfileId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Create BatchPeriod Subscribe batchPeriodId MerchantProfile settlementProfileId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"batchPeriodId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"settlementProfileId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"batchPeriods":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponse"}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"batchPeriods":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponse"}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"},"batchPeriods":{"type":"array","items":{"type":"object","properties":{"batchPeriodId":{"type":"string","format":"uuid"},"batchPeriodCode":{"type":"string","nullable":true},"default":{"type":"boolean"},"fromTime":{"type":"string","nullable":true},"toTime":{"type":"string","nullable":true},"periodHour":{"type":"integer","format":"int32"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"modifiedBy":{"type":"string","nullable":true},"modifiedByEmail":{"type":"string","nullable":true},"settlementProfiles":{"type":"array","items":{"type":"object","properties":{"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetSettlementProfileResponse"},"nullable":true}},"additionalProperties":false,"title":"GetBatchPeriodResponse"},"nullable":true}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponse"}},"additionalProperties":false,"title":"GetSettlementProfileWithBatchPeriodResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      