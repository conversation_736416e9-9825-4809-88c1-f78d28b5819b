---
id: update-servicesetups
title: "Update service setups id"
description: "Update service setups id"
sidebar_label: "Update service setups id"
hide_title: true
hide_table_of_contents: true
api: eJztV01v2zAM/SsBT/tQm25H37KiaHvYUDTtKciBsZhEnWypEt0uMPzfB1pu4ixDkezjUiSXGDRJkU9P1mMNzlNANq681pBB5TUyjSk8mZwiceUjKGBcRMgm0NnHYoepAo8BC2IK8raGEguCDIwGBaaEDDzyEhQEeqxMIA0Zh4oUxHxJBUJWA6+8REQOplyAgrkLBbLUURkNTaPWSS8vvo5H93dXL6mXhJrCAcmbZpqcKfIXp1fikbuSqWR5RO+tyVsghg/RlWLbSeVmD5QzKPBBYGNDUd5qE73F1be20t2myspanFlKFTYKntBW+3g2ClBrIzWhvektOUcbSQEbFl8YaX3f37d2f25TqwIiMP3gt9dVf8s+fHxj7UmeQNG7MqZ6P5+dtWVTzIPxkh4yGFd5TlGOaI/K7W57i+YgPGKXauM4c84SltAoKChGXOwHlEbeYzmj9zj/6r/tkoJoq8VejnkgZNIjfrVg2ckTNgW1cDlt5uaQmH1Zc0m8zZfEkL9O8PJ/haW2FHbO16Gn68imI5u22fRHd9CRRkca9WnUXovbV2C6RAcxxQ+SZh20GrQgXjqddGguQrTVoxkM0ZthF3CSAoa10Q0oEOuLnK2CFaHJ7GM2HC6oiJVfBNR0Ks+5dZU+Re9BhKUQ+3YjLi9+YOEt7eiMDd4dJ9bqVGTt3LU7swaliB5XgzExWyqo5MHo5lpCKcTU+9MnCfQucoHtuerE8iuYbIG3pkFPMjQqNV53aE0AvemQ2eAFCjKjZQZYusjiVdczjHQfbNOI+bGisIJsMpVWg0nknbRwyLNec+CXgtY6Bt7ddsr+/UBGkN8V2hmxXPUQBQXfaZXmkGbaqJdR4Z+vnlbpDSbrCmRuSW/PU8KTO0mw8di5WjcRozwnzz3f/qLTHqlvRnfnsuqsG2YKp8U94LNMRficoHBtb+3XrbXVYLFcVO13E9LS8vsJgHPnpA==
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update service setups id"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/service-setups/{id}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update service setups id

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"id","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddUpdateServiceSetupRequest"}},"text/json":{"schema":{"type":"object","properties":{"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddUpdateServiceSetupRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddUpdateServiceSetupRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceSetupResponse"}},"additionalProperties":false,"title":"GetServiceSetupResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceSetupResponse"}},"additionalProperties":false,"title":"GetServiceSetupResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceSetupResponse"}},"additionalProperties":false,"title":"GetServiceSetupResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      