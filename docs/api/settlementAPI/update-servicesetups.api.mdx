---
id: update-servicesetups
title: "Update service setups id"
description: "Update service setups id"
sidebar_label: "Update service setups id"
hide_title: true
hide_table_of_contents: true
api: eJztV0tPGzEQ/ivRnPowhPa4txQh4NAKETihHCbrSWLqXRt7NjSK9r9XYy/JpqlQ6OOCyCWr2Xn58+f1N2twngKycfWlhgIar5FpTGFpSorEjY+ggHEeobiDzj4WO0wUeAxYEVOQt2uosSIowGhQYGoowCMvQEGgh8YE0lBwaEhBLBdUIRRr4JWXiMjB1HNQMHOhQpY+GqOhbdUm6fnZ1/Ho9ubiKfWCUFN4QfK2nWRnivzF6ZV4lK5mqlke0XtrygTE8D66Wmx7qdz0nkoGBT4IbGwoylttore4+pY63V9U3ViLU0u5w1bBEm1ziGerALU20hPaq17JGdpICtiw+MJI69v+vqX9uc5LFRCB6Qe/vlX1t+zDx1e2PMkTKHpXx9zv55OT1DbFMhgv6aGAcVOWFOWI9qicdttbNC/CI3apto5T5yxhDa2CimLE+WFAaeQDyhl9wPlX/22XFETbzA9yLAMhkx7xsw3LTh6xqSjB5bSZmZfEHMqac+JdvmSG/HWCp/8LrLWlsHe+Xnq63tj0xqZdNv3RHfRGozca9WmUrsXdKzBfooOY4wdZsw6SBq2IF05nHVqKEE16tIAhejPsAo5ywHBtdAsKxPokZ5tgRWgy+1gMh5GYLVVU85Gm5fGcquhxFZ1tpJHj0lUgClMYfr1VmWc/sPKW9gTHFviOHBuZKvp25tIWbdBJpQbjTQeD0dWlhFKIGYTlJwn0LnKF6YB1qvkZcHZQ3PChpx1alRFYd7DdAXrTQbQFDhQURsswsHCRxWu9nmKk22DbVswPDYUVFHcTWWowmcV3CQ551hsy/NLQRtDAu+tO4r8fyCzyu0Y7I9arHqKg4Dut8kDSTlr1NDP88+q5Sm9C2XQgA0x+e5oTHt1Igq3H3h27jRiVJXnu+faLTnrsvhrdnErVaTfVVE6Le8BHGY/wMUPh0trSZy7Z1mCxnjfpAwq5tPx+At+m628=
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update service setups id"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/service-setups/{id}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update service setups id

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"id","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddUpdateServiceSetupRequest"}},"text/json":{"schema":{"type":"object","properties":{"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddUpdateServiceSetupRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true}},"additionalProperties":false,"title":"AddUpdateServiceSetupRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceSetupResponse"}},"additionalProperties":false,"title":"GetServiceSetupResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceSetupResponse"}},"additionalProperties":false,"title":"GetServiceSetupResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"displayName":{"type":"string","nullable":true},"value":{"type":"string","nullable":true},"slug":{"type":"string","nullable":true},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceSetupResponse"}},"additionalProperties":false,"title":"GetServiceSetupResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      