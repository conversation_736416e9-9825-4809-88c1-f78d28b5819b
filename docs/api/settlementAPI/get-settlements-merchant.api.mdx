---
id: get-settlements-merchant
title: "Get settlements merchant merchantId"
description: "Get settlements merchant merchantId"
sidebar_label: "Get settlements merchant merchantId"
hide_title: true
hide_table_of_contents: true
api: eJztWktv2zgQ/ivGnHYBte52b7olaZEGaLpGnO6l8GFMjm22EqmQI6NeQ/99QT0sJVG2lBHsiSfDJr95c8gPniOYgiyyMvpGQgpb4iUxZ5STZndLVuxQMyTAuHWQfoN+FVYJFGgxJybr146gMSdIIW9hNxISUBpSKJB3kIClh1JZkpCyLSkBJ3aUI6RH4EPhkY6t0ltIYGNsjgwplKWSUFXJSfgCt/SlzNdkO+EPJdkDjEhTmmlb7zuJU5r/fP9M3lL9Q68j7frj7fLi6/2nTtqOUNaYUNerauU3u8JoR86vv3/3zn9IcsKqwmcKUliWQpBzkIAwmn02vCT6yfMiQ6/5+FyDWX8n4XNZWJ90Vo1814rqN66NyQg1VAnk5BxuaSxFuswyXGfUeFQlIJGH6tBa9IFUTLkLMONUWJ/N1tfiL4siAbaoHYqueqch7mhDlrQIc24A/DDMxDTo1Q7to2jqrpJPlkpTevAY7gqLc6F/Y1aeofcD8mh4ehQyvWGVj2jVmrKg+DSZlwuyz1GDUnRk90rQwpq9kmS/1AcuSPwj3JWRYTjMTal5gUoGxq0B3JumRQaCnCmtoEvUP4IN6yHhMaghF0J4C89ENU6E4Ab9P+RMdtsnZLRrFbfBMTtBFtZsVEbLIlMcHHLlLpHFbrwwsWQzuBYD9kxqA5bYHq58CoIuowTW3tRp3a1LwVetHkq6nC7A+XBOhJzCcaMX1mzti5eQsIRM8oLDW1FupNqoaZjeoLbkp1xaLSSw5NdTzvt6ykn3m6ee8yHGBCFE+MFZWJWjPbz0vJjUKf73QmibxZRCaCETe99l6ZQm54Jz1gH/0muDVir9wpvp+bX2y27lN0mp/DWO2WLg5gYzRwmwqq83uCbuCMLyqft37Qt2krTl09N0lpTnNvViRuJR9HwirL0WHWEI275R1vHihTd0Xw9WjSUrw/OxbBgzD3aBltaAOxLGylCIpp9n21dY2itTuvPwr1IQn1UTXtn98Am1zMg29VYUmRI1NZ5/dyZyqsipIqeKnCpyqsipIqeKnCpyqsipIqeKnCpyqgmcqv6HKpKpSKYimYpkKpKpSKYimYpkKpKpSKYimYpkKpKpKWSq8hoeDwVeE8/6c+5m3RGcPRrIzIl3pp319G3Cj2amMMdCzQfgeYeZH3t0Bc1rtZv3LG3mZxyZC5fOB+g3kvZvt5S7Ag/OZKW3760wOfjhRqU3TQ8/BaDeN+u9n10sbiABr6ZxbP9HXdfGcY41pemmLYMclqN0aDAsWSWNJ8c2GN8ACwXDNuugb2iQQDqQv0pgZxx71PG4RkdfbVZV/udmktTHSSrnK0Oekv+DDk9HWfcNAQLw06T/AWhnVfvtK//Fqqb0xpQ98f80MAq/3bVDqb/P/ITvWFw6qqwPQ52dPYMwVKsq6aZdX92KRttgtvZRsJrVCyGo4MHaUMRqUPbXH++hqv4FVpzNZA==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get settlements merchant merchantId"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/settlements/merchant/{merchantId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get settlements merchant merchantId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"merchantId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"settlementLogId":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"transactionReference":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionCharge":{"type":"number","format":"double"},"transactionChargeCap":{"type":"number","format":"double"},"transactionChargeValue":{"type":"number","format":"double"},"transactionDate":{"type":"string","format":"date-time"},"transactionChannel":{"type":"string","nullable":true},"settledPerChannel":{"type":"boolean"},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"amountPaid":{"type":"number","format":"double"},"amountToSettle":{"type":"number","format":"double"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"merchantId":{"type":"string","format":"uuid"},"merchantName":{"type":"string","nullable":true},"settlementMode":{"type":"string","nullable":true},"settlementProfileSplitCode":{"type":"string","nullable":true},"isBatch":{"type":"boolean"},"autoSettlement":{"type":"boolean"},"autoSettlementCharge":{"type":"number","format":"double"},"retryCount":{"type":"integer","format":"int32"},"batchReference":{"type":"string","nullable":true},"merchantUniqueBatchReference":{"type":"string","nullable":true},"splitReference":{"type":"string","nullable":true},"settlementInProgress":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementAccount":{"type":"object","properties":{"settlementAccountId":{"type":"string","format":"uuid"},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"code":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"merchantId":{"type":"string","format":"uuid"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementProfile":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetSettlementAccountResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetMerchantSettlementResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"settlementLogId":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"transactionReference":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionCharge":{"type":"number","format":"double"},"transactionChargeCap":{"type":"number","format":"double"},"transactionChargeValue":{"type":"number","format":"double"},"transactionDate":{"type":"string","format":"date-time"},"transactionChannel":{"type":"string","nullable":true},"settledPerChannel":{"type":"boolean"},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"amountPaid":{"type":"number","format":"double"},"amountToSettle":{"type":"number","format":"double"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"merchantId":{"type":"string","format":"uuid"},"merchantName":{"type":"string","nullable":true},"settlementMode":{"type":"string","nullable":true},"settlementProfileSplitCode":{"type":"string","nullable":true},"isBatch":{"type":"boolean"},"autoSettlement":{"type":"boolean"},"autoSettlementCharge":{"type":"number","format":"double"},"retryCount":{"type":"integer","format":"int32"},"batchReference":{"type":"string","nullable":true},"merchantUniqueBatchReference":{"type":"string","nullable":true},"splitReference":{"type":"string","nullable":true},"settlementInProgress":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementAccount":{"type":"object","properties":{"settlementAccountId":{"type":"string","format":"uuid"},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"code":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"merchantId":{"type":"string","format":"uuid"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementProfile":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetSettlementAccountResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetMerchantSettlementResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"settlementLogId":{"type":"string","format":"uuid"},"transactionId":{"type":"string","format":"uuid"},"transactionReference":{"type":"string","nullable":true},"transactionDescription":{"type":"string","nullable":true},"transactionCharge":{"type":"number","format":"double"},"transactionChargeCap":{"type":"number","format":"double"},"transactionChargeValue":{"type":"number","format":"double"},"transactionDate":{"type":"string","format":"date-time"},"transactionChannel":{"type":"string","nullable":true},"settledPerChannel":{"type":"boolean"},"serviceProviderName":{"type":"string","nullable":true},"serviceProviderCode":{"type":"string","nullable":true},"amountPaid":{"type":"number","format":"double"},"amountToSettle":{"type":"number","format":"double"},"sourceBankCode":{"type":"string","nullable":true},"sourceBankName":{"type":"string","nullable":true},"sourceAccountName":{"type":"string","nullable":true},"sourceAccountNumber":{"type":"string","nullable":true},"merchantId":{"type":"string","format":"uuid"},"merchantName":{"type":"string","nullable":true},"settlementMode":{"type":"string","nullable":true},"settlementProfileSplitCode":{"type":"string","nullable":true},"isBatch":{"type":"boolean"},"autoSettlement":{"type":"boolean"},"autoSettlementCharge":{"type":"number","format":"double"},"retryCount":{"type":"integer","format":"int32"},"batchReference":{"type":"string","nullable":true},"merchantUniqueBatchReference":{"type":"string","nullable":true},"splitReference":{"type":"string","nullable":true},"settlementInProgress":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementAccount":{"type":"object","properties":{"settlementAccountId":{"type":"string","format":"uuid"},"bankCode":{"type":"string","nullable":true},"bankName":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true},"code":{"type":"string","nullable":true},"isPrimary":{"type":"boolean"},"merchantId":{"type":"string","format":"uuid"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"},"settlementProfile":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"}},"additionalProperties":false,"title":"GetSettlementAccountResponse"}},"additionalProperties":false,"title":"GetMerchantSettlementResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetMerchantSettlementResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      