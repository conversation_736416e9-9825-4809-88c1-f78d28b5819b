---
id: get-settlementprofile-all
title: "Get SettlementProfile all"
description: "Get SettlementProfile all"
sidebar_label: "Get SettlementProfile all"
hide_title: true
hide_table_of_contents: true
api: eJztV01PGzEQ/SvRnFrJJZTe9pZKCJAKjQicUA6T9WRj6rWNPRuRRvvfK6/zscmmKELcyCmKPR9vZp5H+5ZgHXlkZc2NhAwK4hExayrJsPN2qjQNtAYBjEWA7Am218N0DWMBDj2WxOSjyRIMlgQZDLGgu6qckAcBykAGLxX5BQgI+YxKhGwJvHDRVBmmorGbWl8ip6MfF1DXYifeSP2lj4l2dXk7Gjw+XK+jzQhl4+PppVKeJGTsKzoQPrBXpoC6Hkfj4KwJFOL9xfl5/JEUcq9cbCpkMKrynEIAAbk1TIabSPTKfacxZl52M9jJM+UMApyP82GV4odVqK3hxFpNaKAWUFIIWFAXpwBTaY0TTamiWoBEbqdD7zE2UjGV4QgY+xSI1Olm3fS+qpRMCH0+Q8M/q6AMhXDXDOIIuGvH32Zi0UtlisMZO45Ysd0S9lDfopGUKs4K9bBV5hR1IAGsWDdsIb5doei8gPsVB2KNHQhu+wiOIWhyaFh+nPlU+cDD/wx+OwKvDvVH4/t92TLq6ByORNo43FNuvTzWxdDru/E5T3Nlq/A+/4/kxS+VuizXB9dopCaf2OecVnmzgfvPwZ72wWkfnPbBJ94HzZfBaRGcFsFpEXzmRVDHRLtC4oq41wnVw0adlcQzuxJw8VkizyCDPjrV77j0k0sgP18LtsrrqICYXcj6/e0z/iZpflZQGRwugtVVRHKW2xKi9FFmapv+bSpu7FoYe4PhDQiIaVIJ8+8NkW3gEpsFt9Zib5S204TNtFoCqhYJ/3JV9hOgi4PrKlUBMeRYwMwGjobL5QQDPXpd1/E4CcrYEKlCnLncjPUPLfYV7Rx1FaFAFJVvOKwk69Z8HP94lUj1NK7FWnsezLxX/0ZEwpf7lVD92ov6/FBfEoiW0t3BnG4HeU6OW3ftEOMWta4uH6Cu/wEZTaSY
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementProfile all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementProfile/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementProfile all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      