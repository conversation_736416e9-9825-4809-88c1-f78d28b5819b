---
id: get-settlementprofile-all
title: "Get SettlementProfile all"
description: "Get SettlementProfile all"
sidebar_label: "Get SettlementProfile all"
hide_title: true
hide_table_of_contents: true
api: eJztV01PGzEQ/SvRnFrJYim97S2VKkAqNCJwQjlM1pONqdc29mxEGu1/r7zOx4ZNUYS4kVMSez7ezDyP8lZgHXlkZc21hBxK4jExa6rIsPN2pjQNtQYBjGWA/BF216N0DRMBDj1WxOSjyQoMVgQ5jLCk27qakgcBykAOzzX5JQgIxZwqhHwFvHTRVBmmsrWbWV8hp6PvF9A0Yi/eWP2lj4l2+fNmPHy4v9pEmxPK1sfTc608ScjZ13QgfGCvTAlNM4nGwVkTKMT7i/Pz+CEpFF652FTIYVwXBYUAAgprmAy3keiFM6cxZl71M9jpExUMApyP82GV4od1qJ3h1FpNaKARUFEIWFIfpwBTa41TTamiRoBE7qZD7zE2UjFV4QgYrykQqdPPuu19XSuZEPpijoZ/1EEZCuG2HcQRcDeOv83UopfKlIcz9hyxZrsj7KG+RSMpVZwV6lGnzBnqQAJYsW7ZQnyzRtF7AXdrDsQaexDc7hEcQ9Dk0LL8OPOZ8oFH/xn8bgReHeqPxvf7smXU0TkcibR1uKPCenmsi6GXd+NznhbK1uF9/h/Ji18qdVluDq7QSE0+sc85rYp2A2dPwZ72wWkfnPbBJ94H7T+D0yI4LYLTIvjMi6CJifaFxCXxoBdqgK06q4jndi3g4rNEnkMOGTqV9Vyy5BLILzaCrfY6KiBmF/IsK6kKtSs9SjqL3wtta3mGzkFUPMrMbNu2baFVcLjsQBsMR9cgIEZPyBffWv7awBW2e20jwd6oaK/27ZA6uqkRCfZqXe0joIvz6gtUATHkRMDcBo6Gq9UUAz143TTxOOnI2AepQhy13E7zDy1fC9kF6jpCgagl33BYK9Wd+ST+8Cpx6XHSiI3kPJj5Vf1b7Qhf7tb69OsgyvJDfUkgOgJ3D3O6HRYFOe7cdUNMOoy6/HkPTfMPzB+gzQ==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get SettlementProfile all"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/SettlementProfile/all"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get SettlementProfile all

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"PageNumber","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"PageSize","in":"query","schema":{"type":"integer","format":"int32"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseListPagedResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseListPagedResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"settlementProfileId":{"type":"string","format":"uuid"},"merchantBusinessName":{"type":"string","nullable":true},"merchantOnboardingId":{"type":"string","nullable":true},"autoSettlement":{"type":"boolean"}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponse"},"nullable":true},"pageNumber":{"type":"integer","format":"int32"},"pageSize":{"type":"integer","format":"int32"},"firstPage":{"type":"string","format":"uri","nullable":true},"lastPage":{"type":"string","format":"uri","nullable":true},"totalPages":{"type":"integer","format":"int32"},"totalRecords":{"type":"integer","format":"int32"},"nextPage":{"type":"string","format":"uri","nullable":true},"previousPage":{"type":"string","format":"uri","nullable":true}},"additionalProperties":false,"title":"GetMerchantSettlementProfileResponseListPagedResponseHandler"}}}}}}
>
  
</StatusCodes>


      