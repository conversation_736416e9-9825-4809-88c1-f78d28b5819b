---
id: update-settlementaccount
title: "Update SettlementAccount settlementAccountId"
description: "Update SettlementAccount settlementAccountId"
sidebar_label: "Update SettlementAccount settlementAccountId"
hide_title: true
hide_table_of_contents: true
api: eJztVUtv2zAM/isBT3uoTbejb1lRdD1sKPo4BTkwFpuolSVVotMGhv/7QMt5NR6wAjs2l9gi+ZH8RH9swAeKyMa7Kw0F1EEj0y0xW6rIMZalrx2DAsZFgmIKO9ukt80UBIxYEVMUlwYcVgQFpLeuVxoUGAcFBOQlKIj0XJtIGgqONSlI5ZIqhKIBXocOgqNxC1Dw4GOFLBXWRkPbqm2Wy4tft5P7u58b6CWhpvgO8LadZWdK/MPrtXiU3jE5lkcMwZqyo2j8mLyTsyMoP3+kUmgKUQhlQ0msc3RP517TUEeuthbnlnJ5reqce6J+d629M8b/Q0SrALU20gza671aH9AmUsCGxRfu38xBn+MmsyT8A9MrfxCyR8j+oHz5+sHMhhlBipSCdym3+v3sTP40pTKaIAmggNu6LCmljslDS04wOsowGtaXinjpddaYUkSm05oCxhjM+Ahk3AygtKAgUVxt9KyOVoSFOaRiPN4FnGhanS6oSgHXydtaCj4tfQWiKDIANztVuXjFKlg6vOod+Uc3Omzye7IleveQb7K/istcyh5Vo8n1FSiQTjKZq28SGHziCrv57FX0nSQf3NB2lDpJCBaNkyQda01P/xQwGFAD60NBMZRjpmDpE0tk08wx0X20bSvHzzXFNRTTmYIVRpNHdtqANkme9XZC3xS51XT4dNOvhs8j2WxDxfeH6NZCH9pa3kDBE63/stnaWas2y+e/l5PT7q26bUmyCbP1PAOe3AnAzuNof8lwbj+S68ndueDN+8VX5bmM+CIbFF9y176ruvt4u7MGLLpFjQvxzaDy+wP2hOue
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update SettlementAccount settlementAccountId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/SettlementAccount/{settlementAccountId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update SettlementAccount settlementAccountId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateSettlementAccountRequest"}},"text/json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateSettlementAccountRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateSettlementAccountRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      