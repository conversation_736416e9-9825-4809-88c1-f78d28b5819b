---
id: update-settlementaccount
title: "Update SettlementAccount settlementAccountId"
description: "Update SettlementAccount settlementAccountId"
sidebar_label: "Update SettlementAccount settlementAccountId"
hide_title: true
hide_table_of_contents: true
api: eJztVUtv2zAM/isBT3toTbejb2lRdD1sKPo4BTkwFpOolSVVotsGhv/7QMt5tPGABdixucQWqY/kR/pjAz5QRDbeXWkooA4amW6J2VJFjrEsfe0YFDAuExRT2NkmvW2mIGDEipiiuDTgsCIoIL13vdKgwDgoICCvQEGkp9pE0lBwrElBKldUIRQN8Dp0EByNW4KChY8VsmRYGw1tq7ZRLi9+3U7u735uoFeEmuIR4G07y86U+MzrtXiU3jE5lkcMwZqyo2j8kLyTswMoP3+gUmgKUQhlQ0msc3SP517TUEWuthbnlnJ6reqce6J+d6Udecf/w41WAWptpBi013u5LtAmUsCGxRfu381BH+MmsyT8A9MrfxCyR8j+oHz5+sHMhhlBipSCdymX+uP0VP40pTKaIAGggNu6LCmljsm3lhxgdBBhNKwvFfHK66wxpYhMpzUFjDGY8QHIuBlAaUFBovi80bM6WhEW5pCK8XhJVarDMqKmE3kura/1CYYAIiTS95udmFy8YhUsve3wjvODRg6b/J5aicwtcgP7DlxSlQKu9xgaTa6vQIEUkDl8/i4Xg09cYTeWvXgeye2bxmwnqFOCYNE4CdKR1fSsTwGDATWwNRQUQzFmClY+sdxsmjkmuo+2beX4qaa4hmI6U/CM0eRJnTagTZJnvR3Md0lupRw+3fQb4fNIFtpQ8v0hurXQh7aWN1DwSOu/LLR21qrNzvnv6eSwextum5IswGw9z4Df7gRg53GwtmQ4t9/G9eTuXPDm/b6r8lxGfJHFiS+5at9l3X2z3VkDFt2yxqX4ZlD5/QGYSufT
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update SettlementAccount settlementAccountId"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/SettlementAccount/{settlementAccountId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update SettlementAccount settlementAccountId

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"settlementAccountId","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateSettlementAccountRequest"}},"text/json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateSettlementAccountRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"bankCode":{"type":"string","nullable":true},"bankAccountName":{"type":"string","nullable":true},"bankAccountNo":{"type":"string","nullable":true}},"additionalProperties":false,"title":"UpdateSettlementAccountRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      