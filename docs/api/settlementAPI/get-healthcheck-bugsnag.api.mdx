---
id: get-healthcheck-bugsnag
title: "Get health check bugsnag"
description: "Get health check bugsnag"
sidebar_label: "Get health check bugsnag"
hide_title: true
hide_table_of_contents: true
api: eJx1kr1u3DAQhF9FmCoBaMtJyc4JjLOLAEHOrg4qeNJaIiyRMncl5CDw3YPV/SSOk0oAZ5aab7gL4kjJiY/hoYFFS3JPrpeu7qh++TK1HFwLA3Etw+5wFL+qiMpgdMkNJJRUXBDcQLDY3H3b3j493sPAB1h05BpKMEj0OvlEDaykiQy47mhwsAvkMOokS/KhRc6VmnmMgYlV/3xzo5+GuE5+1Liw2E51TczI2fylbEiKbo1arCDF/kIykHTxhAolkA4W5dF9tbrL326mNJ/pptQrjMjItiyZRHoaKMhVQ/N1SwOP7sCxnzTDdR0HKIUPz3EF9NKv3Rx9xfYyXtx+f4CB/uYYfv6EbDBGlsEFnT3X+n+oN/SXNoV+Sjn2zge9cI2/nIB3+BMYBue7KoMusqhjWfaO6Sn1Oevx60TpALurDGaXvNsrz67K5vy+2lHjWYUG9tn1TO+S1TEIBYHFhx+nZfhY6H79K/ELHd5u0+z6SW1rs5eH3Nw9IudfO53y6g==
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get health check bugsnag"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/health-check/bugsnag"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get health check bugsnag

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success"}}}
>
  
</StatusCodes>


      