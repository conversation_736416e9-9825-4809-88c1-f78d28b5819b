---
id: update-serviceproviderconnectormaps
title: "Update service provider connector maps id"
description: "Update service provider connector maps id"
sidebar_label: "Update service provider connector maps id"
hide_title: true
hide_table_of_contents: true
api: eJztV0tz0zAQ/iuZPfGQCbQ0gG+hwwAHmE4fp04OG2udCGxJSHJKxuP/zqzsOG5LoekUTsnFibIvffutrK8GY8lhUEZ/lpBCZSUGOiO3UhlZZ1ZKksuM1pQF40q0HgQEXHhIL6EzO+nMvip7vLH8ghZmAiw6LCmQY/saNJYEKSgJApSGFCyGJQhw9KNSjiSkwVUkwGdLKhHSGsLasocPTukFCMi5iMCFVkpC04g+6McPX86mF+efNqGXhJLcDsGbZtYakw/vjVyzRWZ0IB34K1pbqCwiNf7mjea1W6HM/BtlAQRYx7gGRT7aXQeKkf7r1gToAZ7sQLoqGXat5p7bcMW5Z+JWoKBCwQvDfnjGClBKxRvA4mRQX46Fp63bVMqLIQvuaO9pi1SMG+hn2INyA5QhYZ4936MzRIcjO/LWaN9u9+DlS35I8plTlhNCCmdVllGsezCHkWu2QLUbnF2oreHcmIJQM2oleY8L+h3CuioKnPMm+fBoBEgM90in7teuhzX5htfXeP7do/RHJoeAzBEGktPwx7KZF0lQJUWojVS52sXnvhz8SOFv7Gv59g9Cbp6fUMuC3K3x33n492zds/W/svVhb/A9Tfc0/Y80jdeG61eE9tox6ro32iiWUS9ZRqxZRlFxlBSWRraqI2PZEdVHCmO0atxFSDYRkj5CwhHGtZINtDTZqJnKFawzQrA+HY89hVBQSTokklYvFlR6i2tviopLfZGZElhg8IydbkXGh59Y2oLuuPHBYY5vj/LJ6+Tozas3yeujyUEyP8yz5CB7NznMJxPMcQI3Sdhxr2EhlJtIiR77WNTorK91ND35DAJ4Ty2gq1fsaI0PJcbDoJNXuwB9rUU9IQcXt0a04NVdCy4BrYJ+CO9qAghIleRpWhof2K2u5+jpwhVNw8s/KnJrSC9nAlboVDuolzVI5fm77Ml4o8L+eglPTju1+HQE4veVd4uo1wwcFhX/AgHfad1q22bWiI38fPTsbZaB2O0rYC3c/nvcBkzOOcDW4taNZOsxzTKyYWA7TDobjM7J9PyYs847gVwayeYOr1hp41ULhYl7i4d2XKuhQL2o4usA2tT8+QV2GMrA
sidebar_class_name: "patch api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Update service provider connector maps id"}
>
</Heading>

<MethodEndpoint
  method={"patch"}
  path={"/api/service-provider-connector-maps/{id}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Update service provider connector maps id

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"id","in":"path","required":true,"schema":{"type":"string","format":"uuid"}},{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"serviceProviderId":{"type":"string","format":"uuid"},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"}},"additionalProperties":false,"title":"AddUpdateServiceProviderNipConnectorMapRequest"}},"text/json":{"schema":{"type":"object","properties":{"serviceProviderId":{"type":"string","format":"uuid"},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"}},"additionalProperties":false,"title":"AddUpdateServiceProviderNipConnectorMapRequest"}},"application/*+json":{"schema":{"type":"object","properties":{"serviceProviderId":{"type":"string","format":"uuid"},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"}},"additionalProperties":false,"title":"AddUpdateServiceProviderNipConnectorMapRequest"}}}}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"serviceProviderId":{"type":"string","format":"uuid"},"serviceProviderName":{"type":"string","nullable":true},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponse"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponseResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"serviceProviderId":{"type":"string","format":"uuid"},"serviceProviderName":{"type":"string","nullable":true},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponse"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponseResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"object","properties":{"id":{"type":"string","format":"uuid"},"serviceProviderId":{"type":"string","format":"uuid"},"serviceProviderName":{"type":"string","nullable":true},"nipConnector":{"enum":["nibss","wema"],"type":"string","title":"NipConnectors"},"createdAt":{"type":"string","format":"date-time"},"modifiedAt":{"type":"string","format":"date-time"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponse"}},"additionalProperties":false,"title":"GetServiceProviderNipConnectorMapResponseResponseHandler"}}}}}}
>
  
</StatusCodes>


      