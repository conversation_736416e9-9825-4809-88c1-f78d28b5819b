---
id: get-wemanipservice-nipbanks
title: "Get WemaNIPService NipBanks"
description: "Get WemaNIPService NipBanks"
sidebar_label: "Get WemaNIPService NipBanks"
hide_title: true
hide_table_of_contents: true
api: eJztVU1r20AQ/StmTi1so7RH3dwSHENrTJzQg9FhLE3sTVa7m92RqRH738tIiiMnpQ30kkNOBs2br/fW81pwngKydnZeQQ5b4p9Uo9U+Utjrkhbab9DeR1DAuI2Qr0EAi/ly1QOgUOAxYE1MQeItWKwJcphd/FhNb64vQYG2kMOOsKIACgI9NDpQBTmHhhTEckc1Qt4CH7xkRg7abiGlQsDROxspSvzL+bn8VBTLoL1MDTmsmrKkKBOWzjJZ7irRL868QencvuzgNndUMijwQQhg3dePQ6kn4MY5Q2ghKagpRtzSyzkV2MYY3BjqN0oKKuRxOwwBD8IDUx3/PYYwvuhIfEUrAX9z1WvASQFWlRbe0CxHLW/RRFLAmgULC+2/dqKn/y7xXUe+GiS8RFsZCtAV8d7osnt42V107yq9SZW6f9G7PG9TniR1Tk/hjHhyep4nxzGEe9654coLXcg7yCFDr7PTpGyUJD7weNibYOSOM/uYZ1kkZkM1Wf5U0f5sS3X0eIjONDLNWelqkAOu7a3raB2WmvW4yeqYPpku56BA2vRr7D8Lad5FrrF7eo+O8tf1Tqg4yjgygqT6Ddph9TWg16CeG5p6kq5QsHORBdq2G4x0E0xK8vmhoXCAfF0o2GPQvbjrIqlHmxO+Kh0lUB2FfTbi0a/gw9XgiR8n4rR/Gv2eDqemukfTCAySaofotCzJ8yg2LlGMXsDs4hpS+g3sz8fo
sidebar_class_name: "get api-method"
info_path: docs/api/settlementAPI/gemspay-settlement-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Get WemaNIPService NipBanks"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/api/WemaNIPService/NipBanks"}
  context={"endpoint"}
>
  
</MethodEndpoint>



Get WemaNIPService NipBanks

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"GEMSAUTH","in":"header","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"text/plain":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"bankName":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NipBanks"},"nullable":true}},"additionalProperties":false,"title":"NipBanksListResponseHandler"}},"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"bankName":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NipBanks"},"nullable":true}},"additionalProperties":false,"title":"NipBanksListResponseHandler"}},"text/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string","nullable":true},"data":{"type":"array","items":{"type":"object","properties":{"bankName":{"type":"string","nullable":true},"bankCode":{"type":"string","nullable":true}},"additionalProperties":false,"title":"NipBanks"},"nullable":true}},"additionalProperties":false,"title":"NipBanksListResponseHandler"}}}}}}
>
  
</StatusCodes>


      