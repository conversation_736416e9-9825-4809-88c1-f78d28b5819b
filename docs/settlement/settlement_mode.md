---
title: Settlement Mode
sidebar_position: 3
description: The settlement mode describes the disbursement processes
---

import { CardGrid } from '../../src/components/Card/CardGrid.tsx';
import Card from '../../src/components/Card/Card.tsx';

The settlement mode defines how and where funds can be disburse to merchants after payment collection. KollectKollect provides flexible settlement options, allowing merchants to choose the disbursement strategy that best fits their business needs.

Merchants can configure their settlement preferences in several ways:

- **Consolidated Settlement**: All payments from multiple shops combine and sent to a single merchant account
- **Shop-Specific Settlement**: Each shop's payments disbursed to their individually paired bank accounts
- **Bank-Matched Settlement**: Payments collected through specific banks settle to corresponding merchant accounts (e.g., Access Bank payments → Access Bank merchant account, GTB payments → GTB merchant account)
- **Spill Settlement**: Merchants can define spill settlement rules based on percentage or amounts.

## Method of disbursement

There are three methods of disbursement which gives merchant the flexibility to choose how they want to receive their funds. The methods are:

<CardGrid cols={3}>
  <Card title="Sub-account" href="/docs/settlement/subaccount" icon="🏦">
    Disburse funds to specific bank accounts.
  </Card>
  
  <Card title="Mapping Profile" href="/docs/settlement/mapping_profile" icon="📋">
    Disburse funds to a specific bank account by mapping the payment source to bank accounts.
  </Card>
  
  <Card title="Split Configuration" href="/docs/settlement/split_configuration" icon="✂️">
    Disburse funds to multiple bank accounts based on a percentage or amount.
  </Card>
</CardGrid>
