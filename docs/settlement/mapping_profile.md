---
title: Settlement Mapping
sidebar_position: 6
description: Mapping profile allows merchants to direct payments from specific sources or products to designated bank accounts (sub-accounts).
---

Settlement Mapping allows merchants to direct payments from specific sources or products to designated bank accounts (sub-accounts). For example, when a merchant creates a mapping profile that routes all Access Bank payments to a specific bank account, KollectKollect disburses all Access Bank payments to that account.

Merchants can also use settlement mapping to route payments based on specific sales or product to specific bank accounts(sub-account). For example, merchants can create a mapping profile that directs all hardware payments to a particular sub-account 1 or all software payments to sub-account 2, and KollectKollect will disburse the funds respectively.

:::note

1. If the merchant has a mapping profile that maps a specific payment source to a bank account, if other payments are not mapped to a specific sub-account, KollectKollect will automatically disburse the funds to the primary account.

2. Merchants can not map the same payment source to multiple sub-accounts. For example, a merchant can not map all Access Bank payments to two different bank accounts. If you try to do this, KollectKollect will throw an error.

    :::warning
    If any of these actions don't work as expected, please contact the developer team for further help or investigate the issue.

:::

## Creating settlement mapping execution process

The mapping profile creation process involves the following steps:

1. Merchant clicks on `Settlement Mapping`
2. Merchant enter settlement mapping name
3. Merchant selects the payment source(s) to map to the sub-account
4. KollectKollect saves the mapping profile to the database

### Creation flow

```mermaid
graph LR
    A[Merchant] --> B[Click on Settlement Mapping]
    B --> C[Enter Mapping Name]
    C --> D[Select Sub-Account]
    D --> E[Select Payment Source]
    E --> F[Submit]
```

### Creation code explanation

## Settlement mapping disbursement process

The mapping profile process involves the following steps:

1. KollectKollect checks if there is a mapping profile for the payment source
2. If there is a mapping profile, KollectKollect disburses the funds to the mapped bank account
3. If there is no mapping profile, KollectKollect checks if there is a primary account
4. If there is a primary account, KollectKollect disburses the funds to the primary account

### Disbursement flow

```mermaid
graph TD
    A[KollectKollect] --> B[Check for Mapping Profile]
    B -->|Yes| C[Disburse to Mapped sub-account]
    B -->|No| D[Check for Primary Account]
    D -->|Yes| E[Disburse to Primary Account]
    C --> G[End]
    E --> G[End]
```

### Disbursement code explanation

// here
