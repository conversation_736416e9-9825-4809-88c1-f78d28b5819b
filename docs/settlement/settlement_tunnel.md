---
title: Settlement Tunnel
sidebar_position: 4
description: This page explains the settlement tunnel and its role in payment automation.
---

Settlement tunnels are backend payment channels KollectKollect uses to collect money from customers and disburse it to merchants. These tunnels also enable KollectKollect to debit customer accounts during payment processing.

KollectKollect currently supports these settlement tunnels:

- **Wema Tunnel** – powered by Wema Bank  
- **NIP Tunnel** – powered by NIBSS Instant Payment  
- **Premier Trust Tunnel** – powered by Premier Trust

:::note
Each tunnel provides KollectKollect with **collection accounts**—dedicated account numbers for receiving payments on behalf of merchants.
:::

## How collection accounts work with the tunnels

When a customer initiates a transaction, they select their preferred bank. KollectKollect then generates a virtual account based on the selected provider (e.g., Wema or NIP). The customer pays into this account, and the payment automatically reflects in the corresponding collection account.

For example, if a customer selects Wema, KollectKollect generates a Wema account for payment, and the funds reflect in the Wema collection account.

KollectKollect maps each collection account to and disburses the funds according to the merchant's settlement configuration.

:::note
Each collection account maps to its respective tunnel and provider.
:::

## Collection account execution process

The collection process follows these steps:

1. The customer initiates payment.
2. KollectKollect selects a settlement tunnel based on configuration.
3. KollectKollect generates a collection account from the provider.
4. The customer pays into the collection account.
5. The provider credits the funds to its collection account.

### Collection flow

```mermaid
graph TD
    A[Customer] --> B[Initiate Payment]
    B --> C[Select preferred bank]
    C --> D[Generate virtual Account]
    D --> E[Customer Pays]
    E --> F[Funds Land in mapped collection Account]
```
