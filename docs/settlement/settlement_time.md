---
title: Settlement Time
sidebar_position: 2
description: This explain when the system settles the merchant
---

The settlement time defines when the KollectKollect settles the merchants. KollectKollect provides two settlement time options:

- **Immediate Settlement**: This is when KollectKollect disburse the funds to merchants immediately after receiving the payment from the customer.
- **Batch Settlement**: The is when KollectKollect collate all the transactions into a single invoice and disburse the funds to the merchants at a end of the day.

:::warning
Gemspay isn't licensed to hold funds for more than 24 hours. So it's important that the settlement service is able to disburse funds to merchants within 24 hours of receiving the payment from the customer.
:::

## Instant settlement execution process

The immediate settlement process involves the following steps:

1. KollectKollect receives a payment from a customer
2. It validates the payment
3. Calculate the transaction charge from the payment based on what has been configured for the merchant
4. Find merchant's settlement account
5. Disburse the funds to the merchant

### Immediate settlement flow

```mermaid
graph LR
    A[Customer] --> B[Make Payment]
    B --> C[KollectKollect]
    C --> D[Validate Payment]
    D --> E[Calculate the transaction charge]
    E --> F[Find settlement account]
    F --> G[Disburse Funds]
```

## Immediate disbursement code explanation

// here

## Batch settlement execution process

The batch settlement process involves the following steps:

1. KollectKollect receives a payment from a customer
2. Validates the payment
3. Automate add the payment to the batch queue
4. Collates all the payments in the batch queue into a single invoice
5. Deduces the fees from the invoice
6. Disburse the funds to the merchant at the end of the day
7. Sends a notification to the merchant

### Batch disbursement flow

```mermaid
graph LR
    A[Customer] --> B[Make Payment]
    B --> C[KollectKollect]
    C --> D[Validate Payment]
    D --> E[Add Payment to Batch Queue]
    E --> F[Collate Payments]
    F --> G[Deduct Fees]
    G --> H[Disburse Funds]
    H --> I[Send Notification]
```

### Batch settlement code explanation

// here