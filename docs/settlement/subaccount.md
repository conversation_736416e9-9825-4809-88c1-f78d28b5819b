---
title: Sub-account
sidebar_position: 4
description: This page explain the sub-account
---

At the point of onboarding merchants, they must provide a primary account. The primary account acts as the default account for all disbursements. It's also use to disburse funds to the merchant if there is no specific disbursement method.

:::warning
Merchant shouldn't be able to delete their primary account. Reject every request to delete the primary account. If you notice a merchant didn't provide a primary account, Contact the onboarding team.
:::

Sub-accounts are secondary accounts that merchants link to their primary account. Merchants use them to target fund disbursements for specific purposes. For example, a merchant can create a sub-account for each of their shops. When the system disburses funds to these sub-accounts, the merchant can identify which shop generated the revenue.

Each of this sub-account make use of **mode-code** to identify the type of account. The mode-code is a unique identifier for each type of account. For example,


| Sub-account | Mode Code | Description |
|-------------|-----------|-------------|
| Primary Account | SBA1      | Shop 1    |
| Sub-account 1 | SBA2      | Shop 2      |
| Sub-account 2 | SBA3      | Shop 3      |
| Sub-account 3 | SBA4      | Shop 4      |

## Adding sub-account execution process

The sub-account creation process involves the following steps:

1. <PERSON> adds a sub-account
2. Merchant selects the bank and account number
3. KollectKollect verifies the account and get the account name
4. Merchant confirms the account details
5. KollectKollect saves the account to the sub-account collection

### Creation flow

```mermaid
graph LR
    A[Merchant] --> B[Add Sub-Account]
    B --> C[Select bank and account number]
    C --> D[Verify account and get account name]
    D --> E[Confirm Account Details]
    E --> F[submit]
    F --> G[Save Account to Sub-account collection]
```

### Creation code explanation


## Sub-account disbursement execution process

The sub-account disbursement process involves the following steps:

1. KollectKollect checks if there is a specific sub-account for settlement
2. If there is a specific sub-account, KollectKollect disburses the funds to the sub-account
3. If there is no specific sub-account, KollectKollect checks if there is a primary account
4. If there is a primary account, KollectKollect disburses the funds to the primary account

### Disbursement flow

```mermaid
graph TD
    A[System] --> B[Check for Specific Shop Accounts]
    B -->|Yes| C[Disburse to Specify Shop Account]
    B -->|No| D[Check for Primary Account]
    D -->|Yes| E[Disburse to Primary Account]
    C --> G[End]
    E --> G[End]
```

### Disbursement code explanation

// here
