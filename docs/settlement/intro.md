---
title: Overview
sidebar_position: 1
description: Learn about the kollectkollect settlement service
---
import { CardGrid } from '../../src/components/Card/CardGrid.tsx';
import Card from '../../src/components/Card/Card.tsx';

The settlement service handles the disbursement of funds to merchants after receiving payments from their customers. For example, the customer pays ₦200,000 through any channel provided by KollectKollect, the settlement service disburse this funds to the merchant. This action takes place according to the settlement configuration setup for the merchant.  

Here is visual representation of the settlement service:

![settlement service](/img/settlediagram.png)

## Settlement service processes

Three processes involve in settling merchants' transactions are:

<CardGrid cols={3}>
  <Card title="Settlement Time" href="/docs/settlement/settlement_time" icon="⏱️">
    When the system settles the merchants' transactions.
  </Card>
  
  <Card title="Settlement Mode" href="/docs/settlement/settlement_mode" icon="🏦">
    How and where the funds are disbursed to merchants.
  </Card>
  
  <Card title="Settlement Tunnel" href="/docs/settlement/settlement_tunnel" icon="🔗">
    Channel used for the transfer of settling transactions.
  </Card>
</CardGrid>

