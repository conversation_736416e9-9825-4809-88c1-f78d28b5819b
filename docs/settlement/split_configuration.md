---
title: Settlement Splitting
sidebar_position: 6
description: This page explain the`Settlement Splitting
---
Settlement Splitting allows merchants to split payments into multiple sub-accounts based on a percentage or amount. For example, a merchant can configure a split that sends 50% of all payments to one bank account and 50% to another or merchants can configure a split that sends ₦100,000 of all payments to sub-account 1,  ₦50,000 to sub-account 2, ₦20,000 to sub-account 3 and the remaining amount to the primary account.

## Creating percentage settlement splitting execution process

The percentage split configuration creation process involves the following steps:

1. <PERSON> clicks on `Settlement Splitting`
2. <PERSON> enter`Settlement Splitting` name
3. Merchant selects the sub-account to split to
4. <PERSON> enters the percentage to split to the sub-account
5. KollectKollect saves the`Settlement Splitting to the database

### Percentage settlement splitting creation flow

```mermaid
graph LR
    A[Merchant] --> B[Click on Settlement Splitting]
    B --> C[Enter Settlement Splitting Name]
    C --> D[Select Sub-Account]
    D --> E[Enter Percentage]
    E --> F[Submit]
```

### Percentage settlement splitting code explanation

// here

## Adding flat(fixed amount) settlement splitting execution process

The flat Settlement Splitting creation process involves the following steps:

1. Merchant clicks on `Settlement Splitting`
2. Merchant enter Settlement Splitting name
3. <PERSON> selects the sub-account to split to
4. <PERSON> enters the amount to split to the sub-account
5. KollectKollect saves the Settlement Splitting to the database

### Creation flow

```mermaid
graph LR
    A[Merchant] --> B[Click on Settlement Splitting]
    B --> C[Enter Settlement Splitting Name]
    C --> D[Select Sub-Account]
    D --> E[Enter Amount]
    E --> F[Submit]
```

### Flat Settlement Splitting code explanation

// here