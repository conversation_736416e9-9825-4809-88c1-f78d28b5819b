---
title: Introduction
sidebar_position: 1
description: This is the introduction page
---

import { CardGrid } from '../../src/components/Card/CardGrid.tsx';
import Card from '../../src/components/Card/Card.tsx';

Welcome to the KollectKollect process documentation. This documentation explains the application process, the tech stack used, the workflow and the necessary requirement needed.

## What's kollectkollect?

KollectKollect is a smart payment collection solution that helps merchants to automate and manage their day to day transactions. It's also responsible for handling transaction invoicing and consolidation.
For example, if a merchant has a four shops, KollectKollect:

- Help this merchant to collect all the payments from the four shops via multiple bank accounts
- Manage all the transactions from the four shops with minimal charges
- Help merchant to receive payment from their customers through diverse means such as USSD, QR Code and bank transfer
- Collate all the transactions from the four shops into a single view
- Merge all the payments from the four shops into a single invoice when disbursing funds to the merchant

## Services offered

KollectKollect offers the following services:

<CardGrid cols={3}>
  <Card title="Settlement Service" href="../settlement/intro" icon="🏦">
    Disbursement of funds to merchants after receiving payments from their customers.
  </Card>
  
  <Card title="Virtual Account Service" icon="💳">
    Management of virtual accounts for merchants.
  </Card>
  
  <Card title="Notification Service"  icon="🔔">
    Sending notifications to merchants.
  </Card>

  <Card title="Partners Service" icon="🤝">
    Management of partners for merchants.
  </Card>
  
  <Card title="USSD Service" icon="📞">
    Management of USSD for merchants.
  </Card>
  
  <Card title="NQR Service" icon="🂓">
    Management of NQR for merchants.
  </Card>
  
  <Card title="Hosted Checkout Service"  icon="🕸️">
    Management of hosted checkout for merchants.
  </Card>

  <Card title="API" icon="👨🏽‍💻">
    Management of API for merchants.
  </Card>
  
  <Card title="SDK" icon="JS">
    Management of SDK for merchants.
  </Card>
  
  <Card title="User Interface" icon="📲">
    Management of user interface for merchants.
  </Card> 

  <Card title="Connector API Gateway" icon="🔗">
    Management of connector API gateway for merchants.
  </Card>

  <Card title="ERP Connector Service" icon="📡">
    Management of ERP connector service for merchants.
  </Card>
</CardGrid>

:::note
Not all the services are available via the GUI. Some services like ERP Connector Service, Connector API Gateway etc operate in the backend and are only use via the API.
:::
