---
sidebar_position: 1
---

# Local Development Setup

This guide will help you set up the ERP system for local development.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (v18 or higher)
- **PostgreSQL** (v14 or higher)
- **Git**
- **Docker** (optional, for containerized development)

## Environment Setup

### 1. Clone the Repository

```bash
git clone https://github.com/yourcompany/erp-system.git
cd erp-system
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Environment Configuration

Create a `.env` file in the root directory:

```env
# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/erp_db"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key"
JWT_EXPIRES_IN="24h"

# OAuth Configuration
OAUTH_CLIENT_ID="your-oauth-client-id"
OAUTH_CLIENT_SECRET="your-oauth-client-secret"

# Redis Configuration (optional)
REDIS_URL="redis://localhost:6379"

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
```

### 4. Database Setup

```bash
# Create database
createdb erp_db

# Run migrations
npm run db:migrate

# Seed initial data
npm run db:seed
```

## Running the Application

### Development Mode

```bash
# Start all services
npm run dev

# Or start services individually
npm run dev:api      # API Gateway (port 3000)
npm run dev:payroll  # Payroll Service (port 3001)
npm run dev:hr       # HR Service (port 3002)
npm run dev:inventory # Inventory Service (port 3003)
npm run dev:frontend # React Frontend (port 3004)
```

### Using Docker

```bash
# Start all services with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f
```

## Verification

Once everything is running, verify your setup:

1. **API Gateway**: http://localhost:3000/health
2. **Frontend**: http://localhost:3004
3. **API Documentation**: http://localhost:3000/api-docs

## Common Issues

### Database Connection Issues

- Ensure PostgreSQL is running
- Check database credentials in `.env`
- Verify database exists

### Port Conflicts

- Check if ports 3000-3004 are available
- Modify port configurations in `docker-compose.yml` if needed

### Permission Issues

- Ensure proper file permissions for log directories
- Check Docker daemon permissions


