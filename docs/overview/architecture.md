---
sidebar_position: 2
---

# System architecture

## High-level architecture

The ERP system follows a modern microservices architecture with clear separation of concerns:

```mermaid
graph TD
    subgraph Clients
        A["Web Client\n(React.js)"]
        B["Mobile App\n(React Native)"]
        C["External APIs\n(Webhooks)"]
    end

    subgraph Gateway
        D["API Gateway\n(Express.js)"]
    end

    subgraph Services
        E[Payroll Service]
        F[HR Service]
        G[Inventory Service]
    end

    subgraph Database
        H["Database\n(PostgreSQL)"]
    end

    A --> D
    B --> D
    C --> D
    D --> E
    D --> F
    D --> G
    E --> H
    F --> H
    G --> H
```

## Core components

### API gateway

- **Purpose**: Single entry point for all client requests
- **Technology**: Express.js with middleware for authentication, rate limiting, and logging
- **Responsibilities**:
  - Route requests to appropriate microservices
  - Handle authentication and authorization
  - Request/response transformation
  - API versioning and documentation

### Microservices

#### Payroll service

- **Port**: 3001
- **Database Schema**: `payroll_*` tables
- **Key Features**:
  - Employee salary calculations
  - Tax deductions and benefits
  - Payslip generation
  - Reporting and analytics
