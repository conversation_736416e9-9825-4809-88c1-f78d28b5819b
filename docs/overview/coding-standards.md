---
sidebar_position: 3
---

# Coding standards

## JavaScript/typeScript Style Guide

### Naming Conventions

- **Variables and Functions**: Use camelCase
  ```typescript
  const userData = getUserProfile();
  ```

- **Classes and Interfaces**: Use PascalCase
  ```typescript
  class UserService {
    // ...
  }
  ```

- **Constants**: Use UPPER_SNAKE_CASE
  ```typescript
  const MAX_LOGIN_ATTEMPTS = 5;
  ```

- **File Names**: Use kebab-case for files
  ```
  user-service.ts
  auth-middleware.ts
  ```

### Code formatting

- **Indentation**: 2 spaces
- **Line Length**: Maximum 80 characters
- **Semicolons**: Required
- **Quotes**: Single quotes for strings
- **Trailing Commas**: Required for multiline

```typescript
const user = {
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
};
```

### TypeScript best practices

- **Type Annotations**: Prefer explicit types over `any`
  ```typescript
  function getUser(id: string): User {
    // ...
  }
  ```

- **Interfaces vs Types**: Use interfaces for objects, types for unions/intersections
  ```typescript
  interface User {
    id: string;
    name: string;
  }

  type UserRole = 'admin' | 'user' | 'guest';
  ```

- **Null Checking**: Use optional chaining and nullish coalescing
  ```typescript
  const userName = user?.profile?.name ?? 'Anonymous';
  ```

## React Component Guidelines

### Component Structure

```typescript
import React from 'react';
import './UserProfile.css';

interface UserProfileProps {
  userId: string;
  showDetails: boolean;
}

export const UserProfile: React.FC<UserProfileProps> = ({ 
  userId, 
  showDetails 
}) => {
  // State hooks
  const [loading, setLoading] = useState(false);
  
  // Effect hooks
  useEffect(() => {
    // Component logic
  }, [userId]);
  
  // Event handlers
  const handleClick = () => {
    // Handler logic
  };
  
  // Render helpers
  const renderDetails = () => {
    // Render logic
  };
  
  return (
    <div className="user-profile">
      {/* JSX structure */}
    </div>
  );
};
```

### State Management

- Use React Context for global state
- Use Redux for complex state with many components
- Use React Query for server state