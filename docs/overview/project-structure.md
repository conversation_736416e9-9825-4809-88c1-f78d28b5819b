---
sidebar_position: 1
---

# Project Structure

Understanding the codebase organization and file structure.

## Root Directory Structure

```
erp-system/
├── services/                 # Microservices
│   ├── api-gateway/         # Main API gateway
│   ├── payroll-service/     # Payroll management
│   ├── hr-service/          # Human resources
│   └── inventory-service/   # Inventory management
├── frontend/                # React.js frontend
├── shared/                  # Shared utilities and types
├── database/               # Database schemas and migrations
├── docs/                   # Documentation
├── scripts/                # Build and deployment scripts
├── docker-compose.yml      # Docker configuration
└── package.json           # Root package configuration
```

## Service Structure

Each microservice follows a consistent structure:

```
service-name/
├── src/
│   ├── controllers/        # Request handlers
│   ├── services/          # Business logic
│   ├── models/            # Data models (Prisma)
│   ├── middleware/        # Custom middleware
│   ├── routes/            # API routes
│   ├── utils/             # Utility functions
│   └── app.js             # Express app setup
├── tests/                 # Unit and integration tests
├── prisma/               # Database schema
├── Dockerfile            # Container configuration
└── package.json          # Service dependencies
```

## Frontend Structure

```
frontend/
├── public/               # Static assets
├── src/
│   ├── components/       # Reusable UI components
│   ├── pages/           # Page components
│   ├── hooks/           # Custom React hooks
│   ├── services/        # API service calls
│   ├── store/           # State management
│   ├── utils/           # Helper functions
│   ├── types/           # TypeScript type definitions
│   └── App.tsx          # Main app component
├── package.json
└── tsconfig.json
```

## Shared Directory

```
shared/
├── types/               # Common TypeScript types
├── constants/           # Application constants
├── utils/              # Shared utility functions
├── validators/         # Input validation schemas
└── interfaces/         # API interfaces
```

## Database Structure

```
database/
├── migrations/         # Database migration files
├── seeds/             # Initial data seeds
├── schemas/           # SQL schema definitions
└── backup/            # Database backup scripts
```

## Key Files and Their Purpose

### Configuration Files

- **`docker-compose.yml`** - Multi-service Docker setup
- **`.env.example`** - Environment variables template
- **`package.json`** - Root dependencies and scripts

### Service Entry Points

- **`services/api-gateway/src/app.js`** - Main API gateway
- **`services/*/src/app.js`** - Individual service entry points
- **`frontend/src/App.tsx`** - React application root

### Database Files

- **`services/*/prisma/schema.prisma`** - Database schemas
- **`database/migrations/`** - Database version control

## Naming Conventions

### Files and Directories
- **kebab-case** for directories: `user-management/`
- **camelCase** for JavaScript/TypeScript files: `userController.js`
- **PascalCase** for React components: `UserProfile.tsx`

### Code Conventions
- **camelCase** for variables and functions: `getUserData()`
- **PascalCase** for classes and interfaces: `UserService`
- **UPPER_SNAKE_CASE** for constants: `MAX_LOGIN_ATTEMPTS`

## Import/Export Patterns

### Barrel Exports
```typescript
// src/services/index.ts
export { UserService } from './userService';
export { PayrollService } from './payrollService';
export { InventoryService } from './inventoryService';
```

### Relative Imports
```typescript
// Use absolute imports for shared utilities
import { validateEmail } from '@shared/utils';

// Use relative imports within the same service
import { UserModel } from '../models/userModel';
```

## Environment-Specific Files

```
config/
├── development.json     # Development configuration
├── production.json      # Production configuration
├── test.json           # Test environment configuration
└── default.json        # Default configuration
```

## Testing Structure

```
tests/
├── unit/               # Unit tests
├── integration/        # Integration tests
├── e2e/               # End-to-end tests
├── fixtures/          # Test data
└── helpers/           # Test utilities
```

## Build and Deployment

```
scripts/
├── build.sh           # Build all services
├── deploy.sh          # Deployment script
├── test.sh            # Run all tests
└── setup.sh           # Initial setup script
```
