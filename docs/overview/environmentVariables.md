---
sidebar_position: 3
---

# Environment Variables

This guide covers all environment variables used in the ERP system and how to configure them for different environments.

## Required Environment Variables

### Database Configuration

```bash
# PostgreSQL Database Connection
DATABASE_URL="postgresql://username:password@localhost:5432/erp_db"

# Database Pool Settings
DB_POOL_MIN=2
DB_POOL_MAX=20
DB_POOL_TIMEOUT=30000
```

### Authentication & Security

```bash
# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-min-32-characters"
JWT_EXPIRES_IN="24h"
JWT_REFRESH_EXPIRES_IN="7d"

# OAuth Configuration
OAUTH_CLIENT_ID="your-oauth-client-id"
OAUTH_CLIENT_SECRET="your-oauth-client-secret"
OAUTH_REDIRECT_URI="http://localhost:3000/auth/callback"

# Encryption Keys
ENCRYPTION_KEY="your-32-character-encryption-key"
BCRYPT_ROUNDS=12
```

### Application Settings

```bash
# Server Configuration
NODE_ENV="development"
PORT=3000
HOST="localhost"

# API Configuration
API_VERSION="v1"
API_BASE_URL="http://localhost:3000/api"
CORS_ORIGIN="http://localhost:3004"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100
```

### External Services

```bash
# Redis Configuration
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD=""
REDIS_DB=0

# Email Configuration (SMTP)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
EMAIL_FROM="<EMAIL>"

# File Storage (AWS S3)
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="your-s3-bucket-name"

# Payment Gateway (Stripe)
STRIPE_PUBLIC_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."
```

### Monitoring & Logging

```bash
# Logging Configuration
LOG_LEVEL="info"
LOG_FORMAT="json"
LOG_FILE_PATH="./logs/app.log"

# Monitoring
SENTRY_DSN="https://<EMAIL>/project-id"
NEW_RELIC_LICENSE_KEY="your-new-relic-license-key"
NEW_RELIC_APP_NAME="ERP System"

# Health Check
HEALTH_CHECK_INTERVAL=30000  # 30 seconds
```

## Environment-Specific Configurations

### Development Environment

```bash
# .env.development
NODE_ENV=development
PORT=3000
DATABASE_URL=postgresql://dev_user:dev_pass@localhost:5432/erp_dev
JWT_SECRET=dev-jwt-secret-key-for-development-only
LOG_LEVEL=debug
CORS_ORIGIN=http://localhost:3004
```

### Testing Environment

```bash
# .env.test
NODE_ENV=test
PORT=3001
DATABASE_URL=postgresql://test_user:test_pass@localhost:5432/erp_test
JWT_SECRET=test-jwt-secret-key-for-testing-only
LOG_LEVEL=error
RATE_LIMIT_MAX_REQUESTS=1000  # Higher limit for tests
```

### Staging Environment

```bash
# .env.staging
NODE_ENV=staging
PORT=3000
DATABASE_URL=******************************************************/erp_staging
JWT_SECRET=staging-jwt-secret-key-32-characters-min
LOG_LEVEL=info
CORS_ORIGIN=https://staging.yourcompany.com
```

### Production Environment

```bash
# .env.production
NODE_ENV=production
PORT=3000
DATABASE_URL=***********************************************/erp_prod
JWT_SECRET=production-jwt-secret-key-very-secure-32-chars
LOG_LEVEL=warn
CORS_ORIGIN=https://app.yourcompany.com
RATE_LIMIT_MAX_REQUESTS=50  # Stricter limits in production
```

## Security Best Practices

### Secret Management

1. **Never commit secrets to version control**
   ```bash
   # Add to .gitignore
   .env
   .env.local
   .env.*.local
   ```

2. **Use environment-specific files**
   ```bash
   .env.example        # Template with dummy values
   .env.development    # Development settings
   .env.test          # Test settings
   .env.production    # Production settings (never committed)
   ```

3. **Use secret management services in production**
   - AWS Secrets Manager
   - Azure Key Vault
   - HashiCorp Vault
   - Kubernetes Secrets

### Environment Variable Validation

```typescript
// config/env.ts
import { z } from 'zod';

const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'test', 'staging', 'production']),
  PORT: z.string().transform(Number),
  DATABASE_URL: z.string().url(),
  JWT_SECRET: z.string().min(32),
  JWT_EXPIRES_IN: z.string(),
  REDIS_URL: z.string().url().optional(),
  SMTP_HOST: z.string(),
  SMTP_PORT: z.string().transform(Number),
  SMTP_USER: z.string().email(),
  SMTP_PASS: z.string(),
});

export const env = envSchema.parse(process.env);
```

## Loading Environment Variables

### Using dotenv

```typescript
// app.ts
import dotenv from 'dotenv';
import path from 'path';

// Load environment-specific .env file
const envFile = `.env.${process.env.NODE_ENV || 'development'}`;
dotenv.config({ path: path.resolve(process.cwd(), envFile) });

// Fallback to default .env
dotenv.config();
```

### Docker Environment

```dockerfile
# Dockerfile
FROM node:18-alpine

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000

# Copy environment file
COPY .env.production .env

# Install dependencies and start app
RUN npm ci --only=production
CMD ["npm", "start"]
```

### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/erp_db
      - REDIS_URL=redis://redis:6379
    env_file:
      - .env.development
    depends_on:
      - db
      - redis

  db:
    image: postgres:14
    environment:
      POSTGRES_DB: erp_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password

  redis:
    image: redis:7-alpine
```

## Troubleshooting

### Common Issues

1. **Environment variables not loading**
   ```bash
   # Check if .env file exists
   ls -la .env*
   
   # Verify file permissions
   chmod 600 .env
   
   # Check for syntax errors
   cat .env | grep -v '^#' | grep '='
   ```

2. **Database connection issues**
   ```bash
   # Test database connection
   psql $DATABASE_URL -c "SELECT 1;"
   
   # Check if database exists
   psql $DATABASE_URL -c "\l"
   ```

3. **Redis connection issues**
   ```bash
   # Test Redis connection
   redis-cli -u $REDIS_URL ping
   ```

### Environment Variable Debugging

```typescript
// utils/debug-env.ts
export function debugEnvironment() {
  console.log('Environment Variables:');
  console.log('NODE_ENV:', process.env.NODE_ENV);
  console.log('PORT:', process.env.PORT);
  console.log('DATABASE_URL:', process.env.DATABASE_URL ? '***HIDDEN***' : 'NOT SET');
  console.log('JWT_SECRET:', process.env.JWT_SECRET ? '***HIDDEN***' : 'NOT SET');
  console.log('REDIS_URL:', process.env.REDIS_URL ? '***HIDDEN***' : 'NOT SET');
}

// Only run in development
if (process.env.NODE_ENV === 'development') {
  debugEnvironment();
}
```

## Environment Template

Create a `.env.example` file for your team:

```bash
# .env.example
# Copy this file to .env and fill in your values

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# Authentication
JWT_SECRET=your-jwt-secret-key-minimum-32-characters
JWT_EXPIRES_IN=24h

# OAuth (optional)
OAUTH_CLIENT_ID=your-oauth-client-id
OAUTH_CLIENT_SECRET=your-oauth-client-secret

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Redis (optional)
REDIS_URL=redis://localhost:6379

# AWS S3 (optional)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-bucket-name

# Monitoring (optional)
SENTRY_DSN=https://<EMAIL>/project-id
```

