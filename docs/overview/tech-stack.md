---
title: Tech Stack
sidebar_position: 3
description: This page explain the tech stack used in the project
---

import CardGrid  from '../../src/components/Card/CardGrid.tsx';
import Card from '../../src/components/Card/Card.tsx';

## Overview

This document outlines the technology stack used in the project.

## Frontend

- **Framework:** React.js, TypeScript, Vue.js
- **Styling:** Tailwind CSS
- **State Management:** Redux, Context API

## Backend

- **Language:**: .NET
- **API:** REST
- **Authentication:** JWT, API key

## Database

- **Type:** Ms SQL
- **ORM/ODM:** Prisma, Sequelize

## DevOps & Deployment

- **Containerization:** Docker
- **CI/CD:** Azure pipeline, Git
- **Cloud Provider:** Microsoft Azure

## Monitoring & Logging

- **Monitoring Tools:** Prometheus,Traefik
- **Logging:** Console and Database
- **Error Reporting:** <PERSON>nag and <PERSON>ail