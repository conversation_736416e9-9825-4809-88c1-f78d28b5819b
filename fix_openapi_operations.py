#!/usr/bin/env python3
"""
Script to fix OpenAPI operations by adding missing summary and operationId fields.
"""

import re
import sys
from pathlib import Path

def generate_operation_id(method, path):
    """Generate a camelCase operationId from HTTP method and path."""
    # Remove path parameters and clean up the path
    clean_path = re.sub(r'\{[^}]+\}', '', path)
    clean_path = re.sub(r'/+', '/', clean_path)  # Remove double slashes
    clean_path = clean_path.strip('/')
    
    # Split path into parts and convert to camelCase
    parts = [part for part in clean_path.split('/') if part]
    
    # Convert method to action word
    method_actions = {
        'get': 'get',
        'post': 'create' if 'add' not in clean_path.lower() else 'add',
        'put': 'update',
        'patch': 'update',
        'delete': 'delete'
    }
    
    action = method_actions.get(method.lower(), method.lower())
    
    # Build operation ID
    if not parts:
        return f"{action}Root"
    
    # Convert parts to camelCase
    camel_parts = []
    for i, part in enumerate(parts):
        if i == 0:
            camel_parts.append(part.lower())
        else:
            camel_parts.append(part.capitalize())
    
    operation_id = action + ''.join(word.capitalize() for word in camel_parts)
    
    # Clean up common patterns
    operation_id = re.sub(r'([A-Z])[A-Z]+', lambda m: m.group(0).capitalize(), operation_id)
    
    return operation_id

def generate_summary(method, path):
    """Generate a human-readable summary from HTTP method and path."""
    # Remove path parameters for summary
    clean_path = re.sub(r'\{[^}]+\}', 'item', path)
    clean_path = re.sub(r'/+', '/', clean_path)
    clean_path = clean_path.strip('/')
    
    # Convert method to action phrase
    method_phrases = {
        'get': 'Get',
        'post': 'Create' if 'add' not in clean_path.lower() else 'Add',
        'put': 'Update',
        'patch': 'Update',
        'delete': 'Delete'
    }
    
    action = method_phrases.get(method.lower(), method.capitalize())
    
    # Extract resource name from path
    parts = [part for part in clean_path.split('/') if part and part != 'api']
    
    if not parts:
        return f"{action} root resource"
    
    # Build a readable resource name
    resource_parts = []
    for part in parts:
        if part == 'item':
            continue
        # Convert kebab-case and snake_case to words
        words = re.sub(r'[-_]', ' ', part).split()
        resource_parts.extend(words)
    
    if resource_parts:
        resource_name = ' '.join(resource_parts)
        return f"{action} {resource_name}"
    else:
        return f"{action} resource"

def fix_openapi_file(file_path):
    """Fix OpenAPI file by adding missing summary and operationId fields."""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    new_lines = []
    i = 0
    current_path = None
    
    while i < len(lines):
        line = lines[i]
        
        # Track current path
        path_match = re.match(r'^  (/[^:]+):', line)
        if path_match:
            current_path = path_match.group(1)
        
        # Check for HTTP method without summary/operationId
        method_match = re.match(r'^    (get|post|put|patch|delete):', line)
        if method_match and current_path:
            method = method_match.group(1)
            new_lines.append(line)
            
            # Look ahead to see if summary or operationId already exists
            j = i + 1
            has_summary = False
            has_operation_id = False
            indent_level = None
            
            while j < len(lines):
                next_line = lines[j]
                
                # Determine the indent level of the operation content
                if indent_level is None and next_line.strip():
                    indent_match = re.match(r'^(\s+)', next_line)
                    if indent_match:
                        indent_level = len(indent_match.group(1))
                
                # Stop if we hit another operation or path
                if (re.match(r'^    (get|post|put|patch|delete):', next_line) or 
                    re.match(r'^  /', next_line)):
                    break
                
                # Check for existing summary or operationId
                if re.match(r'^\s+summary:', next_line):
                    has_summary = True
                if re.match(r'^\s+operationId:', next_line):
                    has_operation_id = True
                
                j += 1
            
            # Add missing fields
            if not has_summary or not has_operation_id:
                operation_id = generate_operation_id(method, current_path)
                summary = generate_summary(method, current_path)
                
                if not has_summary:
                    new_lines.append(f"      summary: {summary}")
                if not has_operation_id:
                    new_lines.append(f"      operationId: {operation_id}")
        else:
            new_lines.append(line)
        
        i += 1
    
    # Write the fixed content back
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(new_lines))
    
    print(f"Fixed OpenAPI file: {file_path}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python fix_openapi_operations.py <openapi_file>")
        sys.exit(1)
    
    file_path = Path(sys.argv[1])
    if not file_path.exists():
        print(f"Error: File {file_path} does not exist")
        sys.exit(1)
    
    fix_openapi_file(file_path)
    print("OpenAPI operations fixed successfully!")
