name: <PERSON> linting
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

  workflow_dispatch: 

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
   
      - name: Vale
        uses: errata.ai/vale-action@v2.0.0
        with:
          debug: true
          styles: 
             https://github.com/errata-ai/write-good/releases/latest/download/write-good.zip
             https://github.com/errata-ai/Google/releases/latest/download/Google.zip
             https://github.com/errata-ai/Joblint/releases/latest/download/Joblint.zip
