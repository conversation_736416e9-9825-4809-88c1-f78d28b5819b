extends: substitution
message: "Use '%s' instead of '%s'."
link: "https://developers.google.com/style/word-list"
level: warning
ignorecase: false
action:
  name: replace
swap:
  "(?:API Console|dev|developer) key": API key
  "(?:cell ?phone|smart ?phone)": phone|mobile phone
  "(?:dev|developer|APIs) console": API console
  "(?:e-mail|Email|E-mail)": email
  "(?:file ?path|path ?name)": path
  "(?:kill|terminate|abort)": stop|exit|cancel|end
  "(?:OAuth ?2|Oauth)": OAuth 2.0
  "(?:ok|Okay)": OK|okay
  "(?:WiFi|wifi)": Wi-Fi
  '[\.]+apk': APK
  '3\-D': 3D
  'Google (?:I\-O|IO)': Google I/O
  "tap (?:&|and) hold": touch & hold
  "un(?:check|select)": clear
  above: preceding
  account name: username
  action bar: app bar
  admin: administrator
  Ajax: AJAX
  a\.k\.a|aka: or|also known as
  Android device: Android-powered device
  android: Android
  API explorer: APIs Explorer
  application: app
  approx\.: approximately
  authN: authentication
  authZ: authorization
  autoupdate: automatically update
  cellular data: mobile data
  cellular network: mobile network
  chapter: documents|pages|sections
  check box: checkbox
  CLI: command-line tool
  click on: click|click in
  Cloud: Google Cloud Platform|GCP
  Container Engine: Kubernetes Engine
  content type: media type
  curated roles: predefined roles
  data are: data is
  Developers Console: Google API Console|API Console
  disabled?: turn off|off
  ephemeral IP address: ephemeral external IP address
  fewer data: less data
  file name: filename
  firewalls: firewall rules
  functionality: capability|feature
  Google account: Google Account
  Google accounts: Google Accounts
  Googling: search with Google
  grayed-out: unavailable
  HTTPs: HTTPS
  in order to: to
  ingest: import|load
  k8s: Kubernetes
  long press: touch & hold
  network IP address: internal IP address
  omnibox: address bar
  open-source: open source
  overview screen: recents screen
  regex: regular expression
  SHA1: SHA-1|HAS-SHA1
  sign into: sign in to
  sign-?on: single sign-on
  static IP address: static external IP address
  stylesheet: style sheet
  synch: sync
  tablename: table name
  tablet: device
  touch: tap
  url: URL
  vs\.: versus
  World Wide Web: web
