import type { SidebarsConfig } from "@docusaurus/plugin-content-docs";
import apiReference from './docs/api/openapi/sidebar'

// This runs in Node.js - Don't use client-side code here (browser APIs, JSX...)

/**
 * Creating a sidebar enables you to:
 - create an ordered group of docs
 - render a sidebar for each doc of that group
 - provide next/previous navigation

 The sidebars can be generated from the filesystem, or explicitly defined here.

 Create as many sidebars as you want.
 */
const sidebars: SidebarsConfig = {
  apiReference: [
    {
      type: 'category',
      label: 'API Reference',
      link: {
          type: "generated-index",
          title: "API Rereference",
          description: 'These are endpoint for First Pension Middleware API',
          slug: "/api-reference",
      },
      items: apiReference
    }
  ],
  docs: [
    {
      type: "category",
      label: "Overview",
      items: [
        "overview/introduction",
        "overview/architecture",
        "overview/tech-stack",
        "overview/project-structure",
        "overview/coding-standards",
        "overview/local-development",
        "overview/common-issues",
      ],
    },
    {
      type: "category",
      label: "Settlement Service",
      items: [
        "settlement/intro",
        "settlement/settlement_time",
        {
          type: "category",
          label: "Settlement Mode",
          link: {
            type: "doc",
            id: "settlement/settlement_mode",
          },
          items: [
            "settlement/subaccount",
            "settlement/mapping_profile",
            "settlement/split_configuration",
          ],
        },
      ],
    },
    "settlement/settlement_tunnel",
    
  ],
};

export default sidebars;
