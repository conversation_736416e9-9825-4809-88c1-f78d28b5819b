import React, { FC, ReactNode } from 'react';
import Link from '@docusaurus/Link';
import styles from './Card.module.css';

interface CardProps {
  title: string;
  children: ReactNode;
  href?: string;
  icon?: ReactNode;
}

const Card: FC<CardProps> = ({ title, children, href, icon }) => {
  const content = (
    <div className={styles.card}>
      {icon && <div className={styles.cardIcon}>{icon}</div>}
      <div className={styles.cardContent}>
        <h3 className={styles.cardTitle}>{title}</h3>
        <div className={styles.cardBody}>{children}</div>
      </div>
      {href && <div className={styles.cardArrow}>→</div>}
    </div>
  );

  return href ? (
    <Link to={href} className={styles.cardLink}>
      {content}
    </Link>
  ) : (
    content
  );
};

export default Card;
