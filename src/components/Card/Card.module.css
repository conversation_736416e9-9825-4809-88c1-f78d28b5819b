.cardLink {
  text-decoration: none;
  color: inherit;
  display: block;
}

.cardLink:hover {
  text-decoration: none;
  color: inherit;
}

.card {
  background: var(--ifm-card-background-color, #fff);
  border: 1px solid var(--ifm-color-emphasis-200);
  border-radius: 8px;
  padding: 1.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.2s ease;
}

.card:hover {
  border-color: var(--ifm-color-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.cardIcon {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--ifm-color-primary);
}

.cardContent {
  flex: 1;
}

.cardTitle {
  margin: 0 0 0.75rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--ifm-heading-color);
}

.cardBody {
  color: var(--ifm-color-content);
  line-height: 1.6;
}

.cardArrow {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  font-size: 1.25rem;
  color: var(--ifm-color-primary);
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.card:hover .cardArrow {
  opacity: 1;
}

/* Dark mode support */
[data-theme='dark'] .card {
  background: var(--ifm-background-surface-color);
  border-color: var(--ifm-color-emphasis-300);
}