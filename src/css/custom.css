/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* You can override the default Infima variables here. */
:root {
  --ifm-color-primary: #817e91;
  --ifm-color-primary-dark: #322c58;
  --ifm-color-primary-darker: #2f2a53;
  --ifm-color-primary-darkest: #272245;
  --ifm-color-primary-light: #443c76;
  --ifm-color-primary-lighter: #473f7b;
  --ifm-color-primary-lightest: #504789;
  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(255, 210, 76, 0.1);
  
  /* Custom brand colors */
  --brand-purple: #3B3467;
  --brand-purple-hover: #6e6d73;
  --brand-background: #F7F8FA;
  --brand-accent: #FFD24C;
  --brand-accent-hover: #e6bd43;
  --brand-text-dark: #b1aeae;
  --brand-text-light: #666666;
  --brand-white: #aba5a5;
  
  /* Typography settings */
  --ifm-font-family-base: Verdana, Geneva, sans-serif;
  
  /* Light mode using brand colors */
  --ifm-background-color: var(--brand-background);
  --ifm-background-surface-color: var(--brand-white);
  --ifm-navbar-background-color: var(--brand-purple);
  --ifm-navbar-link-color: var(--brand-white);
  --ifm-navbar-link-hover-color: var(--brand-accent);
  --ifm-button-background-color: var(--brand-accent);
  --ifm-button-border-color: var(--brand-accent);
  --ifm-button-color: var(--brand-text-dark);
  --ifm-footer-background-color: var(--brand-purple);
  --ifm-footer-color: var(--brand-white);
  --ifm-color-content: black;
  --ifm-color-content-secondary: var(--brand-text-light);
  --ifm-menu-color: #1e1b1b;
  --ifm-menu-color-background-active: rgba(255, 210, 76, 0.1);
  --ifm-menu-color-background-hover: rgba(255, 210, 76, 0.05);
  --ifm-toc-link-color: var(--brand-text-light);
  --ifm-card-background-color: var(--brand-accent);
  --ifm-breadcrumb-color-active: var(--brand-purple);
  --ifm-link-color: var(--brand-accent);
  --ifm-link-hover-color: var(--brand-accent-hover);
  
  /* Code block improvements */
  --ifm-pre-background: #f5f5f5;
  --ifm-code-background: rgba(255, 210, 76, 0.15);
  --ifm-pre-color: var(--brand-text-dark);
  --ifm-code-color: var(--brand-purple);
}

/* Dark mode */
[data-theme='dark'] {
  --ifm-color-primary: #FFD24C;
  --ifm-color-primary-dark: #e6bd43;
  --ifm-color-primary-darker: #d9b23e;
  --ifm-color-primary-darkest: #b8962f;
  --ifm-color-primary-light: #ffe755;
  --ifm-color-primary-lighter: #ffea60;
  --ifm-color-primary-lightest: #fff080;
  --docusaurus-highlighted-code-line-bg: rgba(255, 210, 76, 0.2);
  
  /* Dark theme colors */
  --ifm-background-color: #1a1a1a;
  --ifm-background-surface-color: var(--brand-purple);
  --ifm-navbar-background-color: var(--brand-purple);
  --ifm-navbar-link-color: var(--brand-white);
  --ifm-navbar-link-hover-color: var(--brand-accent);
  --ifm-footer-background-color: var(--brand-purple);
  --ifm-menu-color: var(--brand-text-light);
  --ifm-color-content: var(--brand-white);
  --ifm-card-background-color: var(--brand-purple);
  --ifm-link-color: var(--brand-accent);
  --ifm-link-hover-color: var(--brand-accent-hover);
}

/* Apply Verdana font family globally */
* {
  font-family: Verdana, Geneva, sans-serif;
}

/* Ensure headings use correct sizes */
h1, .h1 {
  font-size: 20px !important;
}

h2, .h2 {
  font-size: 18px !important;
}

h3, .h3 {
  font-size: 16px !important;
}

h4, .h4 {
  font-size: 14px !important;
}

h5, .h5 {
  font-size: 13.5px !important;
}

/* Base text size */
body, p, div, span {
  font-size: 13px;
}
 a {
  text-decoration: none;
 }
/* Landing page gradient background */
.puzzle-gradient-bg {
  background: linear-gradient(135deg, var(--puzzle-dark-navy) 0%, var(--puzzle-purple) 50%, var(--puzzle-dark-navy) 100%);
}

/* Button styles matching landing page */
.puzzle-btn-primary {
  background: var(--puzzle-emerald);
  color: var(--puzzle-white);
  border: none;
  padding: 0.5rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  transition: all 0.2s ease;
}

.puzzle-btn-primary:hover {
  background: var(--puzzle-emerald-hover);
  color: var(--puzzle-white);
  text-decoration: none;
}

.puzzle-btn-outline {
  background: transparent;
  color: var(--puzzle-white);
  border: 1px solid var(--puzzle-white-30);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
  transition: all 0.2s ease;
}

.puzzle-btn-outline:hover {
  background: var(--puzzle-white-10);
  color: var(--puzzle-white);
  text-decoration: none;
}

/* User type buttons from landing page */
.puzzle-user-type-btn {
  background: var(--puzzle-white-10);
  border: 1px solid var(--puzzle-white-20);
  color: var(--puzzle-white);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.puzzle-user-type-btn:hover {
  background: var(--puzzle-white-20);
}

/* Navigation styles */
.puzzle-nav-link {
  color: var(--puzzle-white-80);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.puzzle-nav-link:hover {
  color: var(--puzzle-white);
  text-decoration: none;
}

/* Typography matching landing page */
.puzzle-hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--puzzle-white);
  margin-bottom: 1.5rem;
}

.puzzle-hero-subtitle {
  font-size: 1.25rem;
  color: var(--puzzle-white-80);
  font-weight: 400;
  margin: 0;
}

/* Logo styles */
.puzzle-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--puzzle-white);
}

.puzzle-logo-icon {
  font-size: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .puzzle-hero-title {
    font-size: 2.5rem;
  }
}
