import type {ReactNode} from 'react';
import useDocusaurusContext from '@docusaurus/useDocusaurusContext';
import Layout from '@theme/Layout';
import Heading from '@theme/Heading';

import styles from './index.module.css';

function HomepageHeader() {
  return (
    <header className={styles.hero}>
      <div className={styles.heroContent}>
        <Heading as="h1" className={styles.heroTitle}>
         Welcome to KollectKollect Process Documentation.
        </Heading>
        <p className={styles.heroSubtitle}>
          This documentation contains the details, code examples and process on how the appliaction was built.
        </p>
      </div>
        <div className={styles.userTypeButtons}>
          <button className={styles.userTypeBtn}><a href='/docs/overview/introduction'>Getting Started</a></button>
          <button className={styles.userTypeBtn}><a href='/docs/api/authentication'>API Reference</a></button>
        </div>
    </header>
  );
}

export default function Home(): ReactNode {
  const {siteConfig} = useDocusaurusContext();
  return (
    <Layout
      title={`${siteConfig.title}`}
      description="Your single platform for accounting and insights">
      <div className={styles.pageWrapper}>
        <HomepageHeader />
      </div>
    </Layout>
  );
}
