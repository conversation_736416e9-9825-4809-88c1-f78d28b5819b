/**
 * CSS files with the .module.css suffix will be treated as CSS modules
 * and scoped locally.
 */


.pageWrapper {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1b3a 0%, #2d1b69 50%, #1a1b3a 100%);
  color: white;
}
.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.logoIcon {
  font-size: 1.5rem;
}

.logoText {
  color: white;
}

.navCenter {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.navLink {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
}

.navLink:hover {
  color: white;
  text-decoration: none;
}

.navRight {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.loginBtn {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s;
}

.loginBtn:hover {
  background: rgba(255, 255, 255, 0.1);
  text-decoration: none;
  color: white;
}

.getStartedBtn {
  background: #00d4aa;
  color: white;
  text-decoration: none;
  padding: 0.5rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.2s;
}

.getStartedBtn:hover {
  background: #00c299;
  text-decoration: none;
  color: white;
}

.hero {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 2rem;
  min-height: calc(100vh - 100px);
  text-align: center;
  padding: 2rem;
}

.heroContent {
  max-width: 800px;
}

.userTypeButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 3rem;
}

.userTypeBtn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.userTypeBtn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  color: white;
}

.heroSubtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 400;
}

@media (max-width: 768px) {
  .navContainer {
    flex-direction: column;
    gap: 1rem;
    padding: 0 1rem;
  }

  .navCenter {
    gap: 1rem;
  }

  .heroTitle {
    font-size: 2.5rem;
  }

  .userTypeButtons {
    flex-direction: column;
    align-items: center;
  }
}
